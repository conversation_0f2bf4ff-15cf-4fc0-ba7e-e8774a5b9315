import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON>x<PERSON>,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class ActivityLoggerInterceptor implements NestInterceptor {
  constructor(private prisma: PrismaService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const method = request.method;
    const url = request.url;

    // Extract application ID from URL if present
    const applicationId = this.extractApplicationId(url);

    return next.handle().pipe(
      tap(async () => {
        if (applicationId && user) {
          // Determine action type based on method and URL
          const action = this.determineAction(method, url);

          // Create activity log
          await this.prisma.activityLog.create({
            data: {
              mouApplicationId: applicationId,
              action,
              details: {
                userId: user.id,
                method,
                url,
                userRole: user.baseRole,
                reviewerRole: user.reviewerRole,
                approverRole: user.approverRole
              },
              category: this.determineCategory(url),
              importance: this.determineImportance(method, url),
              ipAddress: request.ip,
              userAgent: request.headers['user-agent']
            }
          });
        }
      })
    );
  }

  private extractApplicationId(url: string): number | null {
    const match = url.match(/\/mou-applications\/(\d+)/);
    return match ? parseInt(match[1]) : null;
  }

  private determineAction(method: string, url: string): string {
    if (url.includes('/review')) {
      return 'REVIEW_ADDED';
    }
    if (url.includes('/assign-expert')) {
      return 'EXPERT_ASSIGNED';
    }
    if (url.includes('/request-modification')) {
      return 'MODIFICATION_REQUESTED';
    }
    if (url.includes('/approve')) {
      return 'APPLICATION_APPROVED';
    }

    switch (method) {
      case 'POST':
        return 'CREATE';
      case 'PUT':
        return 'UPDATE';
      case 'DELETE':
        return 'DELETE';
      default:
        return 'VIEW';
    }
  }

  private determineCategory(url: string): string {
    if (url.includes('/review')) {
      return 'REVIEW';
    }
    if (url.includes('/assign-expert')) {
      return 'ASSIGNMENT';
    }
    if (url.includes('/request-modification')) {
      return 'MODIFICATION';
    }
    if (url.includes('/approve')) {
      return 'APPROVAL';
    }
    return 'GENERAL';
  }

  private determineImportance(method: string, url: string): string {
    if (url.includes('/approve') || url.includes('/review')) {
      return 'HIGH';
    }
    if (method === 'POST' || method === 'PUT') {
      return 'MEDIUM';
    }
    return 'INFO';
  }
}