"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-roving-focu_2dc2a383afe5e5f0b9ee6d67dee2b7ba";
exports.ids = ["vendor-chunks/@radix-ui+react-roving-focu_2dc2a383afe5e5f0b9ee6d67dee2b7ba"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-roving-focu_2dc2a383afe5e5f0b9ee6d67dee2b7ba/node_modules/@radix-ui/react-roving-focus/dist/index.mjs":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-roving-focu_2dc2a383afe5e5f0b9ee6d67dee2b7ba/node_modules/@radix-ui/react-roving-focus/dist/index.mjs ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   RovingFocusGroup: () => (/* binding */ RovingFocusGroup),\n/* harmony export */   RovingFocusGroupItem: () => (/* binding */ RovingFocusGroupItem),\n/* harmony export */   createRovingFocusGroupScope: () => (/* binding */ createRovingFocusGroupScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@_ea112ab680817fde40756284ba23daf4/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_c325a527ee623bb35f115eb421b60f39/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_003328fe74c3632a3c9c5ce511d98a5d/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@19.1.6_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_e3803c74fa3732ab7d036a7d08888245/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_b63c24d6f500999480e301f87263de09/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_07f3fde21329b0c35912c2be5d860183/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_dc6dca59c085a7048aa63aa5d839028d/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Item,Root,RovingFocusGroup,RovingFocusGroupItem,createRovingFocusGroupScope auto */ // packages/react/roving-focus/src/RovingFocusGroup.tsx\n\n\n\n\n\n\n\n\n\n\n\nvar ENTRY_FOCUS = \"rovingFocusGroup.onEntryFocus\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar GROUP_NAME = \"RovingFocusGroup\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(GROUP_NAME);\nvar [createRovingFocusGroupContext, createRovingFocusGroupScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(GROUP_NAME, [\n    createCollectionScope\n]);\nvar [RovingFocusProvider, useRovingFocusContext] = createRovingFocusGroupContext(GROUP_NAME);\nvar RovingFocusGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeRovingFocusGroup,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n            scope: props.__scopeRovingFocusGroup,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RovingFocusGroupImpl, {\n                ...props,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nRovingFocusGroup.displayName = GROUP_NAME;\nvar RovingFocusGroupImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRovingFocusGroup, orientation, loop = false, dir, currentTabStopId: currentTabStopIdProp, defaultCurrentTabStopId, onCurrentTabStopIdChange, onEntryFocus, preventScrollOnEntryFocus = false, ...groupProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_5__.useDirection)(dir);\n    const [currentTabStopId = null, setCurrentTabStopId] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_6__.useControllableState)({\n        prop: currentTabStopIdProp,\n        defaultProp: defaultCurrentTabStopId,\n        onChange: onCurrentTabStopIdChange\n    });\n    const [isTabbingBackOut, setIsTabbingBackOut] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const handleEntryFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_7__.useCallbackRef)(onEntryFocus);\n    const getItems = useCollection(__scopeRovingFocusGroup);\n    const isClickFocusRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const [focusableItemsCount, setFocusableItemsCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"RovingFocusGroupImpl.useEffect\": ()=>{\n            const node = ref.current;\n            if (node) {\n                node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n                return ({\n                    \"RovingFocusGroupImpl.useEffect\": ()=>node.removeEventListener(ENTRY_FOCUS, handleEntryFocus)\n                })[\"RovingFocusGroupImpl.useEffect\"];\n            }\n        }\n    }[\"RovingFocusGroupImpl.useEffect\"], [\n        handleEntryFocus\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RovingFocusProvider, {\n        scope: __scopeRovingFocusGroup,\n        orientation,\n        dir: direction,\n        loop,\n        currentTabStopId,\n        onItemFocus: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": (tabStopId)=>setCurrentTabStopId(tabStopId)\n        }[\"RovingFocusGroupImpl.useCallback\"], [\n            setCurrentTabStopId\n        ]),\n        onItemShiftTab: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": ()=>setIsTabbingBackOut(true)\n        }[\"RovingFocusGroupImpl.useCallback\"], []),\n        onFocusableItemAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": ()=>setFocusableItemsCount({\n                    \"RovingFocusGroupImpl.useCallback\": (prevCount)=>prevCount + 1\n                }[\"RovingFocusGroupImpl.useCallback\"])\n        }[\"RovingFocusGroupImpl.useCallback\"], []),\n        onFocusableItemRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"RovingFocusGroupImpl.useCallback\": ()=>setFocusableItemsCount({\n                    \"RovingFocusGroupImpl.useCallback\": (prevCount)=>prevCount - 1\n                }[\"RovingFocusGroupImpl.useCallback\"])\n        }[\"RovingFocusGroupImpl.useCallback\"], []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div, {\n            tabIndex: isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0,\n            \"data-orientation\": orientation,\n            ...groupProps,\n            ref: composedRefs,\n            style: {\n                outline: \"none\",\n                ...props.style\n            },\n            onMouseDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onMouseDown, ()=>{\n                isClickFocusRef.current = true;\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onFocus, (event)=>{\n                const isKeyboardFocus = !isClickFocusRef.current;\n                if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n                    const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n                    event.currentTarget.dispatchEvent(entryFocusEvent);\n                    if (!entryFocusEvent.defaultPrevented) {\n                        const items = getItems().filter((item)=>item.focusable);\n                        const activeItem = items.find((item)=>item.active);\n                        const currentItem = items.find((item)=>item.id === currentTabStopId);\n                        const candidateItems = [\n                            activeItem,\n                            currentItem,\n                            ...items\n                        ].filter(Boolean);\n                        const candidateNodes = candidateItems.map((item)=>item.ref.current);\n                        focusFirst(candidateNodes, preventScrollOnEntryFocus);\n                    }\n                }\n                isClickFocusRef.current = false;\n            }),\n            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onBlur, ()=>setIsTabbingBackOut(false))\n        })\n    });\n});\nvar ITEM_NAME = \"RovingFocusGroupItem\";\nvar RovingFocusGroupItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeRovingFocusGroup, focusable = true, active = false, tabStopId, ...itemProps } = props;\n    const autoId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n    const { onFocusableItemAdd, onFocusableItemRemove } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"RovingFocusGroupItem.useEffect\": ()=>{\n            if (focusable) {\n                onFocusableItemAdd();\n                return ({\n                    \"RovingFocusGroupItem.useEffect\": ()=>onFocusableItemRemove()\n                })[\"RovingFocusGroupItem.useEffect\"];\n            }\n        }\n    }[\"RovingFocusGroupItem.useEffect\"], [\n        focusable,\n        onFocusableItemAdd,\n        onFocusableItemRemove\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n        scope: __scopeRovingFocusGroup,\n        id,\n        focusable,\n        active,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.span, {\n            tabIndex: isCurrentTabStop ? 0 : -1,\n            \"data-orientation\": context.orientation,\n            ...itemProps,\n            ref: forwardedRef,\n            onMouseDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onMouseDown, (event)=>{\n                if (!focusable) event.preventDefault();\n                else context.onItemFocus(id);\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onFocus, ()=>context.onItemFocus(id)),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                if (event.key === \"Tab\" && event.shiftKey) {\n                    context.onItemShiftTab();\n                    return;\n                }\n                if (event.target !== event.currentTarget) return;\n                const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n                if (focusIntent !== void 0) {\n                    if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n                    event.preventDefault();\n                    const items = getItems().filter((item)=>item.focusable);\n                    let candidateNodes = items.map((item)=>item.ref.current);\n                    if (focusIntent === \"last\") candidateNodes.reverse();\n                    else if (focusIntent === \"prev\" || focusIntent === \"next\") {\n                        if (focusIntent === \"prev\") candidateNodes.reverse();\n                        const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                        candidateNodes = context.loop ? wrapArray(candidateNodes, currentIndex + 1) : candidateNodes.slice(currentIndex + 1);\n                    }\n                    setTimeout(()=>focusFirst(candidateNodes));\n                }\n            })\n        })\n    });\n});\nRovingFocusGroupItem.displayName = ITEM_NAME;\nvar MAP_KEY_TO_FOCUS_INTENT = {\n    ArrowLeft: \"prev\",\n    ArrowUp: \"prev\",\n    ArrowRight: \"next\",\n    ArrowDown: \"next\",\n    PageUp: \"first\",\n    Home: \"first\",\n    PageDown: \"last\",\n    End: \"last\"\n};\nfunction getDirectionAwareKey(key, dir) {\n    if (dir !== \"rtl\") return key;\n    return key === \"ArrowLeft\" ? \"ArrowRight\" : key === \"ArrowRight\" ? \"ArrowLeft\" : key;\n}\nfunction getFocusIntent(event, orientation, dir) {\n    const key = getDirectionAwareKey(event.key, dir);\n    if (orientation === \"vertical\" && [\n        \"ArrowLeft\",\n        \"ArrowRight\"\n    ].includes(key)) return void 0;\n    if (orientation === \"horizontal\" && [\n        \"ArrowUp\",\n        \"ArrowDown\"\n    ].includes(key)) return void 0;\n    return MAP_KEY_TO_FOCUS_INTENT[key];\n}\nfunction focusFirst(candidates, preventScroll = false) {\n    const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n    for (const candidate of candidates){\n        if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n        candidate.focus({\n            preventScroll\n        });\n        if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n    }\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nvar Root = RovingFocusGroup;\nvar Item = RovingFocusGroupItem;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJhZGl4LXVpK3JlYWN0LXJvdmluZy1mb2N1XzJkYzJhMzgzYWZlNWU1ZjBiOWVlNmQ2N2RlZTJiN2JhL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3Qtcm92aW5nLWZvY3VzL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXVCO0FBQ2M7QUFDSjtBQUNEO0FBQ0c7QUFDYjtBQUNJO0FBQ0s7QUFDTTtBQUNSO0FBZ0VuQjtBQTVEVixJQUFNLGNBQWM7QUFDcEIsSUFBTSxnQkFBZ0I7SUFBRSxTQUFTO0lBQU8sWUFBWTtBQUFLO0FBTXpELElBQU0sYUFBYTtBQUduQixJQUFNLENBQUMsWUFBWSxlQUFlLHFCQUFxQixJQUFJLDRFQUFnQixDQUd6RSxVQUFVO0FBR1osSUFBTSxDQUFDLCtCQUErQiwyQkFBMkIsSUFBSSwyRUFBa0IsQ0FDckYsWUFDQTtJQUFDLHFCQUFxQjtDQUFBO0FBK0J4QixJQUFNLENBQUMscUJBQXFCLHFCQUFxQixJQUMvQyw4QkFBa0QsVUFBVTtBQUs5RCxJQUFNLGlDQUF5Qiw4Q0FDN0IsQ0FBQyxPQUEyQztJQUMxQyxPQUNFLHVFQUFDLFdBQVcsVUFBWDtRQUFvQixPQUFPLE1BQU07UUFDaEMsaUZBQUMsV0FBVyxNQUFYO1lBQWdCLE9BQU8sTUFBTTtZQUM1QixpRkFBQztnQkFBc0IsR0FBRztnQkFBTyxLQUFLO1lBQUEsQ0FBYztRQUFBLENBQ3REO0lBQUEsQ0FDRjtBQUVKO0FBR0YsaUJBQWlCLGNBQWM7QUFnQi9CLElBQU0scUNBQTZCLDhDQUdqQyxDQUFDLE9BQStDO0lBQ2hELE1BQU0sRUFDSix5QkFDQSxhQUNBLE9BQU8sT0FDUCxLQUNBLGtCQUFrQixzQkFDbEIseUJBQ0EsMEJBQ0EsY0FDQSw0QkFBNEIsT0FDNUIsR0FBRyxZQUNMLEdBQUk7SUFDSixNQUFNLE1BQVksMENBQW9DLElBQUk7SUFDMUQsTUFBTSxlQUFlLDZFQUFlLENBQUMsY0FBYyxHQUFHO0lBQ3RELE1BQU0sWUFBWSx1RUFBWSxDQUFDLEdBQUc7SUFDbEMsTUFBTSxDQUFDLG1CQUFtQixNQUFNLG1CQUFtQixJQUFJLDRGQUFvQixDQUFDO1FBQzFFLE1BQU07UUFDTixhQUFhO1FBQ2IsVUFBVTtJQUNaLENBQUM7SUFDRCxNQUFNLENBQUMsa0JBQWtCLG1CQUFtQixJQUFVLDRDQUFTLEtBQUs7SUFDcEUsTUFBTSxtQkFBbUIsZ0ZBQWMsQ0FBQyxZQUFZO0lBQ3BELE1BQU0sV0FBVyxjQUFjLHVCQUF1QjtJQUN0RCxNQUFNLGtCQUF3QiwwQ0FBTyxLQUFLO0lBQzFDLE1BQU0sQ0FBQyxxQkFBcUIsc0JBQXNCLElBQVUsNENBQVMsQ0FBQztJQUVoRTswQ0FBVTtZQUNkLE1BQU0sT0FBTyxJQUFJO1lBQ2pCLElBQUksTUFBTTtnQkFDUixLQUFLLGlCQUFpQixhQUFhLGdCQUFnQjtnQkFDbkQ7c0RBQU8sSUFBTSxLQUFLLG9CQUFvQixhQUFhLGdCQUFnQjs7WUFDckU7UUFDRjt5Q0FBRztRQUFDLGdCQUFnQjtLQUFDO0lBRXJCLE9BQ0UsdUVBQUM7UUFDQyxPQUFPO1FBQ1A7UUFDQSxLQUFLO1FBQ0w7UUFDQTtRQUNBLGFBQW1CO2dEQUNqQixDQUFDLFlBQWMsb0JBQW9CLFNBQVM7K0NBQzVDO1lBQUMsbUJBQW1CO1NBQUE7UUFFdEIsZ0JBQXNCO2dEQUFZLElBQU0sb0JBQW9CLElBQUk7K0NBQUcsQ0FBQyxDQUFDO1FBQ3JFLG9CQUEwQjtnREFDeEIsSUFBTTt3REFBdUIsQ0FBQyxZQUFjLFlBQVksQ0FBQzs7K0NBQ3pELENBQUM7UUFFSCx1QkFBNkI7Z0RBQzNCLElBQU07d0RBQXVCLENBQUMsWUFBYyxZQUFZLENBQUM7OytDQUN6RCxDQUFDO1FBR0gsaUZBQUMsZ0VBQVMsQ0FBQyxLQUFWO1lBQ0MsVUFBVSxvQkFBb0Isd0JBQXdCLElBQUksS0FBSztZQUMvRCxvQkFBa0I7WUFDakIsR0FBRztZQUNKLEtBQUs7WUFDTCxPQUFPO2dCQUFFLFNBQVM7Z0JBQVEsR0FBRyxNQUFNO1lBQU07WUFDekMsYUFBYSx5RUFBb0IsQ0FBQyxNQUFNLGFBQWE7Z0JBQ25ELGdCQUFnQixVQUFVO1lBQzVCLENBQUM7WUFDRCxTQUFTLHlFQUFvQixDQUFDLE1BQU0sU0FBUyxDQUFDO2dCQUs1QyxNQUFNLGtCQUFrQixDQUFDLGdCQUFnQjtnQkFFekMsSUFBSSxNQUFNLFdBQVcsTUFBTSxpQkFBaUIsbUJBQW1CLENBQUMsa0JBQWtCO29CQUNoRixNQUFNLGtCQUFrQixJQUFJLFlBQVksYUFBYSxhQUFhO29CQUNsRSxNQUFNLGNBQWMsY0FBYyxlQUFlO29CQUVqRCxJQUFJLENBQUMsZ0JBQWdCLGtCQUFrQjt3QkFDckMsTUFBTSxRQUFRLFNBQVMsRUFBRSxPQUFPLENBQUMsT0FBUyxLQUFLLFNBQVM7d0JBQ3hELE1BQU0sYUFBYSxNQUFNLEtBQUssQ0FBQyxPQUFTLEtBQUssTUFBTTt3QkFDbkQsTUFBTSxjQUFjLE1BQU0sS0FBSyxDQUFDLE9BQVMsS0FBSyxPQUFPLGdCQUFnQjt3QkFDckUsTUFBTSxpQkFBaUI7NEJBQUM7NEJBQVk7K0JBQWdCLEtBQUs7eUJBQUEsQ0FBRSxPQUN6RDt3QkFFRixNQUFNLGlCQUFpQixlQUFlLElBQUksQ0FBQyxPQUFTLEtBQUssSUFBSSxPQUFRO3dCQUNyRSxXQUFXLGdCQUFnQix5QkFBeUI7b0JBQ3REO2dCQUNGO2dCQUVBLGdCQUFnQixVQUFVO1lBQzVCLENBQUM7WUFDRCxRQUFRLHlFQUFvQixDQUFDLE1BQU0sUUFBUSxJQUFNLG9CQUFvQixLQUFLLENBQUM7UUFBQTtJQUM3RTtBQUdOLENBQUM7QUFNRCxJQUFNLFlBQVk7QUFVbEIsSUFBTSxxQ0FBNkIsOENBQ2pDLENBQUMsT0FBMEM7SUFDekMsTUFBTSxFQUNKLHlCQUNBLFlBQVksTUFDWixTQUFTLE9BQ1QsV0FDQSxHQUFHLFdBQ0wsR0FBSTtJQUNKLE1BQU0sU0FBUywwREFBSyxDQUFDO0lBQ3JCLE1BQU0sS0FBSyxhQUFhO0lBQ3hCLE1BQU0sVUFBVSxzQkFBc0IsV0FBVyx1QkFBdUI7SUFDeEUsTUFBTSxtQkFBbUIsUUFBUSxxQkFBcUI7SUFDdEQsTUFBTSxXQUFXLGNBQWMsdUJBQXVCO0lBRXRELE1BQU0sRUFBRSxvQkFBb0Isc0JBQXNCLElBQUk7SUFFaEQ7MENBQVU7WUFDZCxJQUFJLFdBQVc7Z0JBQ2IsbUJBQW1CO2dCQUNuQjtzREFBTyxJQUFNLHNCQUFzQjs7WUFDckM7UUFDRjt5Q0FBRztRQUFDO1FBQVc7UUFBb0IscUJBQXFCO0tBQUM7SUFFekQsT0FDRSx1RUFBQyxXQUFXLFVBQVg7UUFDQyxPQUFPO1FBQ1A7UUFDQTtRQUNBO1FBRUEsaUZBQUMsZ0VBQVMsQ0FBQyxNQUFWO1lBQ0MsVUFBVSxtQkFBbUIsSUFBSTtZQUNqQyxvQkFBa0IsUUFBUTtZQUN6QixHQUFHO1lBQ0osS0FBSztZQUNMLGFBQWEseUVBQW9CLENBQUMsTUFBTSxhQUFhLENBQUM7Z0JBR3BELElBQUksQ0FBQyxVQUFXLE9BQU0sZUFBZTtxQkFFaEMsUUFBUSxZQUFZLEVBQUU7WUFDN0IsQ0FBQztZQUNELFNBQVMseUVBQW9CLENBQUMsTUFBTSxTQUFTLElBQU0sUUFBUSxZQUFZLEVBQUUsQ0FBQztZQUMxRSxXQUFXLHlFQUFvQixDQUFDLE1BQU0sV0FBVyxDQUFDO2dCQUNoRCxJQUFJLE1BQU0sUUFBUSxTQUFTLE1BQU0sVUFBVTtvQkFDekMsUUFBUSxlQUFlO29CQUN2QjtnQkFDRjtnQkFFQSxJQUFJLE1BQU0sV0FBVyxNQUFNLGNBQWU7Z0JBRTFDLE1BQU0sY0FBYyxlQUFlLE9BQU8sUUFBUSxhQUFhLFFBQVEsR0FBRztnQkFFMUUsSUFBSSxnQkFBZ0IsUUFBVztvQkFDN0IsSUFBSSxNQUFNLFdBQVcsTUFBTSxXQUFXLE1BQU0sVUFBVSxNQUFNLFNBQVU7b0JBQ3RFLE1BQU0sZUFBZTtvQkFDckIsTUFBTSxRQUFRLFNBQVMsRUFBRSxPQUFPLENBQUMsT0FBUyxLQUFLLFNBQVM7b0JBQ3hELElBQUksaUJBQWlCLE1BQU0sSUFBSSxDQUFDLE9BQVMsS0FBSyxJQUFJLE9BQVE7b0JBRTFELElBQUksZ0JBQWdCLE9BQVEsZ0JBQWUsUUFBUTt5QkFBQSxJQUMxQyxnQkFBZ0IsVUFBVSxnQkFBZ0IsUUFBUTt3QkFDekQsSUFBSSxnQkFBZ0IsT0FBUSxnQkFBZSxRQUFRO3dCQUNuRCxNQUFNLGVBQWUsZUFBZSxRQUFRLE1BQU0sYUFBYTt3QkFDL0QsaUJBQWlCLFFBQVEsT0FDckIsVUFBVSxnQkFBZ0IsZUFBZSxDQUFDLElBQzFDLGVBQWUsTUFBTSxlQUFlLENBQUM7b0JBQzNDO29CQU1BLFdBQVcsSUFBTSxXQUFXLGNBQWMsQ0FBQztnQkFDN0M7WUFDRixDQUFDO1FBQUE7SUFDSDtBQUdOO0FBR0YscUJBQXFCLGNBQWM7QUFLbkMsSUFBTSwwQkFBdUQ7SUFDM0QsV0FBVztJQUFRLFNBQVM7SUFDNUIsWUFBWTtJQUFRLFdBQVc7SUFDL0IsUUFBUTtJQUFTLE1BQU07SUFDdkIsVUFBVTtJQUFRLEtBQUs7QUFDekI7QUFFQSxTQUFTLHFCQUFxQixLQUFhLEtBQWlCO0lBQzFELElBQUksUUFBUSxNQUFPLFFBQU87SUFDMUIsT0FBTyxRQUFRLGNBQWMsZUFBZSxRQUFRLGVBQWUsY0FBYztBQUNuRjtBQUlBLFNBQVMsZUFBZSxPQUE0QixhQUEyQixLQUFpQjtJQUM5RixNQUFNLE1BQU0scUJBQXFCLE1BQU0sS0FBSyxHQUFHO0lBQy9DLElBQUksZ0JBQWdCLGNBQWM7UUFBQztRQUFhLFlBQVk7S0FBQSxDQUFFLFNBQVMsR0FBRyxFQUFHLFFBQU87SUFDcEYsSUFBSSxnQkFBZ0IsZ0JBQWdCO1FBQUM7UUFBVyxXQUFXO0tBQUEsQ0FBRSxTQUFTLEdBQUcsRUFBRyxRQUFPO0lBQ25GLE9BQU8sd0JBQXdCLEdBQUc7QUFDcEM7QUFFQSxTQUFTLFdBQVcsWUFBMkIsZ0JBQWdCLE9BQU87SUFDcEUsTUFBTSw2QkFBNkIsU0FBUztJQUM1QyxXQUFXLGFBQWEsV0FBWTtRQUVsQyxJQUFJLGNBQWMsMkJBQTRCO1FBQzlDLFVBQVUsTUFBTTtZQUFFO1FBQWMsQ0FBQztRQUNqQyxJQUFJLFNBQVMsa0JBQWtCLDJCQUE0QjtJQUM3RDtBQUNGO0FBTUEsU0FBUyxVQUFhLE9BQVksWUFBb0I7SUFDcEQsT0FBTyxNQUFNLElBQUksQ0FBQyxHQUFHLFFBQVUsT0FBTyxhQUFhLFNBQVMsTUFBTSxNQUFNLENBQUM7QUFDM0U7QUFFQSxJQUFNLE9BQU87QUFDYixJQUFNLE9BQU8iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTEVOT1ZPXFxEZXNrdG9wXFxNb1UgQXBwbGljYXRpb25femlwXFxNb1UgQXBwbGljYXRpb25cXHNyY1xcUm92aW5nRm9jdXNHcm91cC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY29tcG9zZUV2ZW50SGFuZGxlcnMgfSBmcm9tICdAcmFkaXgtdWkvcHJpbWl0aXZlJztcbmltcG9ydCB7IGNyZWF0ZUNvbGxlY3Rpb24gfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtY29sbGVjdGlvbic7XG5pbXBvcnQgeyB1c2VDb21wb3NlZFJlZnMgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtY29tcG9zZS1yZWZzJztcbmltcG9ydCB7IGNyZWF0ZUNvbnRleHRTY29wZSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1jb250ZXh0JztcbmltcG9ydCB7IHVzZUlkIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWlkJztcbmltcG9ydCB7IFByaW1pdGl2ZSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1wcmltaXRpdmUnO1xuaW1wb3J0IHsgdXNlQ2FsbGJhY2tSZWYgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZic7XG5pbXBvcnQgeyB1c2VDb250cm9sbGFibGVTdGF0ZSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC11c2UtY29udHJvbGxhYmxlLXN0YXRlJztcbmltcG9ydCB7IHVzZURpcmVjdGlvbiB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1kaXJlY3Rpb24nO1xuXG5pbXBvcnQgdHlwZSB7IFNjb3BlIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWNvbnRleHQnO1xuXG5jb25zdCBFTlRSWV9GT0NVUyA9ICdyb3ZpbmdGb2N1c0dyb3VwLm9uRW50cnlGb2N1cyc7XG5jb25zdCBFVkVOVF9PUFRJT05TID0geyBidWJibGVzOiBmYWxzZSwgY2FuY2VsYWJsZTogdHJ1ZSB9O1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBSb3ZpbmdGb2N1c0dyb3VwXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IEdST1VQX05BTUUgPSAnUm92aW5nRm9jdXNHcm91cCc7XG5cbnR5cGUgSXRlbURhdGEgPSB7IGlkOiBzdHJpbmc7IGZvY3VzYWJsZTogYm9vbGVhbjsgYWN0aXZlOiBib29sZWFuIH07XG5jb25zdCBbQ29sbGVjdGlvbiwgdXNlQ29sbGVjdGlvbiwgY3JlYXRlQ29sbGVjdGlvblNjb3BlXSA9IGNyZWF0ZUNvbGxlY3Rpb248XG4gIEhUTUxTcGFuRWxlbWVudCxcbiAgSXRlbURhdGFcbj4oR1JPVVBfTkFNRSk7XG5cbnR5cGUgU2NvcGVkUHJvcHM8UD4gPSBQICYgeyBfX3Njb3BlUm92aW5nRm9jdXNHcm91cD86IFNjb3BlIH07XG5jb25zdCBbY3JlYXRlUm92aW5nRm9jdXNHcm91cENvbnRleHQsIGNyZWF0ZVJvdmluZ0ZvY3VzR3JvdXBTY29wZV0gPSBjcmVhdGVDb250ZXh0U2NvcGUoXG4gIEdST1VQX05BTUUsXG4gIFtjcmVhdGVDb2xsZWN0aW9uU2NvcGVdXG4pO1xuXG50eXBlIE9yaWVudGF0aW9uID0gUmVhY3QuQXJpYUF0dHJpYnV0ZXNbJ2FyaWEtb3JpZW50YXRpb24nXTtcbnR5cGUgRGlyZWN0aW9uID0gJ2x0cicgfCAncnRsJztcblxuaW50ZXJmYWNlIFJvdmluZ0ZvY3VzR3JvdXBPcHRpb25zIHtcbiAgLyoqXG4gICAqIFRoZSBvcmllbnRhdGlvbiBvZiB0aGUgZ3JvdXAuXG4gICAqIE1haW5seSBzbyBhcnJvdyBuYXZpZ2F0aW9uIGlzIGRvbmUgYWNjb3JkaW5nbHkgKGxlZnQgJiByaWdodCB2cy4gdXAgJiBkb3duKVxuICAgKi9cbiAgb3JpZW50YXRpb24/OiBPcmllbnRhdGlvbjtcbiAgLyoqXG4gICAqIFRoZSBkaXJlY3Rpb24gb2YgbmF2aWdhdGlvbiBiZXR3ZWVuIGl0ZW1zLlxuICAgKi9cbiAgZGlyPzogRGlyZWN0aW9uO1xuICAvKipcbiAgICogV2hldGhlciBrZXlib2FyZCBuYXZpZ2F0aW9uIHNob3VsZCBsb29wIGFyb3VuZFxuICAgKiBAZGVmYXVsdFZhbHVlIGZhbHNlXG4gICAqL1xuICBsb29wPzogYm9vbGVhbjtcbn1cblxudHlwZSBSb3ZpbmdDb250ZXh0VmFsdWUgPSBSb3ZpbmdGb2N1c0dyb3VwT3B0aW9ucyAmIHtcbiAgY3VycmVudFRhYlN0b3BJZDogc3RyaW5nIHwgbnVsbDtcbiAgb25JdGVtRm9jdXModGFiU3RvcElkOiBzdHJpbmcpOiB2b2lkO1xuICBvbkl0ZW1TaGlmdFRhYigpOiB2b2lkO1xuICBvbkZvY3VzYWJsZUl0ZW1BZGQoKTogdm9pZDtcbiAgb25Gb2N1c2FibGVJdGVtUmVtb3ZlKCk6IHZvaWQ7XG59O1xuXG5jb25zdCBbUm92aW5nRm9jdXNQcm92aWRlciwgdXNlUm92aW5nRm9jdXNDb250ZXh0XSA9XG4gIGNyZWF0ZVJvdmluZ0ZvY3VzR3JvdXBDb250ZXh0PFJvdmluZ0NvbnRleHRWYWx1ZT4oR1JPVVBfTkFNRSk7XG5cbnR5cGUgUm92aW5nRm9jdXNHcm91cEVsZW1lbnQgPSBSb3ZpbmdGb2N1c0dyb3VwSW1wbEVsZW1lbnQ7XG5pbnRlcmZhY2UgUm92aW5nRm9jdXNHcm91cFByb3BzIGV4dGVuZHMgUm92aW5nRm9jdXNHcm91cEltcGxQcm9wcyB7fVxuXG5jb25zdCBSb3ZpbmdGb2N1c0dyb3VwID0gUmVhY3QuZm9yd2FyZFJlZjxSb3ZpbmdGb2N1c0dyb3VwRWxlbWVudCwgUm92aW5nRm9jdXNHcm91cFByb3BzPihcbiAgKHByb3BzOiBTY29wZWRQcm9wczxSb3ZpbmdGb2N1c0dyb3VwUHJvcHM+LCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPENvbGxlY3Rpb24uUHJvdmlkZXIgc2NvcGU9e3Byb3BzLl9fc2NvcGVSb3ZpbmdGb2N1c0dyb3VwfT5cbiAgICAgICAgPENvbGxlY3Rpb24uU2xvdCBzY29wZT17cHJvcHMuX19zY29wZVJvdmluZ0ZvY3VzR3JvdXB9PlxuICAgICAgICAgIDxSb3ZpbmdGb2N1c0dyb3VwSW1wbCB7Li4ucHJvcHN9IHJlZj17Zm9yd2FyZGVkUmVmfSAvPlxuICAgICAgICA8L0NvbGxlY3Rpb24uU2xvdD5cbiAgICAgIDwvQ29sbGVjdGlvbi5Qcm92aWRlcj5cbiAgICApO1xuICB9XG4pO1xuXG5Sb3ZpbmdGb2N1c0dyb3VwLmRpc3BsYXlOYW1lID0gR1JPVVBfTkFNRTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG50eXBlIFJvdmluZ0ZvY3VzR3JvdXBJbXBsRWxlbWVudCA9IFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xudHlwZSBQcmltaXRpdmVEaXZQcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgUHJpbWl0aXZlLmRpdj47XG5pbnRlcmZhY2UgUm92aW5nRm9jdXNHcm91cEltcGxQcm9wc1xuICBleHRlbmRzIE9taXQ8UHJpbWl0aXZlRGl2UHJvcHMsICdkaXInPixcbiAgICBSb3ZpbmdGb2N1c0dyb3VwT3B0aW9ucyB7XG4gIGN1cnJlbnRUYWJTdG9wSWQ/OiBzdHJpbmcgfCBudWxsO1xuICBkZWZhdWx0Q3VycmVudFRhYlN0b3BJZD86IHN0cmluZztcbiAgb25DdXJyZW50VGFiU3RvcElkQ2hhbmdlPzogKHRhYlN0b3BJZDogc3RyaW5nIHwgbnVsbCkgPT4gdm9pZDtcbiAgb25FbnRyeUZvY3VzPzogKGV2ZW50OiBFdmVudCkgPT4gdm9pZDtcbiAgcHJldmVudFNjcm9sbE9uRW50cnlGb2N1cz86IGJvb2xlYW47XG59XG5cbmNvbnN0IFJvdmluZ0ZvY3VzR3JvdXBJbXBsID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUm92aW5nRm9jdXNHcm91cEltcGxFbGVtZW50LFxuICBSb3ZpbmdGb2N1c0dyb3VwSW1wbFByb3BzXG4+KChwcm9wczogU2NvcGVkUHJvcHM8Um92aW5nRm9jdXNHcm91cEltcGxQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICBjb25zdCB7XG4gICAgX19zY29wZVJvdmluZ0ZvY3VzR3JvdXAsXG4gICAgb3JpZW50YXRpb24sXG4gICAgbG9vcCA9IGZhbHNlLFxuICAgIGRpcixcbiAgICBjdXJyZW50VGFiU3RvcElkOiBjdXJyZW50VGFiU3RvcElkUHJvcCxcbiAgICBkZWZhdWx0Q3VycmVudFRhYlN0b3BJZCxcbiAgICBvbkN1cnJlbnRUYWJTdG9wSWRDaGFuZ2UsXG4gICAgb25FbnRyeUZvY3VzLFxuICAgIHByZXZlbnRTY3JvbGxPbkVudHJ5Rm9jdXMgPSBmYWxzZSxcbiAgICAuLi5ncm91cFByb3BzXG4gIH0gPSBwcm9wcztcbiAgY29uc3QgcmVmID0gUmVhY3QudXNlUmVmPFJvdmluZ0ZvY3VzR3JvdXBJbXBsRWxlbWVudD4obnVsbCk7XG4gIGNvbnN0IGNvbXBvc2VkUmVmcyA9IHVzZUNvbXBvc2VkUmVmcyhmb3J3YXJkZWRSZWYsIHJlZik7XG4gIGNvbnN0IGRpcmVjdGlvbiA9IHVzZURpcmVjdGlvbihkaXIpO1xuICBjb25zdCBbY3VycmVudFRhYlN0b3BJZCA9IG51bGwsIHNldEN1cnJlbnRUYWJTdG9wSWRdID0gdXNlQ29udHJvbGxhYmxlU3RhdGUoe1xuICAgIHByb3A6IGN1cnJlbnRUYWJTdG9wSWRQcm9wLFxuICAgIGRlZmF1bHRQcm9wOiBkZWZhdWx0Q3VycmVudFRhYlN0b3BJZCxcbiAgICBvbkNoYW5nZTogb25DdXJyZW50VGFiU3RvcElkQ2hhbmdlLFxuICB9KTtcbiAgY29uc3QgW2lzVGFiYmluZ0JhY2tPdXQsIHNldElzVGFiYmluZ0JhY2tPdXRdID0gUmVhY3QudXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBoYW5kbGVFbnRyeUZvY3VzID0gdXNlQ2FsbGJhY2tSZWYob25FbnRyeUZvY3VzKTtcbiAgY29uc3QgZ2V0SXRlbXMgPSB1c2VDb2xsZWN0aW9uKF9fc2NvcGVSb3ZpbmdGb2N1c0dyb3VwKTtcbiAgY29uc3QgaXNDbGlja0ZvY3VzUmVmID0gUmVhY3QudXNlUmVmKGZhbHNlKTtcbiAgY29uc3QgW2ZvY3VzYWJsZUl0ZW1zQ291bnQsIHNldEZvY3VzYWJsZUl0ZW1zQ291bnRdID0gUmVhY3QudXNlU3RhdGUoMCk7XG5cbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBub2RlID0gcmVmLmN1cnJlbnQ7XG4gICAgaWYgKG5vZGUpIHtcbiAgICAgIG5vZGUuYWRkRXZlbnRMaXN0ZW5lcihFTlRSWV9GT0NVUywgaGFuZGxlRW50cnlGb2N1cyk7XG4gICAgICByZXR1cm4gKCkgPT4gbm9kZS5yZW1vdmVFdmVudExpc3RlbmVyKEVOVFJZX0ZPQ1VTLCBoYW5kbGVFbnRyeUZvY3VzKTtcbiAgICB9XG4gIH0sIFtoYW5kbGVFbnRyeUZvY3VzXSk7XG5cbiAgcmV0dXJuIChcbiAgICA8Um92aW5nRm9jdXNQcm92aWRlclxuICAgICAgc2NvcGU9e19fc2NvcGVSb3ZpbmdGb2N1c0dyb3VwfVxuICAgICAgb3JpZW50YXRpb249e29yaWVudGF0aW9ufVxuICAgICAgZGlyPXtkaXJlY3Rpb259XG4gICAgICBsb29wPXtsb29wfVxuICAgICAgY3VycmVudFRhYlN0b3BJZD17Y3VycmVudFRhYlN0b3BJZH1cbiAgICAgIG9uSXRlbUZvY3VzPXtSZWFjdC51c2VDYWxsYmFjayhcbiAgICAgICAgKHRhYlN0b3BJZCkgPT4gc2V0Q3VycmVudFRhYlN0b3BJZCh0YWJTdG9wSWQpLFxuICAgICAgICBbc2V0Q3VycmVudFRhYlN0b3BJZF1cbiAgICAgICl9XG4gICAgICBvbkl0ZW1TaGlmdFRhYj17UmVhY3QudXNlQ2FsbGJhY2soKCkgPT4gc2V0SXNUYWJiaW5nQmFja091dCh0cnVlKSwgW10pfVxuICAgICAgb25Gb2N1c2FibGVJdGVtQWRkPXtSZWFjdC51c2VDYWxsYmFjayhcbiAgICAgICAgKCkgPT4gc2V0Rm9jdXNhYmxlSXRlbXNDb3VudCgocHJldkNvdW50KSA9PiBwcmV2Q291bnQgKyAxKSxcbiAgICAgICAgW11cbiAgICAgICl9XG4gICAgICBvbkZvY3VzYWJsZUl0ZW1SZW1vdmU9e1JlYWN0LnVzZUNhbGxiYWNrKFxuICAgICAgICAoKSA9PiBzZXRGb2N1c2FibGVJdGVtc0NvdW50KChwcmV2Q291bnQpID0+IHByZXZDb3VudCAtIDEpLFxuICAgICAgICBbXVxuICAgICAgKX1cbiAgICA+XG4gICAgICA8UHJpbWl0aXZlLmRpdlxuICAgICAgICB0YWJJbmRleD17aXNUYWJiaW5nQmFja091dCB8fCBmb2N1c2FibGVJdGVtc0NvdW50ID09PSAwID8gLTEgOiAwfVxuICAgICAgICBkYXRhLW9yaWVudGF0aW9uPXtvcmllbnRhdGlvbn1cbiAgICAgICAgey4uLmdyb3VwUHJvcHN9XG4gICAgICAgIHJlZj17Y29tcG9zZWRSZWZzfVxuICAgICAgICBzdHlsZT17eyBvdXRsaW5lOiAnbm9uZScsIC4uLnByb3BzLnN0eWxlIH19XG4gICAgICAgIG9uTW91c2VEb3duPXtjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vbk1vdXNlRG93biwgKCkgPT4ge1xuICAgICAgICAgIGlzQ2xpY2tGb2N1c1JlZi5jdXJyZW50ID0gdHJ1ZTtcbiAgICAgICAgfSl9XG4gICAgICAgIG9uRm9jdXM9e2NvbXBvc2VFdmVudEhhbmRsZXJzKHByb3BzLm9uRm9jdXMsIChldmVudCkgPT4ge1xuICAgICAgICAgIC8vIFdlIG5vcm1hbGx5IHdvdWxkbid0IG5lZWQgdGhpcyBjaGVjaywgYmVjYXVzZSB3ZSBhbHJlYWR5IGNoZWNrXG4gICAgICAgICAgLy8gdGhhdCB0aGUgZm9jdXMgaXMgb24gdGhlIGN1cnJlbnQgdGFyZ2V0IGFuZCBub3QgYnViYmxpbmcgdG8gaXQuXG4gICAgICAgICAgLy8gV2UgZG8gdGhpcyBiZWNhdXNlIFNhZmFyaSBkb2Vzbid0IGZvY3VzIGJ1dHRvbnMgd2hlbiBjbGlja2VkLCBhbmRcbiAgICAgICAgICAvLyBpbnN0ZWFkLCB0aGUgd3JhcHBlciB3aWxsIGdldCBmb2N1c2VkIGFuZCBub3QgdGhyb3VnaCBhIGJ1YmJsaW5nIGV2ZW50LlxuICAgICAgICAgIGNvbnN0IGlzS2V5Ym9hcmRGb2N1cyA9ICFpc0NsaWNrRm9jdXNSZWYuY3VycmVudDtcblxuICAgICAgICAgIGlmIChldmVudC50YXJnZXQgPT09IGV2ZW50LmN1cnJlbnRUYXJnZXQgJiYgaXNLZXlib2FyZEZvY3VzICYmICFpc1RhYmJpbmdCYWNrT3V0KSB7XG4gICAgICAgICAgICBjb25zdCBlbnRyeUZvY3VzRXZlbnQgPSBuZXcgQ3VzdG9tRXZlbnQoRU5UUllfRk9DVVMsIEVWRU5UX09QVElPTlMpO1xuICAgICAgICAgICAgZXZlbnQuY3VycmVudFRhcmdldC5kaXNwYXRjaEV2ZW50KGVudHJ5Rm9jdXNFdmVudCk7XG5cbiAgICAgICAgICAgIGlmICghZW50cnlGb2N1c0V2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIHtcbiAgICAgICAgICAgICAgY29uc3QgaXRlbXMgPSBnZXRJdGVtcygpLmZpbHRlcigoaXRlbSkgPT4gaXRlbS5mb2N1c2FibGUpO1xuICAgICAgICAgICAgICBjb25zdCBhY3RpdmVJdGVtID0gaXRlbXMuZmluZCgoaXRlbSkgPT4gaXRlbS5hY3RpdmUpO1xuICAgICAgICAgICAgICBjb25zdCBjdXJyZW50SXRlbSA9IGl0ZW1zLmZpbmQoKGl0ZW0pID0+IGl0ZW0uaWQgPT09IGN1cnJlbnRUYWJTdG9wSWQpO1xuICAgICAgICAgICAgICBjb25zdCBjYW5kaWRhdGVJdGVtcyA9IFthY3RpdmVJdGVtLCBjdXJyZW50SXRlbSwgLi4uaXRlbXNdLmZpbHRlcihcbiAgICAgICAgICAgICAgICBCb29sZWFuXG4gICAgICAgICAgICAgICkgYXMgdHlwZW9mIGl0ZW1zO1xuICAgICAgICAgICAgICBjb25zdCBjYW5kaWRhdGVOb2RlcyA9IGNhbmRpZGF0ZUl0ZW1zLm1hcCgoaXRlbSkgPT4gaXRlbS5yZWYuY3VycmVudCEpO1xuICAgICAgICAgICAgICBmb2N1c0ZpcnN0KGNhbmRpZGF0ZU5vZGVzLCBwcmV2ZW50U2Nyb2xsT25FbnRyeUZvY3VzKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICBpc0NsaWNrRm9jdXNSZWYuY3VycmVudCA9IGZhbHNlO1xuICAgICAgICB9KX1cbiAgICAgICAgb25CbHVyPXtjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vbkJsdXIsICgpID0+IHNldElzVGFiYmluZ0JhY2tPdXQoZmFsc2UpKX1cbiAgICAgIC8+XG4gICAgPC9Sb3ZpbmdGb2N1c1Byb3ZpZGVyPlxuICApO1xufSk7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIFJvdmluZ0ZvY3VzR3JvdXBJdGVtXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IElURU1fTkFNRSA9ICdSb3ZpbmdGb2N1c0dyb3VwSXRlbSc7XG5cbnR5cGUgUm92aW5nRm9jdXNJdGVtRWxlbWVudCA9IFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIFByaW1pdGl2ZS5zcGFuPjtcbnR5cGUgUHJpbWl0aXZlU3BhblByb3BzID0gUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBQcmltaXRpdmUuc3Bhbj47XG5pbnRlcmZhY2UgUm92aW5nRm9jdXNJdGVtUHJvcHMgZXh0ZW5kcyBQcmltaXRpdmVTcGFuUHJvcHMge1xuICB0YWJTdG9wSWQ/OiBzdHJpbmc7XG4gIGZvY3VzYWJsZT86IGJvb2xlYW47XG4gIGFjdGl2ZT86IGJvb2xlYW47XG59XG5cbmNvbnN0IFJvdmluZ0ZvY3VzR3JvdXBJdGVtID0gUmVhY3QuZm9yd2FyZFJlZjxSb3ZpbmdGb2N1c0l0ZW1FbGVtZW50LCBSb3ZpbmdGb2N1c0l0ZW1Qcm9wcz4oXG4gIChwcm9wczogU2NvcGVkUHJvcHM8Um92aW5nRm9jdXNJdGVtUHJvcHM+LCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICBjb25zdCB7XG4gICAgICBfX3Njb3BlUm92aW5nRm9jdXNHcm91cCxcbiAgICAgIGZvY3VzYWJsZSA9IHRydWUsXG4gICAgICBhY3RpdmUgPSBmYWxzZSxcbiAgICAgIHRhYlN0b3BJZCxcbiAgICAgIC4uLml0ZW1Qcm9wc1xuICAgIH0gPSBwcm9wcztcbiAgICBjb25zdCBhdXRvSWQgPSB1c2VJZCgpO1xuICAgIGNvbnN0IGlkID0gdGFiU3RvcElkIHx8IGF1dG9JZDtcbiAgICBjb25zdCBjb250ZXh0ID0gdXNlUm92aW5nRm9jdXNDb250ZXh0KElURU1fTkFNRSwgX19zY29wZVJvdmluZ0ZvY3VzR3JvdXApO1xuICAgIGNvbnN0IGlzQ3VycmVudFRhYlN0b3AgPSBjb250ZXh0LmN1cnJlbnRUYWJTdG9wSWQgPT09IGlkO1xuICAgIGNvbnN0IGdldEl0ZW1zID0gdXNlQ29sbGVjdGlvbihfX3Njb3BlUm92aW5nRm9jdXNHcm91cCk7XG5cbiAgICBjb25zdCB7IG9uRm9jdXNhYmxlSXRlbUFkZCwgb25Gb2N1c2FibGVJdGVtUmVtb3ZlIH0gPSBjb250ZXh0O1xuXG4gICAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgIGlmIChmb2N1c2FibGUpIHtcbiAgICAgICAgb25Gb2N1c2FibGVJdGVtQWRkKCk7XG4gICAgICAgIHJldHVybiAoKSA9PiBvbkZvY3VzYWJsZUl0ZW1SZW1vdmUoKTtcbiAgICAgIH1cbiAgICB9LCBbZm9jdXNhYmxlLCBvbkZvY3VzYWJsZUl0ZW1BZGQsIG9uRm9jdXNhYmxlSXRlbVJlbW92ZV0pO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDxDb2xsZWN0aW9uLkl0ZW1TbG90XG4gICAgICAgIHNjb3BlPXtfX3Njb3BlUm92aW5nRm9jdXNHcm91cH1cbiAgICAgICAgaWQ9e2lkfVxuICAgICAgICBmb2N1c2FibGU9e2ZvY3VzYWJsZX1cbiAgICAgICAgYWN0aXZlPXthY3RpdmV9XG4gICAgICA+XG4gICAgICAgIDxQcmltaXRpdmUuc3BhblxuICAgICAgICAgIHRhYkluZGV4PXtpc0N1cnJlbnRUYWJTdG9wID8gMCA6IC0xfVxuICAgICAgICAgIGRhdGEtb3JpZW50YXRpb249e2NvbnRleHQub3JpZW50YXRpb259XG4gICAgICAgICAgey4uLml0ZW1Qcm9wc31cbiAgICAgICAgICByZWY9e2ZvcndhcmRlZFJlZn1cbiAgICAgICAgICBvbk1vdXNlRG93bj17Y29tcG9zZUV2ZW50SGFuZGxlcnMocHJvcHMub25Nb3VzZURvd24sIChldmVudCkgPT4ge1xuICAgICAgICAgICAgLy8gV2UgcHJldmVudCBmb2N1c2luZyBub24tZm9jdXNhYmxlIGl0ZW1zIG9uIGBtb3VzZWRvd25gLlxuICAgICAgICAgICAgLy8gRXZlbiB0aG91Z2ggdGhlIGl0ZW0gaGFzIHRhYkluZGV4PXstMX0sIHRoYXQgb25seSBtZWFucyB0YWtlIGl0IG91dCBvZiB0aGUgdGFiIG9yZGVyLlxuICAgICAgICAgICAgaWYgKCFmb2N1c2FibGUpIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICAvLyBTYWZhcmkgZG9lc24ndCBmb2N1cyBhIGJ1dHRvbiB3aGVuIGNsaWNrZWQgc28gd2UgcnVuIG91ciBsb2dpYyBvbiBtb3VzZWRvd24gYWxzb1xuICAgICAgICAgICAgZWxzZSBjb250ZXh0Lm9uSXRlbUZvY3VzKGlkKTtcbiAgICAgICAgICB9KX1cbiAgICAgICAgICBvbkZvY3VzPXtjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vbkZvY3VzLCAoKSA9PiBjb250ZXh0Lm9uSXRlbUZvY3VzKGlkKSl9XG4gICAgICAgICAgb25LZXlEb3duPXtjb21wb3NlRXZlbnRIYW5kbGVycyhwcm9wcy5vbktleURvd24sIChldmVudCkgPT4ge1xuICAgICAgICAgICAgaWYgKGV2ZW50LmtleSA9PT0gJ1RhYicgJiYgZXZlbnQuc2hpZnRLZXkpIHtcbiAgICAgICAgICAgICAgY29udGV4dC5vbkl0ZW1TaGlmdFRhYigpO1xuICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGlmIChldmVudC50YXJnZXQgIT09IGV2ZW50LmN1cnJlbnRUYXJnZXQpIHJldHVybjtcblxuICAgICAgICAgICAgY29uc3QgZm9jdXNJbnRlbnQgPSBnZXRGb2N1c0ludGVudChldmVudCwgY29udGV4dC5vcmllbnRhdGlvbiwgY29udGV4dC5kaXIpO1xuXG4gICAgICAgICAgICBpZiAoZm9jdXNJbnRlbnQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICBpZiAoZXZlbnQubWV0YUtleSB8fCBldmVudC5jdHJsS2V5IHx8IGV2ZW50LmFsdEtleSB8fCBldmVudC5zaGlmdEtleSkgcmV0dXJuO1xuICAgICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICBjb25zdCBpdGVtcyA9IGdldEl0ZW1zKCkuZmlsdGVyKChpdGVtKSA9PiBpdGVtLmZvY3VzYWJsZSk7XG4gICAgICAgICAgICAgIGxldCBjYW5kaWRhdGVOb2RlcyA9IGl0ZW1zLm1hcCgoaXRlbSkgPT4gaXRlbS5yZWYuY3VycmVudCEpO1xuXG4gICAgICAgICAgICAgIGlmIChmb2N1c0ludGVudCA9PT0gJ2xhc3QnKSBjYW5kaWRhdGVOb2Rlcy5yZXZlcnNlKCk7XG4gICAgICAgICAgICAgIGVsc2UgaWYgKGZvY3VzSW50ZW50ID09PSAncHJldicgfHwgZm9jdXNJbnRlbnQgPT09ICduZXh0Jykge1xuICAgICAgICAgICAgICAgIGlmIChmb2N1c0ludGVudCA9PT0gJ3ByZXYnKSBjYW5kaWRhdGVOb2Rlcy5yZXZlcnNlKCk7XG4gICAgICAgICAgICAgICAgY29uc3QgY3VycmVudEluZGV4ID0gY2FuZGlkYXRlTm9kZXMuaW5kZXhPZihldmVudC5jdXJyZW50VGFyZ2V0KTtcbiAgICAgICAgICAgICAgICBjYW5kaWRhdGVOb2RlcyA9IGNvbnRleHQubG9vcFxuICAgICAgICAgICAgICAgICAgPyB3cmFwQXJyYXkoY2FuZGlkYXRlTm9kZXMsIGN1cnJlbnRJbmRleCArIDEpXG4gICAgICAgICAgICAgICAgICA6IGNhbmRpZGF0ZU5vZGVzLnNsaWNlKGN1cnJlbnRJbmRleCArIDEpO1xuICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgLyoqXG4gICAgICAgICAgICAgICAqIEltcGVyYXRpdmUgZm9jdXMgZHVyaW5nIGtleWRvd24gaXMgcmlza3kgc28gd2UgcHJldmVudCBSZWFjdCdzIGJhdGNoaW5nIHVwZGF0ZXNcbiAgICAgICAgICAgICAgICogdG8gYXZvaWQgcG90ZW50aWFsIGJ1Z3MuIFNlZTogaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlYWN0L2lzc3Vlcy8yMDMzMlxuICAgICAgICAgICAgICAgKi9cbiAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiBmb2N1c0ZpcnN0KGNhbmRpZGF0ZU5vZGVzKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSl9XG4gICAgICAgIC8+XG4gICAgICA8L0NvbGxlY3Rpb24uSXRlbVNsb3Q+XG4gICAgKTtcbiAgfVxuKTtcblxuUm92aW5nRm9jdXNHcm91cEl0ZW0uZGlzcGxheU5hbWUgPSBJVEVNX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuLy8gcHJldHRpZXItaWdub3JlXG5jb25zdCBNQVBfS0VZX1RPX0ZPQ1VTX0lOVEVOVDogUmVjb3JkPHN0cmluZywgRm9jdXNJbnRlbnQ+ID0ge1xuICBBcnJvd0xlZnQ6ICdwcmV2JywgQXJyb3dVcDogJ3ByZXYnLFxuICBBcnJvd1JpZ2h0OiAnbmV4dCcsIEFycm93RG93bjogJ25leHQnLFxuICBQYWdlVXA6ICdmaXJzdCcsIEhvbWU6ICdmaXJzdCcsXG4gIFBhZ2VEb3duOiAnbGFzdCcsIEVuZDogJ2xhc3QnLFxufTtcblxuZnVuY3Rpb24gZ2V0RGlyZWN0aW9uQXdhcmVLZXkoa2V5OiBzdHJpbmcsIGRpcj86IERpcmVjdGlvbikge1xuICBpZiAoZGlyICE9PSAncnRsJykgcmV0dXJuIGtleTtcbiAgcmV0dXJuIGtleSA9PT0gJ0Fycm93TGVmdCcgPyAnQXJyb3dSaWdodCcgOiBrZXkgPT09ICdBcnJvd1JpZ2h0JyA/ICdBcnJvd0xlZnQnIDoga2V5O1xufVxuXG50eXBlIEZvY3VzSW50ZW50ID0gJ2ZpcnN0JyB8ICdsYXN0JyB8ICdwcmV2JyB8ICduZXh0JztcblxuZnVuY3Rpb24gZ2V0Rm9jdXNJbnRlbnQoZXZlbnQ6IFJlYWN0LktleWJvYXJkRXZlbnQsIG9yaWVudGF0aW9uPzogT3JpZW50YXRpb24sIGRpcj86IERpcmVjdGlvbikge1xuICBjb25zdCBrZXkgPSBnZXREaXJlY3Rpb25Bd2FyZUtleShldmVudC5rZXksIGRpcik7XG4gIGlmIChvcmllbnRhdGlvbiA9PT0gJ3ZlcnRpY2FsJyAmJiBbJ0Fycm93TGVmdCcsICdBcnJvd1JpZ2h0J10uaW5jbHVkZXMoa2V5KSkgcmV0dXJuIHVuZGVmaW5lZDtcbiAgaWYgKG9yaWVudGF0aW9uID09PSAnaG9yaXpvbnRhbCcgJiYgWydBcnJvd1VwJywgJ0Fycm93RG93biddLmluY2x1ZGVzKGtleSkpIHJldHVybiB1bmRlZmluZWQ7XG4gIHJldHVybiBNQVBfS0VZX1RPX0ZPQ1VTX0lOVEVOVFtrZXldO1xufVxuXG5mdW5jdGlvbiBmb2N1c0ZpcnN0KGNhbmRpZGF0ZXM6IEhUTUxFbGVtZW50W10sIHByZXZlbnRTY3JvbGwgPSBmYWxzZSkge1xuICBjb25zdCBQUkVWSU9VU0xZX0ZPQ1VTRURfRUxFTUVOVCA9IGRvY3VtZW50LmFjdGl2ZUVsZW1lbnQ7XG4gIGZvciAoY29uc3QgY2FuZGlkYXRlIG9mIGNhbmRpZGF0ZXMpIHtcbiAgICAvLyBpZiBmb2N1cyBpcyBhbHJlYWR5IHdoZXJlIHdlIHdhbnQgdG8gZ28sIHdlIGRvbid0IHdhbnQgdG8ga2VlcCBnb2luZyB0aHJvdWdoIHRoZSBjYW5kaWRhdGVzXG4gICAgaWYgKGNhbmRpZGF0ZSA9PT0gUFJFVklPVVNMWV9GT0NVU0VEX0VMRU1FTlQpIHJldHVybjtcbiAgICBjYW5kaWRhdGUuZm9jdXMoeyBwcmV2ZW50U2Nyb2xsIH0pO1xuICAgIGlmIChkb2N1bWVudC5hY3RpdmVFbGVtZW50ICE9PSBQUkVWSU9VU0xZX0ZPQ1VTRURfRUxFTUVOVCkgcmV0dXJuO1xuICB9XG59XG5cbi8qKlxuICogV3JhcHMgYW4gYXJyYXkgYXJvdW5kIGl0c2VsZiBhdCBhIGdpdmVuIHN0YXJ0IGluZGV4XG4gKiBFeGFtcGxlOiBgd3JhcEFycmF5KFsnYScsICdiJywgJ2MnLCAnZCddLCAyKSA9PT0gWydjJywgJ2QnLCAnYScsICdiJ11gXG4gKi9cbmZ1bmN0aW9uIHdyYXBBcnJheTxUPihhcnJheTogVFtdLCBzdGFydEluZGV4OiBudW1iZXIpIHtcbiAgcmV0dXJuIGFycmF5Lm1hcCgoXywgaW5kZXgpID0+IGFycmF5WyhzdGFydEluZGV4ICsgaW5kZXgpICUgYXJyYXkubGVuZ3RoXSk7XG59XG5cbmNvbnN0IFJvb3QgPSBSb3ZpbmdGb2N1c0dyb3VwO1xuY29uc3QgSXRlbSA9IFJvdmluZ0ZvY3VzR3JvdXBJdGVtO1xuXG5leHBvcnQge1xuICBjcmVhdGVSb3ZpbmdGb2N1c0dyb3VwU2NvcGUsXG4gIC8vXG4gIFJvdmluZ0ZvY3VzR3JvdXAsXG4gIFJvdmluZ0ZvY3VzR3JvdXBJdGVtLFxuICAvL1xuICBSb290LFxuICBJdGVtLFxufTtcbmV4cG9ydCB0eXBlIHsgUm92aW5nRm9jdXNHcm91cFByb3BzLCBSb3ZpbmdGb2N1c0l0ZW1Qcm9wcyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-roving-focu_2dc2a383afe5e5f0b9ee6d67dee2b7ba/node_modules/@radix-ui/react-roving-focus/dist/index.mjs\n");

/***/ })

};
;