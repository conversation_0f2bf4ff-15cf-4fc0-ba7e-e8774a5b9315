"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentFileResponseDto = exports.DocumentUploadDto = exports.DocumentWithRelationsDto = exports.DocumentResponseDto = exports.UpdateDocumentDto = exports.CreateDocumentDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateDocumentDto {
}
exports.CreateDocumentDto = CreateDocumentDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Document name',
        example: 'Strategic Plan 2024-2026',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateDocumentDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Document description',
        example: 'Strategic plan document outlining objectives and goals for the partnership',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateDocumentDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Organization ID that owns this document',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateDocumentDto.prototype, "organizationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Document type ID',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateDocumentDto.prototype, "documentTypeId", void 0);
class UpdateDocumentDto {
}
exports.UpdateDocumentDto = UpdateDocumentDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Document name',
        example: 'Updated Strategic Plan 2024-2026',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateDocumentDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Document description',
        example: 'Updated strategic plan document',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateDocumentDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Document type ID',
        example: 2,
        required: false,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateDocumentDto.prototype, "documentTypeId", void 0);
class DocumentResponseDto {
}
exports.DocumentResponseDto = DocumentResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Document ID', example: 1 }),
    __metadata("design:type", Number)
], DocumentResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Document name', example: 'Strategic Plan 2024-2026' }),
    __metadata("design:type", String)
], DocumentResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Document description', example: 'Strategic plan document...', required: false }),
    __metadata("design:type", String)
], DocumentResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Organization ID', example: 1 }),
    __metadata("design:type", Number)
], DocumentResponseDto.prototype, "organizationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Document type ID', example: 1 }),
    __metadata("design:type", Number)
], DocumentResponseDto.prototype, "documentTypeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation date' }),
    __metadata("design:type", Date)
], DocumentResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update date' }),
    __metadata("design:type", Date)
], DocumentResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Soft delete flag' }),
    __metadata("design:type", Boolean)
], DocumentResponseDto.prototype, "deleted", void 0);
class DocumentWithRelationsDto extends DocumentResponseDto {
}
exports.DocumentWithRelationsDto = DocumentWithRelationsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Organization information' }),
    __metadata("design:type", Object)
], DocumentWithRelationsDto.prototype, "organization", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Document type information' }),
    __metadata("design:type", Object)
], DocumentWithRelationsDto.prototype, "documentType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Associated project (if this is a project document)' }),
    __metadata("design:type", Object)
], DocumentWithRelationsDto.prototype, "project", void 0);
class DocumentUploadDto {
}
exports.DocumentUploadDto = DocumentUploadDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Document file',
        type: 'string',
        format: 'binary',
    }),
    __metadata("design:type", Object)
], DocumentUploadDto.prototype, "file", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Document name',
        example: 'Strategic Plan 2024-2026',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DocumentUploadDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Document description',
        example: 'Strategic plan document outlining objectives and goals',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], DocumentUploadDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Organization ID',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], DocumentUploadDto.prototype, "organizationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Document type ID',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], DocumentUploadDto.prototype, "documentTypeId", void 0);
class DocumentFileResponseDto {
}
exports.DocumentFileResponseDto = DocumentFileResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'File name', example: 'strategic-plan-2024.pdf' }),
    __metadata("design:type", String)
], DocumentFileResponseDto.prototype, "fileName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Original file name', example: 'Strategic Plan 2024-2026.pdf' }),
    __metadata("design:type", String)
], DocumentFileResponseDto.prototype, "originalName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'File size in bytes', example: 1024000 }),
    __metadata("design:type", Number)
], DocumentFileResponseDto.prototype, "size", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'MIME type', example: 'application/pdf' }),
    __metadata("design:type", String)
], DocumentFileResponseDto.prototype, "mimeType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'File path', example: '/uploads/documents/strategic-plan-2024.pdf' }),
    __metadata("design:type", String)
], DocumentFileResponseDto.prototype, "path", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Upload date' }),
    __metadata("design:type", Date)
], DocumentFileResponseDto.prototype, "uploadedAt", void 0);
//# sourceMappingURL=create-document.dto.js.map