import { FundingUnitsService } from './funding-units.service';
import { CreateFundingUnitDto, UpdateFundingUnitDto } from './dto';
export declare class FundingUnitsController {
    private readonly fundingUnitsService;
    constructor(fundingUnitsService: FundingUnitsService);
    create(createFundingUnitDto: CreateFundingUnitDto, req: any): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        unitName: string;
    }>;
    findAll(): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        unitName: string;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        unitName: string;
    }>;
    update(id: number, updateFundingUnitDto: UpdateFundingUnitDto, req: any): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        unitName: string;
    }>;
    remove(id: number, req: any): Promise<{
        message: string;
    }>;
}
