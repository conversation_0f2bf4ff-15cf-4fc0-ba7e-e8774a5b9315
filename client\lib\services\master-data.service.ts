import api from "../api"

// Common interfaces for master data entities
export interface BaseEntity {
  id: number
  createAt: string
  updatedAt: string
}

export interface BudgetType extends BaseEntity {
  typeName: string
}

export interface FundingSource extends BaseEntity {
  sourceName: string
}

export interface FundingUnit extends BaseEntity {
  unitName: string
}

export interface OrganizationType extends BaseEntity {
  typeName: string
}

export interface DomainIntervention extends BaseEntity {
  domainName: string
  description?: string
  parentId?: number
  parent?: DomainIntervention
  children?: DomainIntervention[]
}

export interface InputCategory extends BaseEntity {
  categoryName: string
  description?: string
  parentId?: number
}

export interface FinancingScheme extends BaseEntity {
  schemeName: string
  description?: string
  terms?: string
  conditions?: string
}

export interface FinancingAgent extends BaseEntity {
  agentName: string
  description?: string
  contactInfo?: string
}

export interface HealthCareProvider extends BaseEntity {
  providerName: string
  description?: string
  location?: string
  contactEmail?: string
  contactPhone?: string
  website?: string
}

export interface Currency {
  id: number
  currencyCode: string
  currencyName: string
  createdAt: string
  updatedAt: string
}

export interface InputCategoryHierarchy extends BaseEntity {
  categoryName: string
  description?: string
  parentId?: number
  parent?: InputCategoryHierarchy
  children?: InputCategoryHierarchy[]
}

export interface DomainInterventionHierarchy extends BaseEntity {
  domainName: string
  description?: string
  parentId?: number
  parent?: DomainInterventionHierarchy
  children?: DomainInterventionHierarchy[]
}

// Request interfaces
export interface CreateBudgetTypeRequest {
  typeName: string
}

export interface CreateFundingSourceRequest {
  sourceName: string
}

export interface CreateFundingUnitRequest {
  unitName: string
}

export interface CreateOrganizationTypeRequest {
  typeName: string
}



export interface CreateInputCategoryRequest {
  categoryName: string
  description?: string
  parentId?: number
}

export interface CreateFinancingSchemeRequest {
  schemeName: string
  description?: string
  terms?: string
  conditions?: string
}

export interface CreateFinancingAgentRequest {
  agentName: string
  description?: string
  contactInfo?: string
}

export interface CreateHealthCareProviderRequest {
  providerName: string
  description?: string
  location?: string
  contactEmail?: string
  contactPhone?: string
  website?: string
}

export interface CreateDomainInterventionRequest {
  domainName: string
  description?: string
  parentId?: number
}

export interface CreateCurrencyRequest {
  currencyCode: string
  currencyName: string
}

export const masterDataService = {
  // Budget Types
  async getBudgetTypes(): Promise<BudgetType[]> {
    const response = await api.get("/budget-types")
    return response.data
  },

  async createBudgetType(data: CreateBudgetTypeRequest): Promise<BudgetType> {
    const response = await api.post("/budget-types", data)
    return response.data
  },

  async updateBudgetType(id: number, data: CreateBudgetTypeRequest): Promise<BudgetType> {
    const response = await api.patch(`/budget-types/${id}`, data)
    return response.data
  },

  async deleteBudgetType(id: number): Promise<void> {
    await api.delete(`/budget-types/${id}`)
  },

  // Funding Sources
  async getFundingSources(): Promise<FundingSource[]> {
    const response = await api.get("/funding-sources")
    return response.data
  },

  async createFundingSource(data: CreateFundingSourceRequest): Promise<FundingSource> {
    const response = await api.post("/funding-sources", data)
    return response.data
  },

  async updateFundingSource(id: number, data: CreateFundingSourceRequest): Promise<FundingSource> {
    const response = await api.patch(`/funding-sources/${id}`, data)
    return response.data
  },

  async deleteFundingSource(id: number): Promise<void> {
    await api.delete(`/funding-sources/${id}`)
  },

  // Funding Units
  async getFundingUnits(): Promise<FundingUnit[]> {
    const response = await api.get("/funding-units")
    return response.data
  },

  async createFundingUnit(data: CreateFundingUnitRequest): Promise<FundingUnit> {
    const response = await api.post("/funding-units", data)
    return response.data
  },

  async updateFundingUnit(id: number, data: CreateFundingUnitRequest): Promise<FundingUnit> {
    const response = await api.patch(`/funding-units/${id}`, data)
    return response.data
  },

  async deleteFundingUnit(id: number): Promise<void> {
    await api.delete(`/funding-units/${id}`)
  },

  // Organization Types
  async getOrganizationTypes(): Promise<OrganizationType[]> {
    const response = await api.get("/organization-types")
    return response.data
  },

  async createOrganizationType(data: CreateOrganizationTypeRequest): Promise<OrganizationType> {
    const response = await api.post("/organization-types", data)
    return response.data
  },

  async updateOrganizationType(id: number, data: CreateOrganizationTypeRequest): Promise<OrganizationType> {
    const response = await api.patch(`/organization-types/${id}`, data)
    return response.data
  },

  async deleteOrganizationType(id: number): Promise<void> {
    await api.delete(`/organization-types/${id}`)
  },

  // Health Care Providers
  async getHealthCareProviders(): Promise<HealthCareProvider[]> {
    const response = await api.get("/health-care-providers")
    return response.data
  },

  async createHealthCareProvider(data: CreateHealthCareProviderRequest): Promise<HealthCareProvider> {
    const response = await api.post("/health-care-providers", data)
    return response.data
  },

  async updateHealthCareProvider(id: number, data: CreateHealthCareProviderRequest): Promise<HealthCareProvider> {
    const response = await api.patch(`/health-care-providers/${id}`, data)
    return response.data
  },

  async deleteHealthCareProvider(id: number): Promise<void> {
    await api.delete(`/health-care-providers/${id}`)
  },

  // Financing Agents
  async getFinancingAgents(): Promise<FinancingAgent[]> {
    const response = await api.get("/financing-agents")
    return response.data
  },

  async createFinancingAgent(data: CreateFinancingAgentRequest): Promise<FinancingAgent> {
    const response = await api.post("/financing-agents", data)
    return response.data
  },

  async updateFinancingAgent(id: number, data: CreateFinancingAgentRequest): Promise<FinancingAgent> {
    const response = await api.patch(`/financing-agents/${id}`, data)
    return response.data
  },

  async deleteFinancingAgent(id: number): Promise<void> {
    await api.delete(`/financing-agents/${id}`)
  },

  // Financing Schemes
  async getFinancingSchemes(): Promise<FinancingScheme[]> {
    const response = await api.get("/financing-schemes")
    return response.data
  },

  async createFinancingScheme(data: CreateFinancingSchemeRequest): Promise<FinancingScheme> {
    const response = await api.post("/financing-schemes", data)
    return response.data
  },

  async updateFinancingScheme(id: number, data: CreateFinancingSchemeRequest): Promise<FinancingScheme> {
    const response = await api.patch(`/financing-schemes/${id}`, data)
    return response.data
  },

  async deleteFinancingScheme(id: number): Promise<void> {
    await api.delete(`/financing-schemes/${id}`)
  },

  // Input Categories (with hierarchy support)
  async getInputCategories(): Promise<InputCategory[]> {
    const response = await api.get("/input-categories")
    return response.data
  },

  async getInputCategoriesTree(): Promise<InputCategoryHierarchy[]> {
    const response = await api.get("/input-categories/tree")
    return response.data
  },

  async createInputCategory(data: CreateInputCategoryRequest): Promise<InputCategory> {
    const response = await api.post("/input-categories", data)
    return response.data
  },

  async updateInputCategory(id: number, data: CreateInputCategoryRequest): Promise<InputCategory> {
    const response = await api.patch(`/input-categories/${id}`, data)
    return response.data
  },

  async deleteInputCategory(id: number): Promise<void> {
    await api.delete(`/input-categories/${id}`)
  },

  // Domain Interventions (with hierarchy support)
  async getDomainInterventions(): Promise<DomainIntervention[]> {
    const response = await api.get("/domain-interventions")
    return response.data
  },

  async getDomainInterventionsTree(): Promise<DomainInterventionHierarchy[]> {
    const response = await api.get("/domain-interventions/tree")
    return response.data
  },

  async createDomainIntervention(data: CreateDomainInterventionRequest): Promise<DomainIntervention> {
    const response = await api.post("/domain-interventions", data)
    return response.data
  },

  async updateDomainIntervention(id: number, data: CreateDomainInterventionRequest): Promise<DomainIntervention> {
    const response = await api.patch(`/domain-interventions/${id}`, data)
    return response.data
  },

  async deleteDomainIntervention(id: number): Promise<void> {
    await api.delete(`/domain-interventions/${id}`)
  },

  // Currency
  async getCurrencies(): Promise<Currency[]> {
    const response = await api.get("/currencies")
    return response.data
  },

  async createCurrency(data: CreateCurrencyRequest): Promise<Currency> {
    const response = await api.post("/currencies", data)
    return response.data
  },

  async updateCurrency(id: number, data: CreateCurrencyRequest): Promise<Currency> {
    const response = await api.patch(`/currencies/${id}`, data)
    return response.data
  },

  async deleteCurrency(id: number): Promise<void> {
    await api.delete(`/currencies/${id}`)
  },
}
