import { PrismaService } from '../prisma/prisma.service';
import { CreateInputCategoryDto, UpdateInputCategoryDto } from './dto';
export declare class InputCategoriesService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    create(createInputCategoryDto: CreateInputCategoryDto, userId: string): Promise<{
        parent: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        };
        children: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        categoryName: string;
        parentId: number | null;
    }>;
    findAll(): Promise<({
        parent: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        };
        children: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        categoryName: string;
        parentId: number | null;
    })[]>;
    findTree(): Promise<any[]>;
    findOne(id: number): Promise<{
        parent: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        };
        children: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        categoryName: string;
        parentId: number | null;
    }>;
    update(id: number, updateInputCategoryDto: UpdateInputCategoryDto, userId: string): Promise<{
        parent: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        };
        children: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        categoryName: string;
        parentId: number | null;
    }>;
    remove(id: number, userId: string): Promise<{
        success: boolean;
    }>;
}
