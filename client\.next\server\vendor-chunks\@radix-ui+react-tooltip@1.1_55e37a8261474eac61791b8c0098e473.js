"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-tooltip@1.1_55e37a8261474eac61791b8c0098e473";
exports.ids = ["vendor-chunks/@radix-ui+react-tooltip@1.1_55e37a8261474eac61791b8c0098e473"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.1_55e37a8261474eac61791b8c0098e473/node_modules/@radix-ui/react-tooltip/dist/index.mjs":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-tooltip@1.1_55e37a8261474eac61791b8c0098e473/node_modules/@radix-ui/react-tooltip/dist/index.mjs ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root3),\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip),\n/* harmony export */   TooltipArrow: () => (/* binding */ TooltipArrow),\n/* harmony export */   TooltipContent: () => (/* binding */ TooltipContent),\n/* harmony export */   TooltipPortal: () => (/* binding */ TooltipPortal),\n/* harmony export */   TooltipProvider: () => (/* binding */ TooltipProvider),\n/* harmony export */   TooltipTrigger: () => (/* binding */ TooltipTrigger),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createTooltipScope: () => (/* binding */ createTooltipScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_c325a527ee623bb35f115eb421b60f39/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_003328fe74c3632a3c9c5ce511d98a5d/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_25d1e76761af64c65b25ef237e549d8d/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@19.1.6_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._3fa130616be06a95782b33ef544dc151/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._4572a068bb2a4a1b47f2889d3c644b5b/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._2ac352e659c445f8ff2bfd8f4fda9236/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_e3803c74fa3732ab7d036a7d08888245/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.1_@types+react@19.1.6_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_07f3fde21329b0c35912c2be5d860183/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hi_952a1481d55877aaf09731d2a2c146e5/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Portal,Provider,Root,Tooltip,TooltipArrow,TooltipContent,TooltipPortal,TooltipProvider,TooltipTrigger,Trigger,createTooltipScope auto */ // packages/react/tooltip/src/Tooltip.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar [createTooltipContext, createTooltipScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(\"Tooltip\", [\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope)();\nvar PROVIDER_NAME = \"TooltipProvider\";\nvar DEFAULT_DELAY_DURATION = 700;\nvar TOOLTIP_OPEN = \"tooltip.open\";\nvar [TooltipProviderContextProvider, useTooltipProviderContext] = createTooltipContext(PROVIDER_NAME);\nvar TooltipProvider = (props)=>{\n    const { __scopeTooltip, delayDuration = DEFAULT_DELAY_DURATION, skipDelayDuration = 300, disableHoverableContent = false, children } = props;\n    const [isOpenDelayed, setIsOpenDelayed] = react__WEBPACK_IMPORTED_MODULE_0__.useState(true);\n    const isPointerInTransitRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const skipDelayTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipProvider.useEffect\": ()=>{\n            const skipDelayTimer = skipDelayTimerRef.current;\n            return ({\n                \"TooltipProvider.useEffect\": ()=>window.clearTimeout(skipDelayTimer)\n            })[\"TooltipProvider.useEffect\"];\n        }\n    }[\"TooltipProvider.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipProviderContextProvider, {\n        scope: __scopeTooltip,\n        isOpenDelayed,\n        delayDuration,\n        onOpen: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"TooltipProvider.useCallback\": ()=>{\n                window.clearTimeout(skipDelayTimerRef.current);\n                setIsOpenDelayed(false);\n            }\n        }[\"TooltipProvider.useCallback\"], []),\n        onClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"TooltipProvider.useCallback\": ()=>{\n                window.clearTimeout(skipDelayTimerRef.current);\n                skipDelayTimerRef.current = window.setTimeout({\n                    \"TooltipProvider.useCallback\": ()=>setIsOpenDelayed(true)\n                }[\"TooltipProvider.useCallback\"], skipDelayDuration);\n            }\n        }[\"TooltipProvider.useCallback\"], [\n            skipDelayDuration\n        ]),\n        isPointerInTransitRef,\n        onPointerInTransitChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"TooltipProvider.useCallback\": (inTransit)=>{\n                isPointerInTransitRef.current = inTransit;\n            }\n        }[\"TooltipProvider.useCallback\"], []),\n        disableHoverableContent,\n        children\n    });\n};\nTooltipProvider.displayName = PROVIDER_NAME;\nvar TOOLTIP_NAME = \"Tooltip\";\nvar [TooltipContextProvider, useTooltipContext] = createTooltipContext(TOOLTIP_NAME);\nvar Tooltip = (props)=>{\n    const { __scopeTooltip, children, open: openProp, defaultOpen = false, onOpenChange, disableHoverableContent: disableHoverableContentProp, delayDuration: delayDurationProp } = props;\n    const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const contentId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)();\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const disableHoverableContent = disableHoverableContentProp ?? providerContext.disableHoverableContent;\n    const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n    const wasOpenDelayedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: {\n            \"Tooltip.useControllableState\": (open2)=>{\n                if (open2) {\n                    providerContext.onOpen();\n                    document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n                } else {\n                    providerContext.onClose();\n                }\n                onOpenChange?.(open2);\n            }\n        }[\"Tooltip.useControllableState\"]\n    });\n    const stateAttribute = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Tooltip.useMemo[stateAttribute]\": ()=>{\n            return open ? wasOpenDelayedRef.current ? \"delayed-open\" : \"instant-open\" : \"closed\";\n        }\n    }[\"Tooltip.useMemo[stateAttribute]\"], [\n        open\n    ]);\n    const handleOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Tooltip.useCallback[handleOpen]\": ()=>{\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = 0;\n            wasOpenDelayedRef.current = false;\n            setOpen(true);\n        }\n    }[\"Tooltip.useCallback[handleOpen]\"], [\n        setOpen\n    ]);\n    const handleClose = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Tooltip.useCallback[handleClose]\": ()=>{\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = 0;\n            setOpen(false);\n        }\n    }[\"Tooltip.useCallback[handleClose]\"], [\n        setOpen\n    ]);\n    const handleDelayedOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Tooltip.useCallback[handleDelayedOpen]\": ()=>{\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = window.setTimeout({\n                \"Tooltip.useCallback[handleDelayedOpen]\": ()=>{\n                    wasOpenDelayedRef.current = true;\n                    setOpen(true);\n                    openTimerRef.current = 0;\n                }\n            }[\"Tooltip.useCallback[handleDelayedOpen]\"], delayDuration);\n        }\n    }[\"Tooltip.useCallback[handleDelayedOpen]\"], [\n        delayDuration,\n        setOpen\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Tooltip.useEffect\": ()=>{\n            return ({\n                \"Tooltip.useEffect\": ()=>{\n                    if (openTimerRef.current) {\n                        window.clearTimeout(openTimerRef.current);\n                        openTimerRef.current = 0;\n                    }\n                }\n            })[\"Tooltip.useEffect\"];\n        }\n    }[\"Tooltip.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContextProvider, {\n            scope: __scopeTooltip,\n            contentId,\n            open,\n            stateAttribute,\n            trigger,\n            onTriggerChange: setTrigger,\n            onTriggerEnter: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"Tooltip.useCallback\": ()=>{\n                    if (providerContext.isOpenDelayed) handleDelayedOpen();\n                    else handleOpen();\n                }\n            }[\"Tooltip.useCallback\"], [\n                providerContext.isOpenDelayed,\n                handleDelayedOpen,\n                handleOpen\n            ]),\n            onTriggerLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                \"Tooltip.useCallback\": ()=>{\n                    if (disableHoverableContent) {\n                        handleClose();\n                    } else {\n                        window.clearTimeout(openTimerRef.current);\n                        openTimerRef.current = 0;\n                    }\n                }\n            }[\"Tooltip.useCallback\"], [\n                handleClose,\n                disableHoverableContent\n            ]),\n            onOpen: handleOpen,\n            onClose: handleClose,\n            disableHoverableContent,\n            children\n        })\n    });\n};\nTooltip.displayName = TOOLTIP_NAME;\nvar TRIGGER_NAME = \"TooltipTrigger\";\nvar TooltipTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerMoveOpenedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handlePointerUp = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"TooltipTrigger.useCallback[handlePointerUp]\": ()=>isPointerDownRef.current = false\n    }[\"TooltipTrigger.useCallback[handlePointerUp]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipTrigger.useEffect\": ()=>{\n            return ({\n                \"TooltipTrigger.useEffect\": ()=>document.removeEventListener(\"pointerup\", handlePointerUp)\n            })[\"TooltipTrigger.useEffect\"];\n        }\n    }[\"TooltipTrigger.useEffect\"], [\n        handlePointerUp\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            \"aria-describedby\": context.open ? context.contentId : void 0,\n            \"data-state\": context.stateAttribute,\n            ...triggerProps,\n            ref: composedRefs,\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                if (event.pointerType === \"touch\") return;\n                if (!hasPointerMoveOpenedRef.current && !providerContext.isPointerInTransitRef.current) {\n                    context.onTriggerEnter();\n                    hasPointerMoveOpenedRef.current = true;\n                }\n            }),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerLeave, ()=>{\n                context.onTriggerLeave();\n                hasPointerMoveOpenedRef.current = false;\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerDown, ()=>{\n                isPointerDownRef.current = true;\n                document.addEventListener(\"pointerup\", handlePointerUp, {\n                    once: true\n                });\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onFocus, ()=>{\n                if (!isPointerDownRef.current) context.onOpen();\n            }),\n            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onBlur, context.onClose),\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, context.onClose)\n        })\n    });\n});\nTooltipTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"TooltipPortal\";\nvar [PortalProvider, usePortalContext] = createTooltipContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar TooltipPortal = (props)=>{\n    const { __scopeTooltip, forceMount, children, container } = props;\n    const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeTooltip,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nTooltipPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"TooltipContent\";\nvar TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = \"top\", ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n        present: forceMount || context.open,\n        children: context.disableHoverableContent ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentHoverable, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar TooltipContentHoverable = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref);\n    const [pointerGraceArea, setPointerGraceArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const { trigger, onClose } = context;\n    const content = ref.current;\n    const { onPointerInTransitChange } = providerContext;\n    const handleRemoveGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"TooltipContentHoverable.useCallback[handleRemoveGraceArea]\": ()=>{\n            setPointerGraceArea(null);\n            onPointerInTransitChange(false);\n        }\n    }[\"TooltipContentHoverable.useCallback[handleRemoveGraceArea]\"], [\n        onPointerInTransitChange\n    ]);\n    const handleCreateGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"TooltipContentHoverable.useCallback[handleCreateGraceArea]\": (event, hoverTarget)=>{\n            const currentTarget = event.currentTarget;\n            const exitPoint = {\n                x: event.clientX,\n                y: event.clientY\n            };\n            const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n            const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n            const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n            const graceArea = getHull([\n                ...paddedExitPoints,\n                ...hoverTargetPoints\n            ]);\n            setPointerGraceArea(graceArea);\n            onPointerInTransitChange(true);\n        }\n    }[\"TooltipContentHoverable.useCallback[handleCreateGraceArea]\"], [\n        onPointerInTransitChange\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentHoverable.useEffect\": ()=>{\n            return ({\n                \"TooltipContentHoverable.useEffect\": ()=>handleRemoveGraceArea()\n            })[\"TooltipContentHoverable.useEffect\"];\n        }\n    }[\"TooltipContentHoverable.useEffect\"], [\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentHoverable.useEffect\": ()=>{\n            if (trigger && content) {\n                const handleTriggerLeave = {\n                    \"TooltipContentHoverable.useEffect.handleTriggerLeave\": (event)=>handleCreateGraceArea(event, content)\n                }[\"TooltipContentHoverable.useEffect.handleTriggerLeave\"];\n                const handleContentLeave = {\n                    \"TooltipContentHoverable.useEffect.handleContentLeave\": (event)=>handleCreateGraceArea(event, trigger)\n                }[\"TooltipContentHoverable.useEffect.handleContentLeave\"];\n                trigger.addEventListener(\"pointerleave\", handleTriggerLeave);\n                content.addEventListener(\"pointerleave\", handleContentLeave);\n                return ({\n                    \"TooltipContentHoverable.useEffect\": ()=>{\n                        trigger.removeEventListener(\"pointerleave\", handleTriggerLeave);\n                        content.removeEventListener(\"pointerleave\", handleContentLeave);\n                    }\n                })[\"TooltipContentHoverable.useEffect\"];\n            }\n        }\n    }[\"TooltipContentHoverable.useEffect\"], [\n        trigger,\n        content,\n        handleCreateGraceArea,\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentHoverable.useEffect\": ()=>{\n            if (pointerGraceArea) {\n                const handleTrackPointerGrace = {\n                    \"TooltipContentHoverable.useEffect.handleTrackPointerGrace\": (event)=>{\n                        const target = event.target;\n                        const pointerPosition = {\n                            x: event.clientX,\n                            y: event.clientY\n                        };\n                        const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n                        const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n                        if (hasEnteredTarget) {\n                            handleRemoveGraceArea();\n                        } else if (isPointerOutsideGraceArea) {\n                            handleRemoveGraceArea();\n                            onClose();\n                        }\n                    }\n                }[\"TooltipContentHoverable.useEffect.handleTrackPointerGrace\"];\n                document.addEventListener(\"pointermove\", handleTrackPointerGrace);\n                return ({\n                    \"TooltipContentHoverable.useEffect\": ()=>document.removeEventListener(\"pointermove\", handleTrackPointerGrace)\n                })[\"TooltipContentHoverable.useEffect\"];\n            }\n        }\n    }[\"TooltipContentHoverable.useEffect\"], [\n        trigger,\n        content,\n        pointerGraceArea,\n        onClose,\n        handleRemoveGraceArea\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n        ...props,\n        ref: composedRefs\n    });\n});\nvar [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] = createTooltipContext(TOOLTIP_NAME, {\n    isInside: false\n});\nvar TooltipContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, children, \"aria-label\": ariaLabel, onEscapeKeyDown, onPointerDownOutside, ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentImpl.useEffect\": ()=>{\n            document.addEventListener(TOOLTIP_OPEN, onClose);\n            return ({\n                \"TooltipContentImpl.useEffect\": ()=>document.removeEventListener(TOOLTIP_OPEN, onClose)\n            })[\"TooltipContentImpl.useEffect\"];\n        }\n    }[\"TooltipContentImpl.useEffect\"], [\n        onClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"TooltipContentImpl.useEffect\": ()=>{\n            if (context.trigger) {\n                const handleScroll = {\n                    \"TooltipContentImpl.useEffect.handleScroll\": (event)=>{\n                        const target = event.target;\n                        if (target?.contains(context.trigger)) onClose();\n                    }\n                }[\"TooltipContentImpl.useEffect.handleScroll\"];\n                window.addEventListener(\"scroll\", handleScroll, {\n                    capture: true\n                });\n                return ({\n                    \"TooltipContentImpl.useEffect\": ()=>window.removeEventListener(\"scroll\", handleScroll, {\n                            capture: true\n                        })\n                })[\"TooltipContentImpl.useEffect\"];\n            }\n        }\n    }[\"TooltipContentImpl.useEffect\"], [\n        context.trigger,\n        onClose\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_11__.DismissableLayer, {\n        asChild: true,\n        disableOutsidePointerEvents: false,\n        onEscapeKeyDown,\n        onPointerDownOutside,\n        onFocusOutside: (event)=>event.preventDefault(),\n        onDismiss: onClose,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            \"data-state\": context.stateAttribute,\n            ...popperScope,\n            ...contentProps,\n            ref: forwardedRef,\n            style: {\n                ...contentProps.style,\n                // re-namespace exposed content custom properties\n                ...{\n                    \"--radix-tooltip-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                    \"--radix-tooltip-content-available-width\": \"var(--radix-popper-available-width)\",\n                    \"--radix-tooltip-content-available-height\": \"var(--radix-popper-available-height)\",\n                    \"--radix-tooltip-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                    \"--radix-tooltip-trigger-height\": \"var(--radix-popper-anchor-height)\"\n                }\n            },\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__.Slottable, {\n                    children\n                }),\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(VisuallyHiddenContentContextProvider, {\n                    scope: __scopeTooltip,\n                    isInside: true,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                        id: context.contentId,\n                        role: \"tooltip\",\n                        children: ariaLabel || children\n                    })\n                })\n            ]\n        })\n    });\n});\nTooltipContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"TooltipArrow\";\nvar TooltipArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(ARROW_NAME, __scopeTooltip);\n    return visuallyHiddenContentContext.isInside ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nTooltipArrow.displayName = ARROW_NAME;\nfunction getExitSideFromRect(point, rect) {\n    const top = Math.abs(rect.top - point.y);\n    const bottom = Math.abs(rect.bottom - point.y);\n    const right = Math.abs(rect.right - point.x);\n    const left = Math.abs(rect.left - point.x);\n    switch(Math.min(top, bottom, right, left)){\n        case left:\n            return \"left\";\n        case right:\n            return \"right\";\n        case top:\n            return \"top\";\n        case bottom:\n            return \"bottom\";\n        default:\n            throw new Error(\"unreachable\");\n    }\n}\nfunction getPaddedExitPoints(exitPoint, exitSide, padding = 5) {\n    const paddedExitPoints = [];\n    switch(exitSide){\n        case \"top\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"bottom\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            });\n            break;\n        case \"left\":\n            paddedExitPoints.push({\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"right\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            });\n            break;\n    }\n    return paddedExitPoints;\n}\nfunction getPointsFromRect(rect) {\n    const { top, right, bottom, left } = rect;\n    return [\n        {\n            x: left,\n            y: top\n        },\n        {\n            x: right,\n            y: top\n        },\n        {\n            x: right,\n            y: bottom\n        },\n        {\n            x: left,\n            y: bottom\n        }\n    ];\n}\nfunction isPointInPolygon(point, polygon) {\n    const { x, y } = point;\n    let inside = false;\n    for(let i = 0, j = polygon.length - 1; i < polygon.length; j = i++){\n        const xi = polygon[i].x;\n        const yi = polygon[i].y;\n        const xj = polygon[j].x;\n        const yj = polygon[j].y;\n        const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n        if (intersect) inside = !inside;\n    }\n    return inside;\n}\nfunction getHull(points) {\n    const newPoints = points.slice();\n    newPoints.sort((a, b)=>{\n        if (a.x < b.x) return -1;\n        else if (a.x > b.x) return 1;\n        else if (a.y < b.y) return -1;\n        else if (a.y > b.y) return 1;\n        else return 0;\n    });\n    return getHullPresorted(newPoints);\n}\nfunction getHullPresorted(points) {\n    if (points.length <= 1) return points.slice();\n    const upperHull = [];\n    for(let i = 0; i < points.length; i++){\n        const p = points[i];\n        while(upperHull.length >= 2){\n            const q = upperHull[upperHull.length - 1];\n            const r = upperHull[upperHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n            else break;\n        }\n        upperHull.push(p);\n    }\n    upperHull.pop();\n    const lowerHull = [];\n    for(let i = points.length - 1; i >= 0; i--){\n        const p = points[i];\n        while(lowerHull.length >= 2){\n            const q = lowerHull[lowerHull.length - 1];\n            const r = lowerHull[lowerHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n            else break;\n        }\n        lowerHull.push(p);\n    }\n    lowerHull.pop();\n    if (upperHull.length === 1 && lowerHull.length === 1 && upperHull[0].x === lowerHull[0].x && upperHull[0].y === lowerHull[0].y) {\n        return upperHull;\n    } else {\n        return upperHull.concat(lowerHull);\n    }\n}\nvar Provider = TooltipProvider;\nvar Root3 = Tooltip;\nvar Trigger = TooltipTrigger;\nvar Portal = TooltipPortal;\nvar Content2 = TooltipContent;\nvar Arrow2 = TooltipArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.1_55e37a8261474eac61791b8c0098e473/node_modules/@radix-ui/react-tooltip/dist/index.mjs\n");

/***/ })

};
;