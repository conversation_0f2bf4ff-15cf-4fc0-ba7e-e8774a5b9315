"use client"

import * as React from "react"
import { Upload, X, FileText, AlertCircle, CheckCircle } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { fileUploadConfig } from "@/data/mock-data"

interface FileUploadProps {
  onFileSelect: (file: File | null) => void
  currentFile?: File | null
  accept?: string
  maxSize?: number
  className?: string
  disabled?: boolean
  required?: boolean
  uploadProgress?: number
  error?: string
  label?: string
  description?: string
}

export function FileUpload({
  onFileSelect,
  currentFile,
  accept = fileUploadConfig.acceptedExtensions.join(','),
  maxSize = fileUploadConfig.maxSize,
  className,
  disabled = false,
  required = false,
  uploadProgress,
  error,
  label,
  description,
}: FileUploadProps) {
  const [dragActive, setDragActive] = React.useState(false)
  const inputRef = React.useRef<HTMLInputElement>(null)

  const handleDrag = React.useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [])

  const handleDrop = React.useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      e.stopPropagation()
      setDragActive(false)

      if (disabled) return

      const files = e.dataTransfer.files
      if (files && files[0]) {
        handleFileSelection(files[0])
      }
    },
    [disabled]
  )

  const handleFileSelection = React.useCallback(
    (file: File) => {
      // Validate file size
      if (file.size > maxSize) {
        onFileSelect(null)
        return
      }

      // Validate file type
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
      if (!fileUploadConfig.acceptedExtensions.includes(fileExtension)) {
        onFileSelect(null)
        return
      }

      onFileSelect(file)
    },
    [maxSize, onFileSelect]
  )

  const handleInputChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files
      if (files && files[0]) {
        handleFileSelection(files[0])
      }
    },
    [handleFileSelection]
  )

  const handleRemoveFile = React.useCallback(() => {
    onFileSelect(null)
    if (inputRef.current) {
      inputRef.current.value = ''
    }
  }, [onFileSelect])

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const isUploading = uploadProgress !== undefined && uploadProgress > 0 && uploadProgress < 100
  const isUploaded = uploadProgress === 100

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {label}
          {required && <span className="text-destructive ml-1">*</span>}
        </label>
      )}
      
      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}

      <div
        className={cn(
          "relative border-2 border-dashed rounded-lg p-6 transition-colors",
          dragActive && "border-primary bg-primary/5",
          disabled && "opacity-50 cursor-not-allowed",
          error && "border-destructive",
          !disabled && !dragActive && "border-muted-foreground/25 hover:border-muted-foreground/50",
          currentFile && !error && "border-primary/50 bg-primary/5"
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={inputRef}
          type="file"
          accept={accept}
          onChange={handleInputChange}
          disabled={disabled}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed"
        />

        {currentFile ? (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  {isUploaded ? (
                    <CheckCircle className="h-8 w-8 text-green-500" />
                  ) : (
                    <FileText className="h-8 w-8 text-muted-foreground" />
                  )}
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-foreground truncate">
                    {currentFile.name}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {formatFileSize(currentFile.size)}
                  </p>
                </div>
              </div>
              {!disabled && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleRemoveFile}
                  className="flex-shrink-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            {isUploading && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Uploading...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <Progress value={uploadProgress} className="h-2" />
              </div>
            )}
          </div>
        ) : (
          <div className="text-center">
            <Upload className="mx-auto h-12 w-12 text-muted-foreground" />
            <div className="mt-4">
              <p className="text-sm font-medium">
                {dragActive ? "Drop file here" : "Choose file or drag and drop"}
              </p>
              <p className="text-sm text-muted-foreground mt-1">
                {fileUploadConfig.acceptedExtensions.join(', ')} up to {formatFileSize(maxSize)}
              </p>
            </div>
          </div>
        )}
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  )
}
