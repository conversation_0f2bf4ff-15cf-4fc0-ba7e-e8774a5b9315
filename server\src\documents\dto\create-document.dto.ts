import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsInt, IsNotEmpty, IsPositive } from 'class-validator';

export class CreateDocumentDto {
  @ApiProperty({
    description: 'Document name',
    example: 'Strategic Plan 2024-2026',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Document description',
    example: 'Strategic plan document outlining objectives and goals for the partnership',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Organization ID that owns this document',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  organizationId: number;

  @ApiProperty({
    description: 'Document type ID',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  documentTypeId: number;
}

export class UpdateDocumentDto {
  @ApiProperty({
    description: 'Document name',
    example: 'Updated Strategic Plan 2024-2026',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Document description',
    example: 'Updated strategic plan document',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Document type ID',
    example: 2,
    required: false,
  })
  @IsInt()
  @IsPositive()
  @IsOptional()
  documentTypeId?: number;
}

export class DocumentResponseDto {
  @ApiProperty({ description: 'Document ID', example: 1 })
  id: number;

  @ApiProperty({ description: 'Document name', example: 'Strategic Plan 2024-2026' })
  name: string;

  @ApiProperty({ description: 'Document description', example: 'Strategic plan document...', required: false })
  description?: string;

  @ApiProperty({ description: 'Organization ID', example: 1 })
  organizationId: number;

  @ApiProperty({ description: 'Document type ID', example: 1 })
  documentTypeId: number;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;

  @ApiProperty({ description: 'Soft delete flag' })
  deleted: boolean;
}

export class DocumentWithRelationsDto extends DocumentResponseDto {
  @ApiProperty({ description: 'Organization information' })
  organization?: {
    id: number;
    organizationName: string;
    organizationEmail: string;
  };

  @ApiProperty({ description: 'Document type information' })
  documentType?: {
    id: number;
    typeName: string;
  };

  @ApiProperty({ description: 'Associated project (if this is a project document)' })
  project?: {
    id: number;
    name: string;
    description?: string;
    mouApplication: {
      id: number;
      applicationKey: string;
    };
  };
}

export class DocumentUploadDto {
  @ApiProperty({
    description: 'Document file',
    type: 'string',
    format: 'binary',
  })
  file: Express.Multer.File;

  @ApiProperty({
    description: 'Document name',
    example: 'Strategic Plan 2024-2026',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Document description',
    example: 'Strategic plan document outlining objectives and goals',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Organization ID',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  organizationId: number;

  @ApiProperty({
    description: 'Document type ID',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  documentTypeId: number;
}

export class DocumentFileResponseDto {
  @ApiProperty({ description: 'File name', example: 'strategic-plan-2024.pdf' })
  fileName: string;

  @ApiProperty({ description: 'Original file name', example: 'Strategic Plan 2024-2026.pdf' })
  originalName: string;

  @ApiProperty({ description: 'File size in bytes', example: 1024000 })
  size: number;

  @ApiProperty({ description: 'MIME type', example: 'application/pdf' })
  mimeType: string;

  @ApiProperty({ description: 'File path', example: '/uploads/documents/strategic-plan-2024.pdf' })
  path: string;

  @ApiProperty({ description: 'Upload date' })
  uploadedAt: Date;
}
