import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class CreateFinancingSchemeDto {
  @ApiProperty({
    description: 'Name of the financing scheme',
    example: 'Health Sector Development Grant',
  })
  @IsString()
  @IsNotEmpty()
  schemeName: string;

  @ApiProperty({
    description: 'Description of the financing scheme',
    example: 'Grant program for health sector infrastructure development',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Terms and conditions of the financing scheme',
    example: 'Maximum grant amount: $1M, Duration: 3 years, Matching funds required',
    required: false,
  })
  @IsString()
  @IsOptional()
  terms?: string;

  @ApiProperty({
    description: 'Additional conditions for the financing scheme',
    example: 'Must demonstrate community impact and sustainability plan',
    required: false,
  })
  @IsString()
  @IsOptional()
  conditions?: string;
}
