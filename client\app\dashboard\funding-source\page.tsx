"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { DataTable } from "@/components/data-table"
import { Loader2, Plus } from "lucide-react"
import { masterDataService, type FundingSource } from "@/lib/services/master-data.service"

export default function FundingSourcePage() {
  const [fundingSources, setFundingSources] = useState<FundingSource[]>([])
  const [loading, setLoading] = useState(true)
  const [formLoading, setFormLoading] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingSource, setEditingSource] = useState<FundingSource | null>(null)
  const [sourceName, setSourceName] = useState("")
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  useEffect(() => {
    loadFundingSources()
  }, [])

  const loadFundingSources = async () => {
    try {
      setLoading(true)
      const data = await masterDataService.getFundingSources()
      setFundingSources(data)
    } catch (error) {
      console.error("Failed to load funding sources:", error)
      setError("Failed to load funding sources")
    } finally {
      setLoading(false)
    }
  }

  const columns = [
    { key: "sourceName", label: "Source Name" },
    {
      key: "createAt",
      label: "Created At",
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
    {
      key: "updatedAt",
      label: "Updated At",
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
  ]

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setFormLoading(true)
    setError("")
    setSuccess("")

    try {
      if (editingSource) {
        // Update existing source
        await masterDataService.updateFundingSource(editingSource.id, { sourceName })
        setSuccess("Funding source updated successfully")
      } else {
        // Create new source
        await masterDataService.createFundingSource({ sourceName })
        setSuccess("Funding source created successfully")
      }

      // Reload the data to get the latest from server
      await loadFundingSources()
      setDialogOpen(false)
      setSourceName("")
      setEditingSource(null)
    } catch (err: any) {
      console.error("Failed to save funding source:", err)
      setError(err.response?.data?.message || "Failed to save funding source")
    } finally {
      setFormLoading(false)
    }
  }

  const handleEdit = (source: FundingSource) => {
    setEditingSource(source)
    setSourceName(source.sourceName)
    setDialogOpen(true)
  }

  const handleDelete = async (source: FundingSource) => {
    if (confirm("Are you sure you want to delete this funding source?")) {
      try {
        await masterDataService.deleteFundingSource(source.id)
        setSuccess("Funding source deleted successfully")
        // Reload the data to get the latest from server
        await loadFundingSources()
      } catch (err: any) {
        console.error("Failed to delete funding source:", err)
        setError(err.response?.data?.message || "Failed to delete funding source")
      }
    }
  }

  const resetForm = () => {
    setSourceName("")
    setEditingSource(null)
    setError("")
    setSuccess("")
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Funding Sources</h2>
        <Dialog
          open={dialogOpen}
          onOpenChange={(open) => {
            setDialogOpen(open)
            if (!open) resetForm()
          }}
        >
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Add Funding Source
            </Button>
          </DialogTrigger>
          <DialogContent>
            <form onSubmit={handleSubmit}>
              <DialogHeader>
                <DialogTitle>{editingSource ? "Edit Funding Source" : "Add Funding Source"}</DialogTitle>
                <DialogDescription>
                  {editingSource ? "Update the funding source details." : "Create a new funding source."}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <div className="space-y-2">
                  <Label htmlFor="sourceName">Source Name</Label>
                  <Input
                    id="sourceName"
                    value={sourceName}
                    onChange={(e) => setSourceName(e.target.value)}
                    placeholder="Enter funding source name"
                    required
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" disabled={formLoading}>
                  {formLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {editingSource ? "Updating..." : "Creating..."}
                    </>
                  ) : editingSource ? (
                    "Update"
                  ) : (
                    "Create"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {success && (
        <Alert className="bg-green-50 text-green-700 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <p className="text-muted-foreground">Loading funding sources...</p>
        </div>
      ) : (
        <DataTable
          data={fundingSources}
          columns={columns}
          searchKey="sourceName"
          onEdit={handleEdit}
          onDelete={handleDelete}
          searchPlaceholder="Search funding sources..."
        />
      )}
    </div>
  )
}
