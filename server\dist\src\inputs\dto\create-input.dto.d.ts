export declare class CreateInputDto {
    name: string;
    inputSubclassId: number;
}
export declare class UpdateInputDto {
    name?: string;
    inputSubclassId?: number;
}
export declare class InputResponseDto {
    id: number;
    name: string;
    inputSubclassId: number;
    createdAt: Date;
    updatedAt: Date;
    deleted: boolean;
}
export declare class InputWithRelationsDto extends InputResponseDto {
    inputSubclass?: {
        id: number;
        subclassId: number;
        name: string;
        budget: number;
    };
    activities?: Array<{
        id: number;
        name: string;
        description?: string;
        project: {
            id: number;
            name: string;
        };
    }>;
}
export declare class CreateInputSubclassDto {
    subclassId: number;
    name: string;
    budget: number;
}
export declare class UpdateInputSubclassDto {
    name?: string;
    budget?: number;
}
export declare class InputSubclassResponseDto {
    id: number;
    subclassId: number;
    name: string;
    budget: number;
    createdAt: Date;
    updatedAt: Date;
    deleted: boolean;
}
export declare class InputSubclassWithRelationsDto extends InputSubclassResponseDto {
    inputs?: Array<{
        id: number;
        name: string;
    }>;
}
