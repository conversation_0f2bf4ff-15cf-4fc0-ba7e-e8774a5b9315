import { UserRole } from '@prisma/client';
export declare class UserResponse {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    role: UserRole;
    organizationId?: number;
    isActive: boolean;
    emailVerified: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export declare class UserListResponse {
    data: UserResponse[];
    meta: {
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    };
}
export declare class ActivityLogEntry {
    id: number;
    userId: number;
    action: string;
    details: Record<string, any>;
    category: string;
    importance: string;
    ipAddress?: string;
    userAgent?: string;
    createdAt: Date;
    user: {
        firstName: string;
        lastName: string;
        email: string;
    };
}
export declare class ActivityResponse {
    data: ActivityLogEntry[];
    meta: {
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    };
}
export declare class AuditLogEntry {
    id: number;
    userId: number;
    action: string;
    target: string;
    changes: Record<string, any>;
    timestamp: Date;
    user: {
        firstName: string;
        lastName: string;
        email: string;
        role: UserRole;
    };
}
export declare class AuditLogResponse {
    data: AuditLogEntry[];
    meta: {
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    };
}
export declare class PermissionSet {
    canCreate: boolean;
    canRead: boolean;
    canUpdate: boolean;
    canDelete: boolean;
    canApprove: boolean;
    canReview: boolean;
    custom: Record<string, boolean>;
}
export declare class UserPermissions {
    userId: number;
    permissions: Record<string, PermissionSet>;
}
