import { UsersService } from './users.service';
import { CreateUserDto, UpdateUserDto } from './dto';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    create(createUserDto: CreateUserDto, req: any): Promise<{
        id: number;
        firstName: string;
        lastName: string;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        emailVerified: boolean;
        organizationId: number;
        createdAt: Date;
        updatedAt: Date;
        tempPassword: string;
    }>;
    findAll(req: any): Promise<{
        id: number;
        firstName: string;
        lastName: string;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        emailVerified: boolean;
        organizationId: number;
        organization: {
            id: number;
            organizationName: string;
        };
        createdAt: Date;
        updatedAt: Date;
    }[]>;
    findOne(id: string, req: any): Promise<{
        id: number;
        firstName: string;
        lastName: string;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        emailVerified: boolean;
        organizationId: number;
        organization: {
            id: number;
            organizationName: string;
        };
        createdAt: Date;
        updatedAt: Date;
    }>;
    update(id: string, updateUserDto: UpdateUserDto, req: any): Promise<{
        id: number;
        firstName: string;
        lastName: string;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        emailVerified: boolean;
        organizationId: number;
        organization: {
            id: number;
            organizationName: string;
        };
        createdAt: Date;
        updatedAt: Date;
    }>;
    remove(id: string, req: any): Promise<{
        message: string;
    }>;
}
