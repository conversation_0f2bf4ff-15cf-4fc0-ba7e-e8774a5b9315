"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var FinancingSchemesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinancingSchemesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let FinancingSchemesService = FinancingSchemesService_1 = class FinancingSchemesService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(FinancingSchemesService_1.name);
    }
    async create(createFinancingSchemeDto, userId) {
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can create financing schemes');
            }
            const existing = await this.prisma.financingScheme.findFirst({
                where: {
                    schemeName: createFinancingSchemeDto.schemeName,
                    deleted: false
                }
            });
            if (existing) {
                throw new common_1.ConflictException('Financing scheme with this name already exists');
            }
            const financingScheme = await this.prisma.financingScheme.create({
                data: {
                    schemeName: createFinancingSchemeDto.schemeName,
                    description: createFinancingSchemeDto.description,
                    terms: createFinancingSchemeDto.terms,
                    conditions: createFinancingSchemeDto.conditions
                }
            });
            return financingScheme;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to create financing scheme', error.stack);
            throw new Error('Failed to create financing scheme');
        }
    }
    async findAll() {
        return this.prisma.financingScheme.findMany({
            where: {
                deleted: false
            },
            orderBy: {
                schemeName: 'asc'
            }
        });
    }
    async findOne(id) {
        const financingScheme = await this.prisma.financingScheme.findFirst({
            where: {
                id,
                deleted: false
            }
        });
        if (!financingScheme) {
            throw new common_1.NotFoundException('Financing scheme not found');
        }
        return financingScheme;
    }
    async update(id, updateFinancingSchemeDto, userId) {
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can update financing schemes');
            }
            const existing = await this.prisma.financingScheme.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });
            if (!existing) {
                throw new common_1.NotFoundException('Financing scheme not found');
            }
            if (updateFinancingSchemeDto.schemeName && updateFinancingSchemeDto.schemeName !== existing.schemeName) {
                const nameConflict = await this.prisma.financingScheme.findFirst({
                    where: {
                        schemeName: updateFinancingSchemeDto.schemeName,
                        deleted: false,
                        id: { not: id }
                    }
                });
                if (nameConflict) {
                    throw new common_1.ConflictException('Another financing scheme with this name already exists');
                }
            }
            const updated = await this.prisma.financingScheme.update({
                where: { id },
                data: {
                    schemeName: updateFinancingSchemeDto.schemeName,
                    description: updateFinancingSchemeDto.description,
                    terms: updateFinancingSchemeDto.terms,
                    conditions: updateFinancingSchemeDto.conditions
                }
            });
            return updated;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to update financing scheme', error.stack);
            throw new Error('Failed to update financing scheme');
        }
    }
    async remove(id, userId) {
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can delete financing schemes');
            }
            const existing = await this.prisma.financingScheme.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });
            if (!existing) {
                throw new common_1.NotFoundException('Financing scheme not found');
            }
            await this.prisma.financingScheme.update({
                where: { id },
                data: { deleted: true }
            });
            return { success: true };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to delete financing scheme', error.stack);
            throw new Error('Failed to delete financing scheme');
        }
    }
};
exports.FinancingSchemesService = FinancingSchemesService;
exports.FinancingSchemesService = FinancingSchemesService = FinancingSchemesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], FinancingSchemesService);
//# sourceMappingURL=financing-schemes.service.js.map