"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectWithRelationsDto = exports.ProjectResponseDto = exports.UpdateProjectDto = exports.CreateProjectDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateProjectDto {
}
exports.CreateProjectDto = CreateProjectDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project name',
        example: 'Primary Healthcare Enhancement Project',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project description',
        example: 'A comprehensive project to enhance primary healthcare services in rural areas',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project duration in months',
        example: 12,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateProjectDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Budget type ID',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateProjectDto.prototype, "budgetTypeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Funding unit ID',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateProjectDto.prototype, "fundingUnitId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Funding source ID',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateProjectDto.prototype, "fundingSourceId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Organization ID',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateProjectDto.prototype, "organizationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project document ID',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateProjectDto.prototype, "projectDocumentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'MoU application ID',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateProjectDto.prototype, "mouApplicationId", void 0);
class UpdateProjectDto {
}
exports.UpdateProjectDto = UpdateProjectDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project name',
        example: 'Updated Primary Healthcare Enhancement Project',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateProjectDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project description',
        example: 'Updated project description',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateProjectDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project duration in months',
        example: 18,
        required: false,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateProjectDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Budget type ID',
        example: 2,
        required: false,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateProjectDto.prototype, "budgetTypeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Funding unit ID',
        example: 2,
        required: false,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateProjectDto.prototype, "fundingUnitId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Funding source ID',
        example: 2,
        required: false,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateProjectDto.prototype, "fundingSourceId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project document ID',
        example: 2,
        required: false,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateProjectDto.prototype, "projectDocumentId", void 0);
class ProjectResponseDto {
}
exports.ProjectResponseDto = ProjectResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project ID', example: 1 }),
    __metadata("design:type", Number)
], ProjectResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project name', example: 'Primary Healthcare Enhancement Project' }),
    __metadata("design:type", String)
], ProjectResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project description', example: 'A comprehensive project...', required: false }),
    __metadata("design:type", String)
], ProjectResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project duration in months', example: 12 }),
    __metadata("design:type", Number)
], ProjectResponseDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Budget type ID', example: 1 }),
    __metadata("design:type", Number)
], ProjectResponseDto.prototype, "budgetTypeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Funding unit ID', example: 1 }),
    __metadata("design:type", Number)
], ProjectResponseDto.prototype, "fundingUnitId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Funding source ID', example: 1 }),
    __metadata("design:type", Number)
], ProjectResponseDto.prototype, "fundingSourceId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Organization ID', example: 1 }),
    __metadata("design:type", Number)
], ProjectResponseDto.prototype, "organizationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project document ID', example: 1 }),
    __metadata("design:type", Number)
], ProjectResponseDto.prototype, "projectDocumentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'MoU application ID', example: 1 }),
    __metadata("design:type", Number)
], ProjectResponseDto.prototype, "mouApplicationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation date' }),
    __metadata("design:type", Date)
], ProjectResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update date' }),
    __metadata("design:type", Date)
], ProjectResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Soft delete flag' }),
    __metadata("design:type", Boolean)
], ProjectResponseDto.prototype, "deleted", void 0);
class ProjectWithRelationsDto extends ProjectResponseDto {
}
exports.ProjectWithRelationsDto = ProjectWithRelationsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Budget type information' }),
    __metadata("design:type", Object)
], ProjectWithRelationsDto.prototype, "budgetType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Funding unit information' }),
    __metadata("design:type", Object)
], ProjectWithRelationsDto.prototype, "fundingUnit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Funding source information' }),
    __metadata("design:type", Object)
], ProjectWithRelationsDto.prototype, "fundingSource", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Organization information' }),
    __metadata("design:type", Object)
], ProjectWithRelationsDto.prototype, "organization", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project document information' }),
    __metadata("design:type", Object)
], ProjectWithRelationsDto.prototype, "projectDocument", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'MoU application information' }),
    __metadata("design:type", Object)
], ProjectWithRelationsDto.prototype, "mouApplication", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project activities' }),
    __metadata("design:type", Array)
], ProjectWithRelationsDto.prototype, "activities", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Approval steps' }),
    __metadata("design:type", Array)
], ProjectWithRelationsDto.prototype, "approvalSteps", void 0);
//# sourceMappingURL=create-project.dto.js.map