"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { 
  Plus, 
  Trash2, 
  FolderOpen, 
  Calendar, 
  DollarSign, 
  Info,
  ChevronDown,
  ChevronRight,
  AlertTriangle,
  Check
} from "lucide-react"
import { Step3Data, MouApplication, ProjectFormData, ActivityFormData } from "@/lib/services/mou-application.service"

interface Step3ProjectInformationProps {
  data: Step3Data
  onChange: (data: Step3Data) => void
  application: MouApplication | null
  mouDurationYears: number
}

export function Step3ProjectInformation({ data, onChange, application, mouDurationYears }: Step3ProjectInformationProps) {
  const [expandedProjects, setExpandedProjects] = useState<Set<number>>(new Set([0]))

  const addProject = () => {
    const newProject: ProjectFormData = {
      projectName: "",
      projectDescription: "",
      startDate: "",
      endDate: "",
      totalBudget: undefined,
      currency: "USD",
      activities: [{
        activityName: "",
        description: "",
        timeline: "",
        budgetAllocation: undefined,
        currency: "USD"
      }]
    }

    const newProjects = [...data.projects, newProject]
    onChange({
      ...data,
      projects: newProjects
    })

    // Expand the new project
    const newExpanded = new Set(expandedProjects)
    newExpanded.add(newProjects.length - 1)
    setExpandedProjects(newExpanded)
  }

  const removeProject = (projectIndex: number) => {
    if (data.projects.length > 1) {
      const newProjects = data.projects.filter((_, i) => i !== projectIndex)
      onChange({
        ...data,
        projects: newProjects
      })

      // Update expanded set
      const newExpanded = new Set<number>()
      expandedProjects.forEach(index => {
        if (index < projectIndex) {
          newExpanded.add(index)
        } else if (index > projectIndex) {
          newExpanded.add(index - 1)
        }
      })
      setExpandedProjects(newExpanded)
    }
  }

  const updateProject = (projectIndex: number, field: keyof ProjectFormData, value: any) => {
    const newProjects = [...data.projects]
    newProjects[projectIndex] = {
      ...newProjects[projectIndex],
      [field]: value
    }
    onChange({
      ...data,
      projects: newProjects
    })
  }

  const addActivity = (projectIndex: number) => {
    const newActivity: ActivityFormData = {
      activityName: "",
      description: "",
      timeline: "",
      budgetAllocation: undefined,
      currency: "USD"
    }

    const newProjects = [...data.projects]
    newProjects[projectIndex] = {
      ...newProjects[projectIndex],
      activities: [...newProjects[projectIndex].activities, newActivity]
    }
    onChange({
      ...data,
      projects: newProjects
    })
  }

  const removeActivity = (projectIndex: number, activityIndex: number) => {
    const project = data.projects[projectIndex]
    if (project.activities.length > 1) {
      const newProjects = [...data.projects]
      newProjects[projectIndex] = {
        ...newProjects[projectIndex],
        activities: project.activities.filter((_, i) => i !== activityIndex)
      }
      onChange({
        ...data,
        projects: newProjects
      })
    }
  }

  const updateActivity = (projectIndex: number, activityIndex: number, field: keyof ActivityFormData, value: any) => {
    const newProjects = [...data.projects]
    const newActivities = [...newProjects[projectIndex].activities]
    newActivities[activityIndex] = {
      ...newActivities[activityIndex],
      [field]: value
    }
    newProjects[projectIndex] = {
      ...newProjects[projectIndex],
      activities: newActivities
    }
    onChange({
      ...data,
      projects: newProjects
    })
  }

  const toggleProjectExpansion = (projectIndex: number) => {
    const newExpanded = new Set(expandedProjects)
    if (newExpanded.has(projectIndex)) {
      newExpanded.delete(projectIndex)
    } else {
      newExpanded.add(projectIndex)
    }
    setExpandedProjects(newExpanded)
  }

  const validateProjectDates = (project: ProjectFormData): string[] => {
    const errors: string[] = []
    
    if (project.startDate && project.endDate) {
      const startDate = new Date(project.startDate)
      const endDate = new Date(project.endDate)
      
      if (startDate >= endDate) {
        errors.push("End date must be after start date")
      }
      
      // Check if project duration exceeds MoU duration
      const projectDurationYears = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 365.25)
      if (projectDurationYears > mouDurationYears) {
        errors.push(`Project duration cannot exceed MoU duration of ${mouDurationYears} years`)
      }
    }
    
    return errors
  }

  const getProjectCompletionStatus = (project: ProjectFormData) => {
    const requiredFields = [
      project.projectName.trim(),
      project.startDate,
      project.endDate,
      project.totalBudget
    ]
    const completedFields = requiredFields.filter(Boolean).length
    const hasValidActivities = project.activities.some(a => a.activityName.trim())
    
    return {
      completed: completedFields,
      total: requiredFields.length + (hasValidActivities ? 1 : 0),
      isValid: completedFields === requiredFields.length && hasValidActivities
    }
  }

  const getTotalBudget = () => {
    return data.projects.reduce((total, project) => {
      return total + (project.totalBudget || 0)
    }, 0)
  }

  // Initialize with one project if none exist
  if (data.projects.length === 0) {
    const initialProject: ProjectFormData = {
      projectName: "",
      projectDescription: "",
      startDate: "",
      endDate: "",
      totalBudget: undefined,
      currency: "USD",
      activities: [{
        activityName: "",
        description: "",
        timeline: "",
        budgetAllocation: undefined,
        currency: "USD"
      }]
    }
    
    onChange({
      ...data,
      projects: [initialProject]
    })
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-cyan-900 mb-2">Project Information</h3>
        <p className="text-muted-foreground">
          Define the projects and activities that will be implemented under this MoU.
        </p>
      </div>

      {/* Project Summary */}
      <Card className="bg-cyan-50 border-cyan-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-cyan-900">Project Summary</h4>
              <p className="text-sm text-cyan-800">
                {data.projects.length} project{data.projects.length !== 1 ? 's' : ''} • 
                Total Budget: ${getTotalBudget().toLocaleString()} USD
              </p>
            </div>
            <Button onClick={addProject} size="sm" className="bg-cyan-600 hover:bg-cyan-700">
              <Plus className="h-4 w-4 mr-1" />
              Add Project
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Projects */}
      <div className="space-y-4">
        {data.projects.map((project, projectIndex) => {
          const isExpanded = expandedProjects.has(projectIndex)
          const status = getProjectCompletionStatus(project)
          const dateErrors = validateProjectDates(project)

          return (
            <Card key={projectIndex} className="border-2">
              <Collapsible open={isExpanded} onOpenChange={() => toggleProjectExpansion(projectIndex)}>
                <CollapsibleTrigger asChild>
                  <CardHeader className="cursor-pointer hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <ChevronRight className="h-4 w-4 text-muted-foreground" />
                        )}
                        <FolderOpen className="h-5 w-5 text-cyan-600" />
                        <div>
                          <CardTitle className="text-base">
                            {project.projectName || `Project ${projectIndex + 1}`}
                          </CardTitle>
                          <CardDescription>
                            {project.activities.length} activit{project.activities.length !== 1 ? 'ies' : 'y'} • 
                            {project.totalBudget ? `$${project.totalBudget.toLocaleString()} ${project.currency}` : 'No budget set'}
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={status.isValid ? "default" : "secondary"} className={status.isValid ? "bg-green-100 text-green-800" : ""}>
                          {status.completed}/{status.total} completed
                        </Badge>
                        {data.projects.length > 1 && (
                          <Button
                            onClick={(e) => {
                              e.stopPropagation()
                              removeProject(projectIndex)
                            }}
                            size="sm"
                            variant="ghost"
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <CardContent className="space-y-6">
                    {/* Project Basic Information */}
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor={`projectName-${projectIndex}`}>Project Name *</Label>
                        <Input
                          id={`projectName-${projectIndex}`}
                          value={project.projectName}
                          onChange={(e) => updateProject(projectIndex, 'projectName', e.target.value)}
                          placeholder="Enter project name"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`currency-${projectIndex}`}>Currency</Label>
                        <Select
                          value={project.currency}
                          onValueChange={(value) => updateProject(projectIndex, 'currency', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="USD">USD</SelectItem>
                            <SelectItem value="RWF">RWF</SelectItem>
                            <SelectItem value="EUR">EUR</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`projectDescription-${projectIndex}`}>Project Description</Label>
                      <Textarea
                        id={`projectDescription-${projectIndex}`}
                        value={project.projectDescription}
                        onChange={(e) => updateProject(projectIndex, 'projectDescription', e.target.value)}
                        placeholder="Describe the project objectives, scope, and expected outcomes..."
                        rows={3}
                      />
                    </div>

                    {/* Project Timeline and Budget */}
                    <div className="grid gap-4 md:grid-cols-3">
                      <div className="space-y-2">
                        <Label htmlFor={`startDate-${projectIndex}`}>Start Date *</Label>
                        <Input
                          id={`startDate-${projectIndex}`}
                          type="date"
                          value={project.startDate}
                          onChange={(e) => updateProject(projectIndex, 'startDate', e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`endDate-${projectIndex}`}>End Date *</Label>
                        <Input
                          id={`endDate-${projectIndex}`}
                          type="date"
                          value={project.endDate}
                          onChange={(e) => updateProject(projectIndex, 'endDate', e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`totalBudget-${projectIndex}`}>Total Budget *</Label>
                        <Input
                          id={`totalBudget-${projectIndex}`}
                          type="number"
                          value={project.totalBudget || ""}
                          onChange={(e) => updateProject(projectIndex, 'totalBudget', e.target.value ? Number(e.target.value) : undefined)}
                          placeholder="0.00"
                          min="0"
                          step="0.01"
                        />
                      </div>
                    </div>

                    {/* Date Validation Errors */}
                    {dateErrors.length > 0 && (
                      <Alert variant="destructive">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          <ul className="list-disc list-inside">
                            {dateErrors.map((error, index) => (
                              <li key={index}>{error}</li>
                            ))}
                          </ul>
                        </AlertDescription>
                      </Alert>
                    )}

                    {/* Project Activities */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">Project Activities</h4>
                        <Button
                          onClick={() => addActivity(projectIndex)}
                          size="sm"
                          variant="outline"
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add Activity
                        </Button>
                      </div>

                      {project.activities.map((activity, activityIndex) => (
                        <div key={activityIndex} className="border rounded-lg p-4 space-y-4 bg-gray-50">
                          <div className="flex items-center justify-between">
                            <h5 className="font-medium text-sm">Activity {activityIndex + 1}</h5>
                            {project.activities.length > 1 && (
                              <Button
                                onClick={() => removeActivity(projectIndex, activityIndex)}
                                size="sm"
                                variant="ghost"
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            )}
                          </div>

                          <div className="grid gap-4 md:grid-cols-2">
                            <div className="space-y-2">
                              <Label htmlFor={`activityName-${projectIndex}-${activityIndex}`}>Activity Name *</Label>
                              <Input
                                id={`activityName-${projectIndex}-${activityIndex}`}
                                value={activity.activityName}
                                onChange={(e) => updateActivity(projectIndex, activityIndex, 'activityName', e.target.value)}
                                placeholder="Enter activity name"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`timeline-${projectIndex}-${activityIndex}`}>Timeline</Label>
                              <Input
                                id={`timeline-${projectIndex}-${activityIndex}`}
                                value={activity.timeline}
                                onChange={(e) => updateActivity(projectIndex, activityIndex, 'timeline', e.target.value)}
                                placeholder="e.g., Q1 2024, 6 months"
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor={`activityDescription-${projectIndex}-${activityIndex}`}>Description</Label>
                            <Textarea
                              id={`activityDescription-${projectIndex}-${activityIndex}`}
                              value={activity.description}
                              onChange={(e) => updateActivity(projectIndex, activityIndex, 'description', e.target.value)}
                              placeholder="Describe what this activity involves..."
                              rows={2}
                            />
                          </div>

                          <div className="grid gap-4 md:grid-cols-2">
                            <div className="space-y-2">
                              <Label htmlFor={`budgetAllocation-${projectIndex}-${activityIndex}`}>Budget Allocation</Label>
                              <Input
                                id={`budgetAllocation-${projectIndex}-${activityIndex}`}
                                type="number"
                                value={activity.budgetAllocation || ""}
                                onChange={(e) => updateActivity(projectIndex, activityIndex, 'budgetAllocation', e.target.value ? Number(e.target.value) : undefined)}
                                placeholder="0.00"
                                min="0"
                                step="0.01"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`activityCurrency-${projectIndex}-${activityIndex}`}>Currency</Label>
                              <Select
                                value={activity.currency}
                                onValueChange={(value) => updateActivity(projectIndex, activityIndex, 'currency', value)}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="USD">USD</SelectItem>
                                  <SelectItem value="RWF">RWF</SelectItem>
                                  <SelectItem value="EUR">EUR</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          )
        })}
      </div>

      {/* Validation Summary */}
      <Card className="bg-cyan-50 border-cyan-200">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-cyan-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-cyan-900">Step 3 Requirements</h4>
              <ul className="text-sm text-cyan-800 mt-2 space-y-1">
                <li className={`flex items-center gap-2 ${data.projects.length > 0 ? 'text-green-700' : ''}`}>
                  {data.projects.length > 0 ? '✓' : '○'} At least one project defined
                </li>
                <li className={`flex items-center gap-2 ${data.projects.some(p => p.projectName.trim() && p.startDate && p.endDate && p.totalBudget) ? 'text-green-700' : ''}`}>
                  {data.projects.some(p => p.projectName.trim() && p.startDate && p.endDate && p.totalBudget) ? '✓' : '○'} Project details completed
                </li>
                <li className={`flex items-center gap-2 ${data.projects.some(p => p.activities.some(a => a.activityName.trim())) ? 'text-green-700' : ''}`}>
                  {data.projects.some(p => p.activities.some(a => a.activityName.trim())) ? '✓' : '○'} At least one activity per project
                </li>
                <li className={`flex items-center gap-2 ${data.projects.every(p => validateProjectDates(p).length === 0) ? 'text-green-700' : ''}`}>
                  {data.projects.every(p => validateProjectDates(p).length === 0) ? '✓' : '○'} Valid project timelines
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
