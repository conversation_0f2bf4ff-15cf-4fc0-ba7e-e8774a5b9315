import {
  Controller,
  Get,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { AdminDashboardService } from './admin-dashboard.service';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { RolesGuard, Roles } from '../auth/guards/roles.guard';
import { UserRole } from '@prisma/client';

@Controller('admin/dashboard')
@UseGuards(JwtGuard, RolesGuard)
@Roles(UserRole.ADMIN)
export class AdminDashboardController {
  constructor(private readonly dashboardService: AdminDashboardService) {}

  @Get('overview')
  async getDashboardOverview() {
    const [
      userStats,
      activityMetrics,
      systemHealth,
      performanceMetrics,
    ] = await Promise.all([
      this.dashboardService.getUserStatistics(),
      this.dashboardService.getActivityMetrics(),
      this.dashboardService.getSystemHealth(),
      this.dashboardService.getPerformanceMetrics(),
    ]);

    return {
      userStats,
      activityMetrics,
      systemHealth,
      performanceMetrics,
    };
  }

  @Get('users/statistics')
  async getUserStatistics() {
    return this.dashboardService.getUserStatistics();
  }

  @Get('activity/metrics')
  async getActivityMetrics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    return this.dashboardService.getActivityMetrics(start, end);
  }

  @Get('system/health')
  async getSystemHealth() {
    return this.dashboardService.getSystemHealth();
  }

  @Get('performance/metrics')
  async getPerformanceMetrics() {
    return this.dashboardService.getPerformanceMetrics();
  }

  @Get('reviews/statistics')
  async getReviewStatistics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    const metrics = await this.dashboardService.getActivityMetrics(start, end);
    return metrics.reviewActivity;
  }

  @Get('approvals/statistics')
  async getApprovalStatistics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    const metrics = await this.dashboardService.getActivityMetrics(start, end);
    return metrics.approvalActivity;
  }

  @Get('users/active')
  async getActiveUsers(
    @Query('period') period: 'daily' | 'weekly' | 'monthly' = 'daily',
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    const metrics = await this.dashboardService.getActivityMetrics(start, end);
    return metrics.loginActivity;
  }

  @Get('applications/throughput')
  async getApplicationThroughput(
    @Query('period') period: 'daily' | 'weekly' | 'monthly' = 'daily',
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const metrics = await this.dashboardService.getPerformanceMetrics();
    return metrics.applicationThroughput;
  }

  @Get('users/productivity')
  async getUserProductivity(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('department') department?: string,
    @Query('role') role?: string,
  ) {
    const metrics = await this.dashboardService.getPerformanceMetrics();
    return metrics.userProductivity;
  }

  @Get('system/errors')
  async getSystemErrors(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('severity') severity?: string,
  ) {
    const health = await this.dashboardService.getSystemHealth();
    return health.recentErrors;
  }

  @Get('departments/performance')
  async getDepartmentPerformance(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const metrics = await this.dashboardService.getPerformanceMetrics();
    return {
      reviewTimes: metrics.averageReviewTime,
      approvalTimes: metrics.averageApprovalTime,
      throughput: metrics.applicationThroughput,
    };
  }

  @Get('roles/activity')
  async getRoleActivity(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    const [userStats, activityMetrics] = await Promise.all([
      this.dashboardService.getUserStatistics(),
      this.dashboardService.getActivityMetrics(start, end),
    ]);

    return {
      distribution: userStats.roleDistribution,
      activity: activityMetrics.userActions,
    };
  }

  @Get('notifications/statistics')
  async getNotificationStatistics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    // This would be implemented in the service
    // Return notification delivery stats, read rates, etc.
    return {
      // Implementation pending
    };
  }

  @Get('audit/summary')
  async getAuditSummary(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    // This would be implemented in the service
    // Return summary of audit logs
    return {
      // Implementation pending
    };
  }
}
