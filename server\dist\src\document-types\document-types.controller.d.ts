import { DocumentTypesService } from './document-types.service';
import { CreateDocumentTypeDto, UpdateDocumentTypeDto } from './dto/create-document-type.dto';
export declare class DocumentTypesController {
    private readonly documentTypesService;
    constructor(documentTypesService: DocumentTypesService);
    create(createDocumentTypeDto: CreateDocumentTypeDto): Promise<{
        id: number;
        typeName: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    findAll(): Promise<{
        id: number;
        typeName: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }[]>;
    findOne(id: number): Promise<{
        documents: ({
            organization: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                organizationName: string;
                organizationPhoneNumber: string;
                organizationEmail: string;
                organizationWebsite: string | null;
                homeCountryRepresentative: string;
                rwandaRepresentative: string;
                organizationRgbNumber: string | null;
                organizationTypeId: number;
                centralLevelInstitutionId: number | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            projectId: number | null;
            documentTypeId: number;
        })[];
    } & {
        id: number;
        typeName: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    update(id: number, updateDocumentTypeDto: UpdateDocumentTypeDto): Promise<{
        id: number;
        typeName: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
}
