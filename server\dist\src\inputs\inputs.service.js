"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InputsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let InputsService = class InputsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createInput(createInputDto) {
        try {
            const inputSubclass = await this.prisma.inputSubclass.findUnique({
                where: { id: createInputDto.inputSubclassId },
            });
            if (!inputSubclass) {
                throw new common_1.NotFoundException(`Input subclass with ID ${createInputDto.inputSubclassId} not found`);
            }
            const input = await this.prisma.input.create({
                data: createInputDto,
                include: {
                    inputSubclass: true,
                    activities: {
                        include: {
                            project: true,
                        },
                    },
                },
            });
            return input;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2003') {
                    throw new common_1.BadRequestException('Invalid input subclass reference');
                }
            }
            throw error;
        }
    }
    async findAllInputs(page = 1, limit = 10, inputSubclassId) {
        const skip = (page - 1) * limit;
        const where = {
            deleted: false,
            ...(inputSubclassId && { inputSubclassId }),
        };
        const [inputs, total] = await Promise.all([
            this.prisma.input.findMany({
                where,
                skip,
                take: limit,
                include: {
                    inputSubclass: true,
                    activities: {
                        include: {
                            project: true,
                        },
                    },
                },
                orderBy: {
                    name: 'asc',
                },
            }),
            this.prisma.input.count({ where }),
        ]);
        return {
            data: inputs,
            meta: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findOneInput(id) {
        const input = await this.prisma.input.findFirst({
            where: {
                id,
                deleted: false,
            },
            include: {
                inputSubclass: true,
                activities: {
                    include: {
                        project: {
                            include: {
                                mouApplication: true,
                            },
                        },
                        domainIntervention: true,
                    },
                },
            },
        });
        if (!input) {
            throw new common_1.NotFoundException(`Input with ID ${id} not found`);
        }
        return input;
    }
    async updateInput(id, updateInputDto) {
        const existingInput = await this.prisma.input.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingInput) {
            throw new common_1.NotFoundException(`Input with ID ${id} not found`);
        }
        if (updateInputDto.inputSubclassId) {
            const inputSubclass = await this.prisma.inputSubclass.findUnique({
                where: { id: updateInputDto.inputSubclassId },
            });
            if (!inputSubclass) {
                throw new common_1.NotFoundException(`Input subclass with ID ${updateInputDto.inputSubclassId} not found`);
            }
        }
        try {
            const updatedInput = await this.prisma.input.update({
                where: { id },
                data: updateInputDto,
                include: {
                    inputSubclass: true,
                    activities: {
                        include: {
                            project: true,
                        },
                    },
                },
            });
            return updatedInput;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2003') {
                    throw new common_1.BadRequestException('Invalid input subclass reference');
                }
            }
            throw error;
        }
    }
    async removeInput(id) {
        const existingInput = await this.prisma.input.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingInput) {
            throw new common_1.NotFoundException(`Input with ID ${id} not found`);
        }
        const activitiesUsingInput = await this.prisma.activity.findMany({
            where: {
                inputId: id,
                deleted: false,
            },
        });
        if (activitiesUsingInput.length > 0) {
            throw new common_1.ConflictException('Cannot delete input that is being used by activities');
        }
        await this.prisma.input.update({
            where: { id },
            data: { deleted: true },
        });
        return { message: 'Input deleted successfully' };
    }
    async createInputSubclass(createInputSubclassDto) {
        try {
            const inputSubclass = await this.prisma.inputSubclass.create({
                data: createInputSubclassDto,
                include: {
                    input: true,
                },
            });
            return inputSubclass;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException('Subclass ID must be unique');
                }
            }
            throw error;
        }
    }
    async findAllInputSubclasses() {
        const inputSubclasses = await this.prisma.inputSubclass.findMany({
            where: {
                deleted: false,
            },
            include: {
                input: true,
            },
            orderBy: {
                subclassId: 'asc',
            },
        });
        return inputSubclasses;
    }
    async findOneInputSubclass(id) {
        const inputSubclass = await this.prisma.inputSubclass.findFirst({
            where: {
                id,
                deleted: false,
            },
            include: {
                input: {
                    include: {
                        activities: {
                            include: {
                                project: true,
                            },
                        },
                    },
                },
            },
        });
        if (!inputSubclass) {
            throw new common_1.NotFoundException(`Input subclass with ID ${id} not found`);
        }
        return inputSubclass;
    }
    async updateInputSubclass(id, updateInputSubclassDto) {
        const existingInputSubclass = await this.prisma.inputSubclass.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingInputSubclass) {
            throw new common_1.NotFoundException(`Input subclass with ID ${id} not found`);
        }
        try {
            const updatedInputSubclass = await this.prisma.inputSubclass.update({
                where: { id },
                data: updateInputSubclassDto,
                include: {
                    input: true,
                },
            });
            return updatedInputSubclass;
        }
        catch (error) {
            throw error;
        }
    }
    async removeInputSubclass(id) {
        const existingInputSubclass = await this.prisma.inputSubclass.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingInputSubclass) {
            throw new common_1.NotFoundException(`Input subclass with ID ${id} not found`);
        }
        const inputsUsingSubclass = await this.prisma.input.findMany({
            where: {
                inputSubclass: {
                    some: { id: id }
                },
                deleted: false,
            },
        });
        if (inputsUsingSubclass.length > 0) {
            throw new common_1.ConflictException('Cannot delete input subclass that is being used by inputs');
        }
        await this.prisma.inputSubclass.update({
            where: { id },
            data: { deleted: true },
        });
        return { message: 'Input subclass deleted successfully' };
    }
    async getInputsBySubclass(inputSubclassId) {
        const inputs = await this.prisma.input.findMany({
            where: {
                inputSubclass: {
                    some: { id: inputSubclassId }
                },
                deleted: false,
            },
            include: {
                inputSubclass: true,
            },
            orderBy: {
                name: 'asc',
            },
        });
        return inputs;
    }
};
exports.InputsService = InputsService;
exports.InputsService = InputsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], InputsService);
//# sourceMappingURL=inputs.service.js.map