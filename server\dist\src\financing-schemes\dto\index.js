"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinancingSchemeResponseDto = exports.UpdateFinancingSchemeDto = exports.CreateFinancingSchemeDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateFinancingSchemeDto {
}
exports.CreateFinancingSchemeDto = CreateFinancingSchemeDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The name of the financing scheme' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateFinancingSchemeDto.prototype, "schemeName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Description of the financing scheme', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateFinancingSchemeDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Terms of the financing scheme', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateFinancingSchemeDto.prototype, "terms", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Conditions of the financing scheme', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateFinancingSchemeDto.prototype, "conditions", void 0);
class UpdateFinancingSchemeDto {
}
exports.UpdateFinancingSchemeDto = UpdateFinancingSchemeDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The name of the financing scheme', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateFinancingSchemeDto.prototype, "schemeName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Description of the financing scheme', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateFinancingSchemeDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Terms of the financing scheme', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateFinancingSchemeDto.prototype, "terms", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Conditions of the financing scheme', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateFinancingSchemeDto.prototype, "conditions", void 0);
class FinancingSchemeResponseDto {
}
exports.FinancingSchemeResponseDto = FinancingSchemeResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], FinancingSchemeResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The name of the financing scheme' }),
    __metadata("design:type", String)
], FinancingSchemeResponseDto.prototype, "schemeName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Description of the financing scheme' }),
    __metadata("design:type", String)
], FinancingSchemeResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Terms of the financing scheme' }),
    __metadata("design:type", String)
], FinancingSchemeResponseDto.prototype, "terms", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Conditions of the financing scheme' }),
    __metadata("design:type", String)
], FinancingSchemeResponseDto.prototype, "conditions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'When the financing scheme was created' }),
    __metadata("design:type", Date)
], FinancingSchemeResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'When the financing scheme was last updated' }),
    __metadata("design:type", Date)
], FinancingSchemeResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether the financing scheme has been soft deleted' }),
    __metadata("design:type", Boolean)
], FinancingSchemeResponseDto.prototype, "deleted", void 0);
//# sourceMappingURL=index.js.map