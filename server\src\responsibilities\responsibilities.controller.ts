import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ResponsibilitiesService } from './responsibilities.service';
import { CreateResponsibilityDto, UpdateResponsibilityDto, ResponsibilityResponseDto } from './dto/create-responsibility.dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { RolesGuard } from '../auth/guard/roles.guard';
import { Roles } from '../auth/decorator/roles.decorator';
import { UserRole } from '../auth/dto';

@ApiTags('Responsibilities')
@ApiBearerAuth()
@UseGuards(JwtGuard, RolesGuard)
@Controller('responsibilities')
export class ResponsibilitiesController {
  constructor(private readonly responsibilitiesService: ResponsibilitiesService) {}

  @Post()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new responsibility' })
  @ApiResponse({
    status: 201,
    description: 'Responsibility created successfully',
    type: ResponsibilityResponseDto,
  })
  @ApiResponse({ status: 409, description: 'Responsibility name already exists' })
  async create(@Body() createResponsibilityDto: CreateResponsibilityDto) {
    return this.responsibilitiesService.create(createResponsibilityDto);
  }

  @Get()
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get all responsibilities' })
  @ApiResponse({
    status: 200,
    description: 'List of responsibilities',
    type: [ResponsibilityResponseDto],
  })
  async findAll() {
    return this.responsibilitiesService.findAll();
  }

  @Get(':id')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get a responsibility by ID' })
  @ApiResponse({
    status: 200,
    description: 'Responsibility details',
    type: ResponsibilityResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Responsibility not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.responsibilitiesService.findOne(id);
  }

  @Patch(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Update a responsibility' })
  @ApiResponse({
    status: 200,
    description: 'Responsibility updated successfully',
    type: ResponsibilityResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Responsibility not found' })
  @ApiResponse({ status: 409, description: 'Responsibility name already exists' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateResponsibilityDto: UpdateResponsibilityDto,
  ) {
    return this.responsibilitiesService.update(id, updateResponsibilityDto);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete a responsibility (soft delete)' })
  @ApiResponse({
    status: 200,
    description: 'Responsibility deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Responsibility not found' })
  @ApiResponse({ status: 409, description: 'Responsibility is being used by parties' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.responsibilitiesService.remove(id);
  }
}
