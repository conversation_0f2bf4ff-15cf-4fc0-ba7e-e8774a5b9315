"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentTypesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let DocumentTypesService = class DocumentTypesService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createDocumentTypeDto) {
        try {
            const documentType = await this.prisma.documentType.create({
                data: createDocumentTypeDto,
            });
            return documentType;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException('Document type name must be unique');
                }
            }
            throw error;
        }
    }
    async findAll() {
        const documentTypes = await this.prisma.documentType.findMany({
            where: {
                deleted: false,
            },
            orderBy: {
                typeName: 'asc',
            },
        });
        return documentTypes;
    }
    async findOne(id) {
        const documentType = await this.prisma.documentType.findFirst({
            where: {
                id,
                deleted: false,
            },
            include: {
                documents: {
                    include: {
                        organization: true,
                    },
                },
            },
        });
        if (!documentType) {
            throw new common_1.NotFoundException(`Document type with ID ${id} not found`);
        }
        return documentType;
    }
    async update(id, updateDocumentTypeDto) {
        const existingDocumentType = await this.prisma.documentType.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingDocumentType) {
            throw new common_1.NotFoundException(`Document type with ID ${id} not found`);
        }
        try {
            const updatedDocumentType = await this.prisma.documentType.update({
                where: { id },
                data: updateDocumentTypeDto,
            });
            return updatedDocumentType;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException('Document type name must be unique');
                }
            }
            throw error;
        }
    }
    async remove(id) {
        const existingDocumentType = await this.prisma.documentType.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingDocumentType) {
            throw new common_1.NotFoundException(`Document type with ID ${id} not found`);
        }
        const documentsUsingType = await this.prisma.document.findMany({
            where: {
                documentTypeId: id,
                deleted: false,
            },
        });
        if (documentsUsingType.length > 0) {
            throw new common_1.ConflictException('Cannot delete document type that is being used by documents');
        }
        await this.prisma.documentType.update({
            where: { id },
            data: { deleted: true },
        });
        return { message: 'Document type deleted successfully' };
    }
};
exports.DocumentTypesService = DocumentTypesService;
exports.DocumentTypesService = DocumentTypesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], DocumentTypesService);
//# sourceMappingURL=document-types.service.js.map