import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ObjectivesService } from './objectives.service';
import { CreateObjectiveDto, UpdateObjectiveDto, ObjectiveResponseDto } from './dto/create-objective.dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { RolesGuard } from '../auth/guard/roles.guard';
import { Roles } from '../auth/decorator/roles.decorator';
import { UserRole } from '../auth/dto';

@ApiTags('Objectives')
@ApiBearerAuth()
@UseGuards(JwtGuard, RolesGuard)
@Controller('objectives')
export class ObjectivesController {
  constructor(private readonly objectivesService: ObjectivesService) {}

  @Post()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new objective' })
  @ApiResponse({
    status: 201,
    description: 'Objective created successfully',
    type: ObjectiveResponseDto,
  })
  @ApiResponse({ status: 409, description: 'Objective name already exists' })
  async create(@Body() createObjectiveDto: CreateObjectiveDto) {
    return this.objectivesService.create(createObjectiveDto);
  }

  @Get()
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get all objectives' })
  @ApiResponse({
    status: 200,
    description: 'List of objectives',
    type: [ObjectiveResponseDto],
  })
  async findAll() {
    return this.objectivesService.findAll();
  }

  @Get(':id')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get an objective by ID' })
  @ApiResponse({
    status: 200,
    description: 'Objective details',
    type: ObjectiveResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Objective not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.objectivesService.findOne(id);
  }

  @Patch(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Update an objective' })
  @ApiResponse({
    status: 200,
    description: 'Objective updated successfully',
    type: ObjectiveResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Objective not found' })
  @ApiResponse({ status: 409, description: 'Objective name already exists' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateObjectiveDto: UpdateObjectiveDto,
  ) {
    return this.objectivesService.update(id, updateObjectiveDto);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete an objective (soft delete)' })
  @ApiResponse({
    status: 200,
    description: 'Objective deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Objective not found' })
  @ApiResponse({ status: 409, description: 'Objective is being used by parties' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.objectivesService.remove(id);
  }
}
