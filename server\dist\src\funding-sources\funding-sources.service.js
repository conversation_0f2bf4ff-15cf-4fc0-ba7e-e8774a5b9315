"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var FundingSourcesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FundingSourcesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let FundingSourcesService = FundingSourcesService_1 = class FundingSourcesService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(FundingSourcesService_1.name);
    }
    async create(createFundingSourceDto, userId) {
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can create funding sources');
            }
            const existing = await this.prisma.fundingSource.findFirst({
                where: {
                    sourceName: createFundingSourceDto.sourceName,
                    deleted: false
                }
            });
            if (existing) {
                throw new common_1.ConflictException('Funding source with this name already exists');
            }
            const fundingSource = await this.prisma.fundingSource.create({
                data: {
                    sourceName: createFundingSourceDto.sourceName
                }
            });
            return fundingSource;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to create funding source', error.stack);
            throw new Error('Failed to create funding source');
        }
    }
    async findAll() {
        return this.prisma.fundingSource.findMany({
            where: {
                deleted: false
            },
            orderBy: {
                sourceName: 'asc'
            }
        });
    }
    async findOne(id) {
        const fundingSource = await this.prisma.fundingSource.findFirst({
            where: {
                id,
                deleted: false
            }
        });
        if (!fundingSource) {
            throw new common_1.NotFoundException('Funding source not found');
        }
        return fundingSource;
    }
    async update(id, updateFundingSourceDto, userId) {
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can update funding sources');
            }
            const existing = await this.prisma.fundingSource.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });
            if (!existing) {
                throw new common_1.NotFoundException('Funding source not found');
            }
            if (updateFundingSourceDto.sourceName && updateFundingSourceDto.sourceName !== existing.sourceName) {
                const nameConflict = await this.prisma.fundingSource.findFirst({
                    where: {
                        sourceName: updateFundingSourceDto.sourceName,
                        deleted: false,
                        id: { not: id }
                    }
                });
                if (nameConflict) {
                    throw new common_1.ConflictException('Another funding source with this name already exists');
                }
            }
            const updated = await this.prisma.fundingSource.update({
                where: { id },
                data: {
                    sourceName: updateFundingSourceDto.sourceName
                }
            });
            return updated;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to update funding source', error.stack);
            throw new Error('Failed to update funding source');
        }
    }
    async remove(id, userId) {
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can delete funding sources');
            }
            const existing = await this.prisma.fundingSource.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });
            if (!existing) {
                throw new common_1.NotFoundException('Funding source not found');
            }
            const inUse = await this.prisma.project.findFirst({
                where: {
                    fundingSourceId: id,
                    deleted: false
                }
            });
            if (inUse) {
                throw new common_1.ConflictException('Cannot delete funding source that is being used by projects');
            }
            await this.prisma.fundingSource.update({
                where: { id },
                data: { deleted: true }
            });
            return { success: true };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to delete funding source', error.stack);
            throw new Error('Failed to delete funding source');
        }
    }
};
exports.FundingSourcesService = FundingSourcesService;
exports.FundingSourcesService = FundingSourcesService = FundingSourcesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], FundingSourcesService);
//# sourceMappingURL=funding-sources.service.js.map