import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateObjectiveDto, UpdateObjectiveDto } from './dto/create-objective.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class ObjectivesService {
  constructor(private prisma: PrismaService) {}

  async create(createObjectiveDto: CreateObjectiveDto) {
    try {
      const objective = await this.prisma.objective.create({
        data: createObjectiveDto,
      });

      return objective;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Objective name must be unique');
        }
      }
      throw error;
    }
  }

  async findAll() {
    const objectives = await this.prisma.objective.findMany({
      where: {
        deleted: false,
      },
      orderBy: {
        name: 'asc',
      },
    });

    return objectives;
  }

  async findOne(id: number) {
    const objective = await this.prisma.objective.findFirst({
      where: {
        id,
        deleted: false,
      },
      include: {
        parties: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!objective) {
      throw new NotFoundException(`Objective with ID ${id} not found`);
    }

    return objective;
  }

  async update(id: number, updateObjectiveDto: UpdateObjectiveDto) {
    // Check if objective exists
    const existingObjective = await this.prisma.objective.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingObjective) {
      throw new NotFoundException(`Objective with ID ${id} not found`);
    }

    try {
      const updatedObjective = await this.prisma.objective.update({
        where: { id },
        data: updateObjectiveDto,
      });

      return updatedObjective;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Objective name must be unique');
        }
      }
      throw error;
    }
  }

  async remove(id: number) {
    // Check if objective exists
    const existingObjective = await this.prisma.objective.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingObjective) {
      throw new NotFoundException(`Objective with ID ${id} not found`);
    }

    // Check if objective is being used by parties
    const partiesUsingObjective = await this.prisma.party.findMany({
      where: {
        objectiveId: id,
        deleted: false,
      },
    });

    if (partiesUsingObjective.length > 0) {
      throw new ConflictException('Cannot delete objective that is being used by parties');
    }

    // Soft delete
    await this.prisma.objective.update({
      where: { id },
      data: { deleted: true },
    });

    return { message: 'Objective deleted successfully' };
  }
}
