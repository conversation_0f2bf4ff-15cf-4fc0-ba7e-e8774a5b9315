import { useCallback, useEffect, useState } from 'react'
import { useMouStore } from '@/store/mou-store'
import { useToast } from '@/hooks/use-toast'

export const useDraftManager = () => {
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true)
  const { toast } = useToast()

  // Get all store data
  const {
    currentStep,
    organizationName,
    mouDuration,
    durationReason,
    parties,
    projects,
    activities,
    documents
  } = useMouStore()

  // Save draft to server
  const saveDraft = useCallback(async (showToast = false) => {
    if (isSaving) return false

    setIsSaving(true)
    
    try {
      const draftData = {
        currentStep,
        mouDetails: {
          organizationName,
          mouDuration,
          durationReason
        },
        parties,
        projects,
        activities,
        documents
      }

      const response = await fetch('/api/mou-applications/draft', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(draftData)
      })

      if (!response.ok) {
        throw new Error('Failed to save draft')
      }

      const result = await response.json()
      
      if (result.success) {
        setLastSaved(new Date())
        
        if (showToast) {
          toast({
            title: "Draft Saved",
            description: "Your MoU application has been saved as draft.",
            duration: 3000
          })
        }
        
        return true
      } else {
        throw new Error(result.error || 'Failed to save draft')
      }

    } catch (error) {
      console.error('Error saving draft:', error)
      
      if (showToast) {
        toast({
          variant: "destructive",
          title: "Save Failed",
          description: "Failed to save draft. Please try again.",
          duration: 5000
        })
      }
      
      return false
    } finally {
      setIsSaving(false)
    }
  }, [
    isSaving,
    currentStep,
    organizationName,
    mouDuration,
    durationReason,
    parties,
    projects,
    activities,
    documents,
    toast
  ])

  // Load draft from server
  const loadDraft = useCallback(async () => {
    try {
      const response = await fetch('/api/mou-applications/draft')
      
      if (!response.ok) {
        throw new Error('Failed to load draft')
      }

      const result = await response.json()
      
      if (result.success && result.draft) {
        const draft = result.draft
        
        // Update store with draft data
        const store = useMouStore.getState()
        
        if (draft.currentStep) {
          store.setCurrentStep(draft.currentStep)
        }
        
        if (draft.mouDetails) {
          if (draft.mouDetails.organizationName) {
            store.setOrganizationName(draft.mouDetails.organizationName)
          }
          if (draft.mouDetails.mouDuration) {
            store.setMouDuration(draft.mouDetails.mouDuration)
          }
          if (draft.mouDetails.durationReason) {
            store.setDurationReason(draft.mouDetails.durationReason)
          }
        }
        
        if (draft.parties && Array.isArray(draft.parties)) {
          // Clear existing parties and add draft parties
          store.clearParties()
          draft.parties.forEach((party: any) => {
            store.addParty(party)
          })
        }
        
        if (draft.projects && Array.isArray(draft.projects)) {
          // Clear existing projects and add draft projects
          store.clearProjects()
          draft.projects.forEach((project: any) => {
            store.addProject(project)
          })
        }
        
        if (draft.activities && Array.isArray(draft.activities)) {
          // Clear existing activities and add draft activities
          store.clearActivities()
          draft.activities.forEach((activity: any) => {
            store.addActivity(activity)
          })
        }
        
        if (draft.documents && Array.isArray(draft.documents)) {
          // Update documents
          draft.documents.forEach((doc: any) => {
            store.updateDocument(doc.type, doc)
          })
        }

        toast({
          title: "Draft Loaded",
          description: "Your previous draft has been restored.",
          duration: 3000
        })
        
        return true
      }
      
      return false

    } catch (error) {
      console.error('Error loading draft:', error)
      toast({
        variant: "destructive",
        title: "Load Failed",
        description: "Failed to load draft. Starting fresh.",
        duration: 5000
      })
      return false
    }
  }, [toast])

  // Delete draft from server
  const deleteDraft = useCallback(async () => {
    try {
      const response = await fetch('/api/mou-applications/draft', {
        method: 'DELETE'
      })
      
      if (!response.ok) {
        throw new Error('Failed to delete draft')
      }

      const result = await response.json()
      
      if (result.success) {
        setLastSaved(null)
        toast({
          title: "Draft Deleted",
          description: "Your draft has been deleted.",
          duration: 3000
        })
        return true
      }
      
      return false

    } catch (error) {
      console.error('Error deleting draft:', error)
      toast({
        variant: "destructive",
        title: "Delete Failed",
        description: "Failed to delete draft.",
        duration: 5000
      })
      return false
    }
  }, [toast])

  // Auto-save functionality
  useEffect(() => {
    if (!autoSaveEnabled) return

    const autoSaveInterval = setInterval(() => {
      // Only auto-save if there's meaningful data
      if (parties.length > 0 || projects.length > 0 || organizationName) {
        saveDraft(false) // Don't show toast for auto-save
      }
    }, 30000) // Auto-save every 30 seconds

    return () => clearInterval(autoSaveInterval)
  }, [autoSaveEnabled, saveDraft, parties.length, projects.length, organizationName])

  // Manual save with toast
  const saveNow = useCallback(() => {
    return saveDraft(true)
  }, [saveDraft])

  return {
    isSaving,
    lastSaved,
    autoSaveEnabled,
    setAutoSaveEnabled,
    saveDraft: saveNow,
    loadDraft,
    deleteDraft
  }
}
