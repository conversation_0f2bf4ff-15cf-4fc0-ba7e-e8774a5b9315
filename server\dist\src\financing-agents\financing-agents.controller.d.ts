import { FinancingAgentsService } from './financing-agents.service';
import { CreateFinancingAgentDto, UpdateFinancingAgentDto } from './dto';
export declare class FinancingAgentsController {
    private readonly financingAgentsService;
    constructor(financingAgentsService: FinancingAgentsService);
    create(createFinancingAgentDto: CreateFinancingAgentDto, req: any): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        agentName: string;
        contactInfo: string | null;
    }>;
    findAll(): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        agentName: string;
        contactInfo: string | null;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        agentName: string;
        contactInfo: string | null;
    }>;
    update(id: number, updateFinancingAgentDto: UpdateFinancingAgentDto, req: any): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        agentName: string;
        contactInfo: string | null;
    }>;
    remove(id: number, req: any): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        agentName: string;
        contactInfo: string | null;
    }>;
}
