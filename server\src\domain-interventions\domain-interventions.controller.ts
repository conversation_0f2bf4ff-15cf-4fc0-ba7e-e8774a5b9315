import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { DomainInterventionsService } from './domain-interventions.service';
import { CreateDomainInterventionDto, UpdateDomainInterventionDto, DomainInterventionResponseDto } from './dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { Public } from '../auth/decorator/public.decorator';

@ApiTags('domain-interventions')
@Controller('domain-interventions')
export class DomainInterventionsController {
  constructor(private readonly domainInterventionsService: DomainInterventionsService) {}

  @Post()
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new domain intervention (Admin only)' })
  @ApiResponse({ status: 201, description: 'Domain intervention created successfully', type: DomainInterventionResponseDto })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid parent domain' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  async create(@Body() createDomainInterventionDto: CreateDomainInterventionDto, @Request() req: any) {
    return this.domainInterventionsService.create(createDomainInterventionDto, req.user.sub);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all domain interventions with hierarchy' })
  @ApiResponse({ status: 200, description: 'Returns list of domain interventions', type: [DomainInterventionResponseDto] })
  async findAll() {
    return this.domainInterventionsService.findAll();
  }

  @Get('tree')
  @Public()
  @ApiOperation({ summary: 'Get domain interventions as tree structure (root domains with sub-domains)' })
  @ApiResponse({ status: 200, description: 'Returns tree structure of domain interventions', type: [DomainInterventionResponseDto] })
  async findTree() {
    return this.domainInterventionsService.findTree();
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get domain intervention by ID with hierarchy' })
  @ApiResponse({ status: 200, description: 'Returns domain intervention details', type: DomainInterventionResponseDto })
  @ApiResponse({ status: 404, description: 'Domain intervention not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.domainInterventionsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update domain intervention (Admin only)' })
  @ApiResponse({ status: 200, description: 'Domain intervention updated successfully', type: DomainInterventionResponseDto })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid parent domain or circular reference' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Domain intervention not found' })
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateDomainInterventionDto: UpdateDomainInterventionDto, @Request() req: any) {
    return this.domainInterventionsService.update(id, updateDomainInterventionDto, req.user.sub);
  }

  @Delete(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete domain intervention (Admin only)' })
  @ApiResponse({ status: 200, description: 'Domain intervention deleted successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - Domain has sub-domains' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Domain intervention not found' })
  async remove(@Param('id', ParseIntPipe) id: number, @Request() req: any) {
    return this.domainInterventionsService.remove(id, req.user.sub);
  }
}
