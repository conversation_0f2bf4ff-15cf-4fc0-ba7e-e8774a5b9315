"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DomainInterventionTreeResponseDto = exports.DomainInterventionResponseDto = exports.UpdateDomainInterventionDto = exports.CreateDomainInterventionDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateDomainInterventionDto {
}
exports.CreateDomainInterventionDto = CreateDomainInterventionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The name of the domain intervention' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDomainInterventionDto.prototype, "domainName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Description of the domain intervention', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDomainInterventionDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID of the parent domain intervention (for hierarchical structure)', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateDomainInterventionDto.prototype, "parentId", void 0);
class UpdateDomainInterventionDto {
}
exports.UpdateDomainInterventionDto = UpdateDomainInterventionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The name of the domain intervention', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateDomainInterventionDto.prototype, "domainName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Description of the domain intervention', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateDomainInterventionDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID of the parent domain intervention', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateDomainInterventionDto.prototype, "parentId", void 0);
class DomainInterventionResponseDto {
}
exports.DomainInterventionResponseDto = DomainInterventionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], DomainInterventionResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The name of the domain intervention' }),
    __metadata("design:type", String)
], DomainInterventionResponseDto.prototype, "domainName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Description of the domain intervention' }),
    __metadata("design:type", String)
], DomainInterventionResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID of the parent domain intervention' }),
    __metadata("design:type", Number)
], DomainInterventionResponseDto.prototype, "parentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Parent domain intervention details', required: false }),
    __metadata("design:type", DomainInterventionResponseDto)
], DomainInterventionResponseDto.prototype, "parent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Child domain interventions', type: [DomainInterventionResponseDto] }),
    __metadata("design:type", Array)
], DomainInterventionResponseDto.prototype, "children", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'When the domain intervention was created' }),
    __metadata("design:type", Date)
], DomainInterventionResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'When the domain intervention was last updated' }),
    __metadata("design:type", Date)
], DomainInterventionResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether the domain intervention has been soft deleted' }),
    __metadata("design:type", Boolean)
], DomainInterventionResponseDto.prototype, "deleted", void 0);
class DomainInterventionTreeResponseDto extends DomainInterventionResponseDto {
}
exports.DomainInterventionTreeResponseDto = DomainInterventionTreeResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Child domain interventions', type: [DomainInterventionTreeResponseDto] }),
    __metadata("design:type", Array)
], DomainInterventionTreeResponseDto.prototype, "children", void 0);
//# sourceMappingURL=index.js.map