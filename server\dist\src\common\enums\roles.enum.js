"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_ROLE_PERMISSIONS = exports.ResourceType = exports.PermissionLevel = exports.ApproverRole = exports.ReviewerRole = exports.BaseRole = void 0;
exports.hasPermission = hasPermission;
var BaseRole;
(function (BaseRole) {
    BaseRole["ADMIN"] = "ADMIN";
    BaseRole["PARTNER"] = "PARTNER";
    BaseRole["STAFF"] = "STAFF";
})(BaseRole || (exports.BaseRole = BaseRole = {}));
var ReviewerRole;
(function (ReviewerRole) {
    ReviewerRole["PARTNER_COORDINATOR"] = "PARTNER_COORDINATOR";
    ReviewerRole["TECHNICAL_EXPERT"] = "TECHNICAL_EXPERT";
    ReviewerRole["LEGAL_OFFICER"] = "LEGAL_OFFICER";
    ReviewerRole["AUDITOR"] = "AUDITOR";
})(ReviewerRole || (exports.ReviewerRole = ReviewerRole = {}));
var ApproverRole;
(function (ApproverRole) {
    ApproverRole["HEAD_OF_DEPARTMENT"] = "HEAD_OF_DEPARTMENT";
    ApproverRole["LEGAL_OFFICER"] = "LEGAL_OFFICER";
    ApproverRole["PERMANENT_SECRETARY"] = "PERMANENT_SECRETARY";
    ApproverRole["MINISTER"] = "MINISTER";
    ApproverRole["ROLE_MANAGER"] = "ROLE_MANAGER";
    ApproverRole["PERMISSION_MANAGER"] = "PERMISSION_MANAGER";
})(ApproverRole || (exports.ApproverRole = ApproverRole = {}));
var PermissionLevel;
(function (PermissionLevel) {
    PermissionLevel["NONE"] = "NONE";
    PermissionLevel["READ"] = "READ";
    PermissionLevel["WRITE"] = "WRITE";
    PermissionLevel["ADMIN"] = "ADMIN";
})(PermissionLevel || (exports.PermissionLevel = PermissionLevel = {}));
var ResourceType;
(function (ResourceType) {
    ResourceType["USER"] = "USER";
    ResourceType["ORGANIZATION"] = "ORGANIZATION";
    ResourceType["APPLICATION"] = "APPLICATION";
    ResourceType["DOCUMENT"] = "DOCUMENT";
    ResourceType["REVIEW"] = "REVIEW";
    ResourceType["APPROVAL"] = "APPROVAL";
    ResourceType["REPORT"] = "REPORT";
    ResourceType["AUDIT_LOG"] = "AUDIT_LOG";
})(ResourceType || (exports.ResourceType = ResourceType = {}));
exports.DEFAULT_ROLE_PERMISSIONS = {
    [BaseRole.ADMIN]: [
        { resource: ResourceType.USER, level: PermissionLevel.ADMIN },
        { resource: ResourceType.ORGANIZATION, level: PermissionLevel.ADMIN },
        { resource: ResourceType.APPLICATION, level: PermissionLevel.ADMIN },
        { resource: ResourceType.DOCUMENT, level: PermissionLevel.ADMIN },
        { resource: ResourceType.REVIEW, level: PermissionLevel.ADMIN },
        { resource: ResourceType.APPROVAL, level: PermissionLevel.ADMIN },
        { resource: ResourceType.REPORT, level: PermissionLevel.ADMIN },
        { resource: ResourceType.AUDIT_LOG, level: PermissionLevel.ADMIN }
    ],
    [BaseRole.PARTNER]: [
        { resource: ResourceType.APPLICATION, level: PermissionLevel.WRITE },
        { resource: ResourceType.DOCUMENT, level: PermissionLevel.WRITE },
        { resource: ResourceType.ORGANIZATION, level: PermissionLevel.READ }
    ],
    [BaseRole.STAFF]: [
        { resource: ResourceType.APPLICATION, level: PermissionLevel.READ },
        { resource: ResourceType.DOCUMENT, level: PermissionLevel.READ }
    ],
    [ReviewerRole.PARTNER_COORDINATOR]: [
        { resource: ResourceType.APPLICATION, level: PermissionLevel.WRITE },
        { resource: ResourceType.REVIEW, level: PermissionLevel.WRITE },
        { resource: ResourceType.DOCUMENT, level: PermissionLevel.WRITE }
    ],
    [ReviewerRole.TECHNICAL_EXPERT]: [
        { resource: ResourceType.APPLICATION, level: PermissionLevel.READ },
        { resource: ResourceType.REVIEW, level: PermissionLevel.WRITE },
        { resource: ResourceType.DOCUMENT, level: PermissionLevel.READ }
    ],
    [ReviewerRole.LEGAL_OFFICER]: [
        { resource: ResourceType.APPLICATION, level: PermissionLevel.READ },
        { resource: ResourceType.REVIEW, level: PermissionLevel.WRITE },
        { resource: ResourceType.DOCUMENT, level: PermissionLevel.WRITE }
    ],
    [ReviewerRole.AUDITOR]: [
        { resource: ResourceType.AUDIT_LOG, level: PermissionLevel.READ },
        { resource: ResourceType.APPLICATION, level: PermissionLevel.READ },
        { resource: ResourceType.REVIEW, level: PermissionLevel.READ }
    ],
    [ApproverRole.HEAD_OF_DEPARTMENT]: [
        { resource: ResourceType.APPLICATION, level: PermissionLevel.WRITE },
        { resource: ResourceType.APPROVAL, level: PermissionLevel.WRITE },
        { resource: ResourceType.REVIEW, level: PermissionLevel.READ }
    ],
    [ApproverRole.PERMANENT_SECRETARY]: [
        { resource: ResourceType.APPLICATION, level: PermissionLevel.ADMIN },
        { resource: ResourceType.APPROVAL, level: PermissionLevel.ADMIN },
        { resource: ResourceType.ORGANIZATION, level: PermissionLevel.WRITE }
    ],
    [ApproverRole.MINISTER]: [
        { resource: ResourceType.APPLICATION, level: PermissionLevel.ADMIN },
        { resource: ResourceType.APPROVAL, level: PermissionLevel.ADMIN },
        { resource: ResourceType.ORGANIZATION, level: PermissionLevel.ADMIN }
    ],
    [ApproverRole.ROLE_MANAGER]: [
        { resource: ResourceType.USER, level: PermissionLevel.WRITE },
        { resource: ResourceType.ORGANIZATION, level: PermissionLevel.WRITE }
    ],
    [ApproverRole.PERMISSION_MANAGER]: [
        { resource: ResourceType.USER, level: PermissionLevel.ADMIN },
        { resource: ResourceType.ORGANIZATION, level: PermissionLevel.ADMIN }
    ]
};
function hasPermission(userRoles, resource, requiredLevel) {
    const userPermissions = userRoles.flatMap(role => exports.DEFAULT_ROLE_PERMISSIONS[role] || []);
    const highestPermission = userPermissions
        .filter(permission => permission.resource === resource)
        .reduce((highest, current) => {
        const levels = Object.values(PermissionLevel);
        const highestLevel = levels.indexOf(highest?.level || PermissionLevel.NONE);
        const currentLevel = levels.indexOf(current.level);
        return currentLevel > highestLevel ? current : highest;
    }, null);
    if (!highestPermission) {
        return false;
    }
    const levels = Object.values(PermissionLevel);
    return levels.indexOf(highestPermission.level) >= levels.indexOf(requiredLevel);
}
//# sourceMappingURL=roles.enum.js.map