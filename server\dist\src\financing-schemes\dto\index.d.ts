export declare class CreateFinancingSchemeDto {
    schemeName: string;
    description?: string;
    terms?: string;
    conditions?: string;
}
export declare class UpdateFinancingSchemeDto {
    schemeName?: string;
    description?: string;
    terms?: string;
    conditions?: string;
}
export declare class FinancingSchemeResponseDto {
    id: number;
    schemeName: string;
    description?: string;
    terms?: string;
    conditions?: string;
    createdAt: Date;
    updatedAt: Date;
    deleted: boolean;
}
