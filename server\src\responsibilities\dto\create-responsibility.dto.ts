import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class CreateResponsibilityDto {
  @ApiProperty({
    description: 'Responsibility name',
    example: 'Healthcare Service Delivery',
  })
  @IsString()
  @IsNotEmpty()
  name: string;
}

export class UpdateResponsibilityDto {
  @ApiProperty({
    description: 'Responsibility name',
    example: 'Updated Healthcare Service Delivery',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  name?: string;
}

export class ResponsibilityResponseDto {
  @ApiProperty({ description: 'Responsibility ID', example: 1 })
  id: number;

  @ApiProperty({ description: 'Responsibility name', example: 'Healthcare Service Delivery' })
  name: string;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;

  @ApiProperty({ description: 'Soft delete flag' })
  deleted: boolean;
}
