export declare class CreatePartyDto {
    partyId: string;
    name: string;
    responsibilityId: number;
    organizationId: number;
    objectiveId: number;
    goalId: number;
    signatory: string;
    position: string;
    duration: number;
    reasonForExtendedDuration?: string;
}
export declare class UpdatePartyDto {
    name?: string;
    responsibilityId?: number;
    objectiveId?: number;
    goalId?: number;
    signatory?: string;
    position?: string;
    duration?: number;
    reasonForExtendedDuration?: string;
}
export declare class PartyResponseDto {
    id: number;
    partyId: string;
    name: string;
    responsibilityId: number;
    organizationId: number;
    objectiveId: number;
    goalId: number;
    signatory: string;
    position: string;
    duration: number;
    reasonForExtendedDuration?: string;
    createdAt: Date;
    updatedAt: Date;
    deleted: boolean;
}
export declare class PartyWithRelationsDto extends PartyResponseDto {
    organization?: {
        id: number;
        organizationName: string;
        organizationEmail: string;
        organizationType: {
            id: number;
            typeName: string;
        };
    };
    responsibility?: {
        id: number;
        name: string;
    };
    objective?: {
        id: number;
        name: string;
    };
    goal?: {
        id: number;
        name: string;
    };
    mou?: {
        id: number;
        mouKey: string;
        mouApplications: Array<{
            id: number;
            applicationKey: string;
        }>;
    };
}
