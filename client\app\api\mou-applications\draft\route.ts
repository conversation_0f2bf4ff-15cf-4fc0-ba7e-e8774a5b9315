import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      mouDetails,
      parties,
      projects,
      activities,
      documents,
      currentStep
    } = body

    // Upsert draft application in database
    const draftApplication = await prisma.draftMouApplication.upsert({
      where: {
        userId: parseInt(session.user.id)
      },
      update: {
        currentStep: currentStep || 1,
        mouDetails: mouDetails || {},
        parties: parties || [],
        projects: projects || [],
        activities: activities || [],
        documents: documents || [],
        organizationId: session.user.organizationId ? parseInt(session.user.organizationId) : null,
        updatedAt: new Date()
      },
      create: {
        userId: parseInt(session.user.id),
        organizationId: session.user.organizationId ? parseInt(session.user.organizationId) : null,
        currentStep: currentStep || 1,
        mouDetails: mouDetails || {},
        parties: parties || [],
        projects: projects || [],
        activities: activities || [],
        documents: documents || []
      }
    })

    console.log('Draft saved to database:', {
      draftId: draftApplication.id,
      userId: session.user.id,
      step: currentStep,
      partiesCount: parties?.length || 0,
      projectsCount: projects?.length || 0
    })

    return NextResponse.json({
      success: true,
      draftId: draftApplication.id,
      message: 'Draft saved successfully'
    })

  } catch (error) {
    console.error('Error saving draft:', error)
    return NextResponse.json(
      { error: 'Failed to save draft' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get user's current draft from database
    const draft = await prisma.draftMouApplication.findUnique({
      where: {
        userId: parseInt(session.user.id)
      }
    })

    if (!draft) {
      return NextResponse.json({
        success: true,
        draft: null,
        message: 'No draft found'
      })
    }

    return NextResponse.json({
      success: true,
      draft: {
        id: draft.id,
        userId: draft.userId,
        organizationId: draft.organizationId,
        currentStep: draft.currentStep,
        mouDetails: draft.mouDetails,
        parties: draft.parties,
        projects: draft.projects,
        activities: draft.activities,
        documents: draft.documents,
        createdAt: draft.createdAt.toISOString(),
        updatedAt: draft.updatedAt.toISOString()
      },
      message: 'Draft retrieved successfully'
    })

  } catch (error) {
    console.error('Error retrieving draft:', error)
    return NextResponse.json(
      { error: 'Failed to retrieve draft' },
      { status: 500 }
    )
  }
}

export async function DELETE() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Delete user's current draft from database
    await prisma.draftMouApplication.delete({
      where: {
        userId: parseInt(session.user.id)
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Draft deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting draft:', error)
    return NextResponse.json(
      { error: 'Failed to delete draft' },
      { status: 500 }
    )
  }
}
