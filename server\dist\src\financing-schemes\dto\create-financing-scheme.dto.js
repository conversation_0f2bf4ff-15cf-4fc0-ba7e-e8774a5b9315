"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateFinancingSchemeDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateFinancingSchemeDto {
}
exports.CreateFinancingSchemeDto = CreateFinancingSchemeDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the financing scheme',
        example: 'Health Sector Development Grant',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateFinancingSchemeDto.prototype, "schemeName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Description of the financing scheme',
        example: 'Grant program for health sector infrastructure development',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateFinancingSchemeDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Terms and conditions of the financing scheme',
        example: 'Maximum grant amount: $1M, Duration: 3 years, Matching funds required',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateFinancingSchemeDto.prototype, "terms", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Additional conditions for the financing scheme',
        example: 'Must demonstrate community impact and sustainability plan',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateFinancingSchemeDto.prototype, "conditions", void 0);
//# sourceMappingURL=create-financing-scheme.dto.js.map