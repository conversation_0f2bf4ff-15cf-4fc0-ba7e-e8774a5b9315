import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateGoalDto, UpdateGoalDto } from './dto/create-goal.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class GoalsService {
  constructor(private prisma: PrismaService) {}

  async create(createGoalDto: CreateGoalDto) {
    try {
      const goal = await this.prisma.goal.create({
        data: createGoalDto,
      });

      return goal;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Goal name must be unique');
        }
      }
      throw error;
    }
  }

  async findAll() {
    const goals = await this.prisma.goal.findMany({
      where: {
        deleted: false,
      },
      orderBy: {
        name: 'asc',
      },
    });

    return goals;
  }

  async findOne(id: number) {
    const goal = await this.prisma.goal.findFirst({
      where: {
        id,
        deleted: false,
      },
      include: {
        parties: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!goal) {
      throw new NotFoundException(`Goal with ID ${id} not found`);
    }

    return goal;
  }

  async update(id: number, updateGoalDto: UpdateGoalDto) {
    // Check if goal exists
    const existingGoal = await this.prisma.goal.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingGoal) {
      throw new NotFoundException(`Goal with ID ${id} not found`);
    }

    try {
      const updatedGoal = await this.prisma.goal.update({
        where: { id },
        data: updateGoalDto,
      });

      return updatedGoal;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Goal name must be unique');
        }
      }
      throw error;
    }
  }

  async remove(id: number) {
    // Check if goal exists
    const existingGoal = await this.prisma.goal.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingGoal) {
      throw new NotFoundException(`Goal with ID ${id} not found`);
    }

    // Check if goal is being used by parties
    const partiesUsingGoal = await this.prisma.party.findMany({
      where: {
        goalId: id,
        deleted: false,
      },
    });

    if (partiesUsingGoal.length > 0) {
      throw new ConflictException('Cannot delete goal that is being used by parties');
    }

    // Soft delete
    await this.prisma.goal.update({
      where: { id },
      data: { deleted: true },
    });

    return { message: 'Goal deleted successfully' };
  }
}
