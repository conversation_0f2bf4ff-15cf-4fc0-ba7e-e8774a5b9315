"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { DataTable } from "@/components/data-table"
import {
  Loader2, Plus, Settings, Database, Users, FileText,
  CheckCircle, Clock, AlertCircle, TrendingUp, Activity, BarChart,
  ArrowUpRight, ArrowDownRight, Eye, Edit, Trash2, Download,
  Filter, Search, Calendar, Globe, DollarSign, Building2
} from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import {
  masterDataService,
  type BudgetType,
  type FundingSource,
  type FundingUnit,
  type OrganizationType,
  type HealthCareProvider,
  type FinancingScheme,
  type FinancingAgent
} from "@/lib/services/master-data.service"

interface DashboardStats {
  totalMoUs: number
  activeMoUs: number
  pendingReviews: number
  totalPartners: number
  monthlyGrowth: number
  approvalRate: number
}

interface TableColumn {
  key: string
  label: string
  render?: (value: any, row: any) => React.ReactNode
}

const columns: Record<string, TableColumn[]> = {
  budgetTypes: [
    { key: "name", label: "Name" },
    { key: "description", label: "Description" },
    { key: "createdAt", label: "Created", render: (value) => new Date(value).toLocaleDateString() },
  ],
  fundingSources: [
    { key: "name", label: "Name" },
    { key: "description", label: "Description" },
    { key: "createdAt", label: "Created", render: (value) => new Date(value).toLocaleDateString() },
  ],
  organizationTypes: [
    { key: "name", label: "Name" },
    { key: "description", label: "Description" },
    { key: "createdAt", label: "Created", render: (value) => new Date(value).toLocaleDateString() },
  ],
  healthcareProviders: [
    { key: "name", label: "Name" },
    { key: "type", label: "Type" },
    { key: "createdAt", label: "Created", render: (value) => new Date(value).toLocaleDateString() },
  ],
  financingSchemes: [
    { key: "name", label: "Name" },
    { key: "description", label: "Description" },
    { key: "createdAt", label: "Created", render: (value) => new Date(value).toLocaleDateString() },
  ],
}

export default function AdminPage() {
  const { user } = useAuth()
  const [error, setError] = useState("")
  const [activeTab, setActiveTab] = useState("overview")
  const [loading, setLoading] = useState(false)
  const [tabLoading, setTabLoading] = useState(false)
  const [stats] = useState<DashboardStats>({
    totalMoUs: 125,
    activeMoUs: 82,
    pendingReviews: 12,
    totalPartners: 45,
    monthlyGrowth: 15,
    approvalRate: 85
  })

  // Master Data States
  const [budgetTypes, setBudgetTypes] = useState<BudgetType[]>([])
  const [fundingSources, setFundingSources] = useState<FundingSource[]>([])
  const [fundingUnits, setFundingUnits] = useState<FundingUnit[]>([])
  const [organizationTypes, setOrganizationTypes] = useState<OrganizationType[]>([])
  const [healthcareProviders, setHealthcareProviders] = useState<HealthCareProvider[]>([])
  const [financingSchemes, setFinancingSchemes] = useState<FinancingScheme[]>([])
  const [financingAgents, setFinancingAgents] = useState<FinancingAgent[]>([])

  const handleTabChange = async (value: string) => {
    setActiveTab(value)
    if (value === "master-data") {
      await loadMasterData()
    }
  }

  const loadMasterData = async () => {
    try {
      setLoading(true)
      const [
        budgetTypesData,
        fundingSourcesData,
        fundingUnitsData,
        organizationTypesData,
        healthcareProvidersData,
        financingSchemesData,
        financingAgentsData
      ] = await Promise.all([
        masterDataService.getBudgetTypes(),
        masterDataService.getFundingSources(),
        masterDataService.getFundingUnits(),
        masterDataService.getOrganizationTypes(),
        masterDataService.getHealthCareProviders(),
        masterDataService.getFinancingSchemes(),
        masterDataService.getFinancingAgents()
      ])

      setBudgetTypes(budgetTypesData)
      setFundingSources(fundingSourcesData)
      setFundingUnits(fundingUnitsData)
      setOrganizationTypes(organizationTypesData)
      setHealthcareProviders(healthcareProvidersData)
      setFinancingSchemes(financingSchemesData)
      setFinancingAgents(financingAgentsData)
    } catch (err) {
      console.error("Failed to load master data:", err)
      setError("Failed to load master data")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    const loadTabData = async () => {
      setTabLoading(true)
      setError(null)
      try {
        switch (activeTab) {
          case "budget":
            setBudgetTypes(await masterDataService.getBudgetTypes())
            break
          case "funding":
            setFundingSources(await masterDataService.getFundingSources())
            break
          case "organizations":
            setOrganizationTypes(await masterDataService.getOrganizationTypes())
            break
          case "healthcare":
            setHealthcareProviders(await masterDataService.getHealthcareProviders())
            break
          case "financing":
            setFinancingSchemes(await masterDataService.getFinancingSchemes())
            break
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load data")
      } finally {
        setTabLoading(false)
      }
    }
    loadTabData()
  }, [activeTab])

  const handleEdit = async (row: any) => {
    // Implement edit logic
    console.log("Edit row", row)
  }

  const handleDelete = async (row: any) => {
    // Implement delete logic
    console.log("Delete row", row)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="space-y-6 p-4 md:p-6">
        {/* Enhanced Header */}
        <div className="flex flex-col space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div className="space-y-1">
            <div className="flex items-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 shadow-md">
                <Settings className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold tracking-tight text-gray-900">
                  Admin Dashboard
                </h1>
                <p className="text-sm text-gray-600">
                  Welcome back, <span className="font-medium text-blue-600">{user?.firstName}</span>!
                  Here's what's happening today.
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" className="hidden md:flex border-gray-200 hover:bg-gray-50 h-8 px-3">
              <Download className="mr-1.5 h-3.5 w-3.5" />
              Export
            </Button>
            <Button size="sm" className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-md h-8 px-3">
              <Plus className="mr-1.5 h-3.5 w-3.5" />
              Actions
            </Button>
          </div>
        </div>

        {error && (
          <Alert variant="destructive" className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-red-800">{error}</AlertDescription>
          </Alert>
        )}

        {/* Enhanced Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card className="group relative overflow-hidden border-0 bg-white shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-purple-600/10"></div>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="space-y-0.5">
                <CardTitle className="text-xs font-medium text-gray-600 uppercase tracking-wide">Total MoUs</CardTitle>
                <div className="text-2xl font-bold text-purple-600">{stats.totalMoUs}</div>
              </div>
              <div className="rounded-lg p-2 bg-gradient-to-br from-purple-500 to-purple-600 shadow-md group-hover:shadow-purple-500/25 transition-shadow">
                <FileText className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent className="pt-0 pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1.5">
                  <Badge variant="secondary" className="bg-purple-100 text-purple-700 hover:bg-purple-200 text-xs px-2 py-0.5">
                    {stats.activeMoUs} active
                  </Badge>
                </div>
                <div className="flex items-center text-green-600 text-xs font-medium">
                  <ArrowUpRight className="h-3 w-3 mr-0.5" />
                  +12%
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="group relative overflow-hidden border-0 bg-white shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5">
            <div className="absolute inset-0 bg-gradient-to-br from-amber-500/5 to-orange-600/10"></div>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="space-y-0.5">
                <CardTitle className="text-xs font-medium text-gray-600 uppercase tracking-wide">Pending Reviews</CardTitle>
                <div className="text-2xl font-bold text-amber-600">{stats.pendingReviews}</div>
              </div>
              <div className="rounded-lg p-2 bg-gradient-to-br from-amber-500 to-orange-600 shadow-md group-hover:shadow-amber-500/25 transition-shadow">
                <Clock className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent className="pt-0 pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1.5">
                  <Badge variant="secondary" className="bg-amber-100 text-amber-700 hover:bg-amber-200 text-xs px-2 py-0.5">
                    Urgent
                  </Badge>
                </div>
                <div className="flex items-center text-red-600 text-xs font-medium">
                  <AlertCircle className="h-3 w-3 mr-0.5" />
                  Attention
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="group relative overflow-hidden border-0 bg-white shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-600/10"></div>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="space-y-0.5">
                <CardTitle className="text-xs font-medium text-gray-600 uppercase tracking-wide">Total Partners</CardTitle>
                <div className="text-2xl font-bold text-blue-600">{stats.totalPartners}</div>
              </div>
              <div className="rounded-lg p-2 bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md group-hover:shadow-blue-500/25 transition-shadow">
                <Building2 className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent className="pt-0 pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1.5">
                  <Badge variant="secondary" className="bg-blue-100 text-blue-700 hover:bg-blue-200 text-xs px-2 py-0.5">
                    Organizations
                  </Badge>
                </div>
                <div className="flex items-center text-green-600 text-xs font-medium">
                  <TrendingUp className="h-3 w-3 mr-0.5" />
                  +{stats.monthlyGrowth}%
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="group relative overflow-hidden border-0 bg-white shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-green-600/10"></div>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="space-y-0.5">
                <CardTitle className="text-xs font-medium text-gray-600 uppercase tracking-wide">Approval Rate</CardTitle>
                <div className="text-2xl font-bold text-emerald-600">{stats.approvalRate}%</div>
              </div>
              <div className="rounded-lg p-2 bg-gradient-to-br from-emerald-500 to-green-600 shadow-md group-hover:shadow-emerald-500/25 transition-shadow">
                <CheckCircle className="h-4 w-4 text-white" />
              </div>
            </CardHeader>
            <CardContent className="pt-0 pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1.5">
                  <Badge variant="secondary" className="bg-emerald-100 text-emerald-700 hover:bg-emerald-200 text-xs px-2 py-0.5">
                    30 days
                  </Badge>
                </div>
                <div className="flex items-center text-emerald-600 text-xs font-medium">
                  <ArrowUpRight className="h-3 w-3 mr-0.5" />
                  Excellent
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Main Content Tabs */}
        <Tabs
          defaultValue="overview"
          className="space-y-4"
          onValueChange={handleTabChange}
        >
          <div className="flex flex-col space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0">
            <TabsList className="inline-flex h-10 items-center justify-start rounded-lg bg-white p-1 shadow-md border border-gray-100">
              <TabsTrigger
                value="overview"
                className="inline-flex items-center justify-center gap-1.5 whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-indigo-600 data-[state=active]:text-white data-[state=active]:shadow-md hover:bg-gray-50"
              >
                <BarChart className="h-3.5 w-3.5" />
                Overview
              </TabsTrigger>
              <TabsTrigger
                value="master-data"
                className="inline-flex items-center justify-center gap-1.5 whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-indigo-600 data-[state=active]:text-white data-[state=active]:shadow-md hover:bg-gray-50"
              >
                <Database className="h-3.5 w-3.5" />
                Master Data
              </TabsTrigger>
              <TabsTrigger
                value="analytics"
                className="inline-flex items-center justify-center gap-1.5 whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-indigo-600 data-[state=active]:text-white data-[state=active]:shadow-md hover:bg-gray-50"
              >
                <Activity className="h-3.5 w-3.5" />
                Analytics
              </TabsTrigger>
            </TabsList>

            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" className="hidden md:flex h-8 px-3">
                <Filter className="mr-1.5 h-3.5 w-3.5" />
                Filter
              </Button>
              <Button variant="outline" size="sm" className="hidden md:flex h-8 px-3">
                <Calendar className="mr-1.5 h-3.5 w-3.5" />
                Date Range
              </Button>
            </div>
          </div>

          {tabLoading ? (
            <Card className="border-0 bg-white shadow-md">
              <CardContent className="flex h-[300px] items-center justify-center">
                <div className="text-center space-y-3">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto" />
                  <p className="text-gray-600 text-sm">Loading dashboard data...</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <>
              <TabsContent value="overview" className="space-y-4 mt-0">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
                  <Card className="col-span-4 border-0 bg-white shadow-md hover:shadow-lg transition-shadow duration-200">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-lg font-semibold text-gray-900">Recent Activity</CardTitle>
                          <CardDescription className="text-gray-600 text-sm">
                            Overview of recent MoU applications and reviews
                          </CardDescription>
                        </div>
                        <Button variant="outline" size="sm" className="h-8 px-3">
                          <Eye className="mr-1.5 h-3.5 w-3.5" />
                          View All
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[280px] flex items-center justify-center rounded-lg bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-100">
                        <div className="text-center space-y-3">
                          <div className="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 mx-auto">
                            <BarChart className="h-6 w-6 text-blue-600" />
                          </div>
                          <div>
                            <p className="text-gray-600 font-medium text-sm">Activity Chart</p>
                            <p className="text-xs text-gray-500">Chart visualization will be implemented here</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="col-span-3 border-0 bg-white shadow-md hover:shadow-lg transition-shadow duration-200">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg font-semibold text-gray-900">Status Distribution</CardTitle>
                      <CardDescription className="text-gray-600 text-sm">
                        Current state of MoU applications
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-3 rounded-lg bg-green-50 border border-green-100">
                          <div className="flex items-center space-x-2.5">
                            <div className="rounded-full p-1.5 bg-green-100">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            </div>
                            <div>
                              <span className="font-medium text-gray-900 text-sm">Approved</span>
                              <p className="text-xs text-gray-600">Successfully processed</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <span className="text-xl font-bold text-green-600">45</span>
                            <p className="text-xs text-green-600">+5 this week</p>
                          </div>
                        </div>

                        <div className="flex items-center justify-between p-3 rounded-lg bg-amber-50 border border-amber-100">
                          <div className="flex items-center space-x-2.5">
                            <div className="rounded-full p-1.5 bg-amber-100">
                              <Clock className="h-4 w-4 text-amber-600" />
                            </div>
                            <div>
                              <span className="font-medium text-gray-900 text-sm">Pending</span>
                              <p className="text-xs text-gray-600">Awaiting review</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <span className="text-xl font-bold text-amber-600">12</span>
                            <p className="text-xs text-amber-600">Needs attention</p>
                          </div>
                        </div>

                        <div className="flex items-center justify-between p-3 rounded-lg bg-red-50 border border-red-100">
                          <div className="flex items-center space-x-2.5">
                            <div className="rounded-full p-1.5 bg-red-100">
                              <AlertCircle className="h-4 w-4 text-red-600" />
                            </div>
                            <div>
                              <span className="font-medium text-gray-900 text-sm">Rejected</span>
                              <p className="text-xs text-gray-600">Requires revision</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <span className="text-xl font-bold text-red-600">8</span>
                            <p className="text-xs text-red-600">-2 this week</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="master-data" className="space-y-4 mt-0">
                <Card className="border-0 bg-white shadow-md">
                  <CardHeader className="pb-4">
                    <div className="flex flex-col space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0">
                      <div className="space-y-1">
                        <CardTitle className="text-lg font-semibold text-gray-900">Master Data Management</CardTitle>
                        <CardDescription className="text-gray-600 text-sm">
                          Manage all reference data for the MoU system. Configure budget types, funding sources, and organizational data.
                        </CardDescription>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button variant="outline" size="sm" className="h-8 px-3">
                          <Download className="mr-1.5 h-3.5 w-3.5" />
                          Export
                        </Button>
                        <Button className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-md h-8 px-3">
                          <Plus className="mr-1.5 h-3.5 w-3.5" />
                          Add New
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Tabs defaultValue="budget-types" className="space-y-4">
                      <div className="overflow-x-auto">
                        <TabsList className="inline-flex h-10 items-center justify-start rounded-lg bg-gray-50 p-1 border border-gray-200">
                          <TabsTrigger value="budget-types" className="rounded-md px-3 py-1.5 text-sm font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
                            <DollarSign className="mr-1.5 h-3.5 w-3.5" />
                            Budget Types
                          </TabsTrigger>
                          <TabsTrigger value="funding" className="rounded-md px-3 py-1.5 text-sm font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
                            <Globe className="mr-1.5 h-3.5 w-3.5" />
                            Funding
                          </TabsTrigger>
                          <TabsTrigger value="organizations" className="rounded-md px-3 py-1.5 text-sm font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
                            <Building2 className="mr-1.5 h-3.5 w-3.5" />
                            Organizations
                          </TabsTrigger>
                          <TabsTrigger value="healthcare" className="rounded-md px-3 py-1.5 text-sm font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
                            <Activity className="mr-1.5 h-3.5 w-3.5" />
                            Healthcare
                          </TabsTrigger>
                          <TabsTrigger value="financing" className="rounded-md px-3 py-1.5 text-sm font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
                            <FileText className="mr-1.5 h-3.5 w-3.5" />
                            Financing
                          </TabsTrigger>
                          <TabsTrigger value="categories" className="rounded-md px-3 py-1.5 text-sm font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
                            Categories
                          </TabsTrigger>
                          <TabsTrigger value="domains" className="rounded-md px-3 py-1.5 text-sm font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm">
                            Domains
                          </TabsTrigger>
                        </TabsList>
                      </div>

                      <TabsContent value="budget-types" className="mt-0">
                        <div className="rounded-lg border border-gray-200 bg-white shadow-sm">
                          <div className="p-4">
                            <div className="flex items-center justify-between mb-3">
                              <div>
                                <h3 className="text-base font-semibold text-gray-900">Budget Types</h3>
                                <p className="text-xs text-gray-600">Manage budget type classifications</p>
                              </div>
                              <Badge variant="secondary" className="bg-blue-100 text-blue-700 text-xs px-2 py-0.5">
                                {budgetTypes.length} items
                              </Badge>
                            </div>
                            <DataTable
                              columns={columns.budgetTypes}
                              data={budgetTypes}
                              searchKey="name"
                              onEdit={handleEdit}
                              onDelete={handleDelete}
                              searchPlaceholder="Search budget types..."
                            />
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="funding" className="mt-0">
                        <div className="rounded-lg border border-gray-200 bg-white shadow-sm">
                          <div className="p-4">
                            <div className="flex items-center justify-between mb-3">
                              <div>
                                <h3 className="text-base font-semibold text-gray-900">Funding Sources</h3>
                                <p className="text-xs text-gray-600">Manage funding source configurations</p>
                              </div>
                              <Badge variant="secondary" className="bg-green-100 text-green-700 text-xs px-2 py-0.5">
                                {fundingSources.length} items
                              </Badge>
                            </div>
                            <DataTable
                              columns={columns.fundingSources}
                              data={fundingSources}
                              searchKey="name"
                              onEdit={handleEdit}
                              onDelete={handleDelete}
                              searchPlaceholder="Search funding sources..."
                            />
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="organizations" className="mt-0">
                        <div className="rounded-lg border border-gray-200 bg-white shadow-sm">
                          <div className="p-4">
                            <div className="flex items-center justify-between mb-3">
                              <div>
                                <h3 className="text-base font-semibold text-gray-900">Organization Types</h3>
                                <p className="text-xs text-gray-600">Manage organization type categories</p>
                              </div>
                              <Badge variant="secondary" className="bg-purple-100 text-purple-700 text-xs px-2 py-0.5">
                                {organizationTypes.length} items
                              </Badge>
                            </div>
                            <DataTable
                              columns={columns.organizationTypes}
                              data={organizationTypes}
                              searchKey="name"
                              onEdit={handleEdit}
                              onDelete={handleDelete}
                              searchPlaceholder="Search organization types..."
                            />
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="healthcare" className="mt-0">
                        <div className="rounded-xl border border-gray-200 bg-white shadow-sm">
                          <div className="p-6">
                            <div className="flex items-center justify-between mb-4">
                              <div>
                                <h3 className="text-lg font-semibold text-gray-900">Healthcare Providers</h3>
                                <p className="text-sm text-gray-600">Manage healthcare provider classifications</p>
                              </div>
                              <Badge variant="secondary" className="bg-red-100 text-red-700">
                                {healthcareProviders.length} items
                              </Badge>
                            </div>
                            <DataTable
                              columns={columns.healthcareProviders}
                              data={healthcareProviders}
                              searchKey="name"
                              onEdit={handleEdit}
                              onDelete={handleDelete}
                              searchPlaceholder="Search healthcare providers..."
                            />
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="financing" className="mt-0">
                        <div className="rounded-xl border border-gray-200 bg-white shadow-sm">
                          <div className="p-6">
                            <div className="flex items-center justify-between mb-4">
                              <div>
                                <h3 className="text-lg font-semibold text-gray-900">Financing Schemes</h3>
                                <p className="text-sm text-gray-600">Manage financing scheme configurations</p>
                              </div>
                              <Badge variant="secondary" className="bg-indigo-100 text-indigo-700">
                                {financingSchemes.length} items
                              </Badge>
                            </div>
                            <DataTable
                              columns={columns.financingSchemes}
                              data={financingSchemes}
                              searchKey="name"
                              onEdit={handleEdit}
                              onDelete={handleDelete}
                              searchPlaceholder="Search financing schemes..."
                            />
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="categories" className="mt-0">
                        <div className="rounded-lg border border-gray-200 bg-white shadow-sm">
                          <div className="flex h-[300px] items-center justify-center">
                            <div className="text-center space-y-3">
                              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mx-auto">
                                <Database className="h-6 w-6 text-gray-400" />
                              </div>
                              <div>
                                <p className="text-base font-medium text-gray-900">Categories Management</p>
                                <p className="text-xs text-gray-500">Categories data management will be available soon.</p>
                              </div>
                              <Button variant="outline" disabled size="sm" className="h-8 px-3">
                                Coming Soon
                              </Button>
                            </div>
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="domains" className="mt-0">
                        <div className="rounded-lg border border-gray-200 bg-white shadow-sm">
                          <div className="flex h-[300px] items-center justify-center">
                            <div className="text-center space-y-3">
                              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mx-auto">
                                <Globe className="h-6 w-6 text-gray-400" />
                              </div>
                              <div>
                                <p className="text-base font-medium text-gray-900">Domains Management</p>
                                <p className="text-xs text-gray-500">Domains data management will be available soon.</p>
                              </div>
                              <Button variant="outline" disabled size="sm" className="h-8 px-3">
                                Coming Soon
                              </Button>
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="analytics" className="space-y-4 mt-0">
                <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-3">
                  <Card className="lg:col-span-2 border-0 bg-white shadow-md hover:shadow-lg transition-shadow duration-200">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-lg font-semibold text-gray-900">Analytics & Insights</CardTitle>
                          <CardDescription className="text-gray-600 text-sm">
                            Detailed analysis of MoU activities and trends
                          </CardDescription>
                        </div>
                        <Button variant="outline" size="sm" className="h-8 px-3">
                          <Download className="mr-1.5 h-3.5 w-3.5" />
                          Export Data
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid gap-4 md:grid-cols-2">
                        <Card className="border border-gray-200 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-sm hover:shadow-md transition-shadow">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-base font-medium text-gray-900">Monthly MoU Submissions</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="h-[180px] flex items-center justify-center rounded-lg bg-white border border-blue-100">
                              <div className="text-center space-y-2">
                                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 mx-auto">
                                  <BarChart className="h-5 w-5 text-blue-600" />
                                </div>
                                <div>
                                  <p className="font-medium text-gray-900 text-sm">Chart Visualization</p>
                                  <p className="text-xs text-gray-500">Coming soon</p>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>

                        <Card className="border border-gray-200 bg-gradient-to-br from-green-50 to-emerald-50 shadow-sm hover:shadow-md transition-shadow">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-base font-medium text-gray-900">Organizations by Type</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="h-[180px] flex items-center justify-center rounded-lg bg-white border border-green-100">
                              <div className="text-center space-y-2">
                                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-green-100 mx-auto">
                                  <Building2 className="h-5 w-5 text-green-600" />
                                </div>
                                <div>
                                  <p className="font-medium text-gray-900 text-sm">Organization Analytics</p>
                                  <p className="text-xs text-gray-500">Coming soon</p>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>

                      <Card className="border border-gray-200 bg-gradient-to-br from-purple-50 to-indigo-50 shadow-sm hover:shadow-md transition-shadow">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-base font-medium text-gray-900">Funding Distribution</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="h-[180px] flex items-center justify-center rounded-lg bg-white border border-purple-100">
                            <div className="text-center space-y-2">
                              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-purple-100 mx-auto">
                                <DollarSign className="h-5 w-5 text-purple-600" />
                              </div>
                              <div>
                                <p className="font-medium text-gray-900 text-sm">Financial Analytics</p>
                                <p className="text-xs text-gray-500">Coming soon</p>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </CardContent>
                  </Card>

                  <Card className="border-0 bg-white shadow-md">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base font-semibold text-gray-900">Quick Actions</CardTitle>
                      <CardDescription className="text-gray-600 text-xs">
                        Frequently used admin functions
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <Button className="w-full justify-start bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 h-9 text-sm">
                        <Users className="mr-2 h-4 w-4" />
                        Manage Users
                      </Button>
                      <Button variant="outline" className="w-full justify-start h-9 text-sm">
                        <FileText className="mr-2 h-4 w-4" />
                        Review Applications
                      </Button>
                      <Button variant="outline" className="w-full justify-start h-9 text-sm">
                        <Settings className="mr-2 h-4 w-4" />
                        System Settings
                      </Button>
                      <Button variant="outline" className="w-full justify-start h-9 text-sm">
                        <Download className="mr-2 h-4 w-4" />
                        Generate Reports
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </>
          )}
        </Tabs>
      </div>
    </div>
  )
}
