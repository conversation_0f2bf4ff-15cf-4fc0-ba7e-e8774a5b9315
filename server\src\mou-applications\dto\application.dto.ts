import { IsString, IsInt, IsOptional, <PERSON>Enum, IsArray, IsJSON, Min, IsDateString } from 'class-validator';
import { ApplicationStatus, ModificationSource } from '@prisma/client';

// Define ReviewDecisionType enum locally since it doesn't exist in Prisma
enum ReviewDecisionType {
  APPROVE = 'APPROVE',
  REJECT = 'REJECT',
  REQUEST_MODIFICATIONS = 'REQUEST_MODIFICATIONS'
}

export class CreateApplicationDto {
  @IsString()
  title: string;

  @IsInt()
  partnerId: number;

  @IsOptional()
  @IsJSON()
  tags?: string;

  @IsOptional()
  @Min(0)
  priority?: number;
}

export class UpdateApplicationDto {
  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsEnum(ApplicationStatus)
  status?: ApplicationStatus;

  @IsOptional()
  @IsString()
  currentPhase?: string;

  @IsOptional()
  @IsDateString()
  phaseDueDate?: Date;

  @IsOptional()
  @IsJSON()
  tags?: string;

  @IsOptional()
  @Min(0)
  priority?: number;
}

export class AssignTechnicalExpertDto {
  @IsInt()
  expertId: number;

  @IsOptional()
  @IsDateString()
  dueDate?: Date;

  @IsOptional()
  @Min(0)
  priority?: number;

  @IsOptional()
  @IsString()
  specialInstructions?: string;
}

export class ReviewDecisionDto {
  @IsEnum(ReviewDecisionType)
  decision: ReviewDecisionType;

  @IsString()
  comments: string;

  @IsOptional()
  @IsJSON()
  attachments?: string;

  @IsOptional()
  @IsJSON()
  checklist?: string;

  @IsOptional()
  @IsJSON()
  riskAssessment?: string;
}

export class ModificationRequestDto {
  @IsEnum(ModificationSource)
  source: ModificationSource;

  @IsString()
  details: string;

  @IsOptional()
  @Min(0)
  priority?: number;

  @IsOptional()
  @IsDateString()
  dueDate?: Date;
}

export class FilterApplicationsDto {
  @IsOptional()
  @IsEnum(ApplicationStatus, { each: true })
  status?: ApplicationStatus[];

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsInt()
  partnerId?: number;

  @IsOptional()
  @IsDateString()
  startDate?: Date;

  @IsOptional()
  @IsDateString()
  endDate?: Date;

  @IsOptional()
  @Min(0)
  page?: number;

  @IsOptional()
  @Min(1)
  limit?: number;

  @IsOptional()
  @IsString()
  sortBy?: string;

  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';
}

export class ApplicationVersionDto {
  @IsInt()
  version: number;

  @IsOptional()
  @IsInt()
  previousVersionId?: number;
}
