import { PrismaService } from '../prisma/prisma.service';
import { CreateActivityDto, UpdateActivityDto } from './dto/create-activity.dto';
export declare class ActivitiesService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createActivityDto: CreateActivityDto): Promise<{
        domainIntervention: {
            parent: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                description: string | null;
                userId: number | null;
                parentId: number | null;
                domainName: string;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            userId: number | null;
            parentId: number | null;
            domainName: string;
        };
        project: {
            mouApplication: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                userId: number | null;
                applicationKey: string;
                mouId: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            duration: number;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            mouApplicationId: number;
        };
        input: {
            inputSubclass: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                inputId: number;
                subclassId: number;
                budget: number;
            }[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        description: string | null;
        projectId: number;
        startDate: Date;
        endDate: Date;
        implementer: string;
        implementerUnit: string;
        fiscalYear: number;
        domainInterventionId: number;
        inputId: number;
    }>;
    findAll(page?: number, limit?: number, projectId?: number, fiscalYear?: number): Promise<{
        data: ({
            domainIntervention: {
                parent: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    description: string | null;
                    userId: number | null;
                    parentId: number | null;
                    domainName: string;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                description: string | null;
                userId: number | null;
                parentId: number | null;
                domainName: string;
            };
            project: {
                mouApplication: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    userId: number | null;
                    applicationKey: string;
                    mouId: number;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                organizationId: number;
                description: string | null;
                duration: number;
                budgetTypeId: number;
                fundingUnitId: number;
                fundingSourceId: number;
                mouApplicationId: number;
            };
            input: {
                inputSubclass: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    name: string;
                    inputId: number;
                    subclassId: number;
                    budget: number;
                }[];
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            description: string | null;
            projectId: number;
            startDate: Date;
            endDate: Date;
            implementer: string;
            implementerUnit: string;
            fiscalYear: number;
            domainInterventionId: number;
            inputId: number;
        })[];
        meta: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    findOne(id: number): Promise<{
        domainIntervention: {
            parent: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                description: string | null;
                userId: number | null;
                parentId: number | null;
                domainName: string;
            };
            children: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                description: string | null;
                userId: number | null;
                parentId: number | null;
                domainName: string;
            }[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            userId: number | null;
            parentId: number | null;
            domainName: string;
        };
        project: {
            budgetType: {
                id: number;
                typeName: string;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
            };
            fundingUnit: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                unitName: string;
            };
            fundingSource: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                sourceName: string;
            };
            mouApplication: {
                mou: {
                    party: {
                        organization: {
                            id: number;
                            createdAt: Date;
                            updatedAt: Date;
                            deleted: boolean;
                            organizationName: string;
                            organizationPhoneNumber: string;
                            organizationEmail: string;
                            organizationWebsite: string | null;
                            homeCountryRepresentative: string;
                            rwandaRepresentative: string;
                            organizationRgbNumber: string | null;
                            organizationTypeId: number;
                            centralLevelInstitutionId: number | null;
                        };
                    } & {
                        id: number;
                        createdAt: Date;
                        updatedAt: Date;
                        deleted: boolean;
                        name: string;
                        organizationId: number;
                        duration: number;
                        partyId: string;
                        responsibilityId: number | null;
                        objectiveId: number | null;
                        goalId: number;
                        signatory: string;
                        position: string;
                        reasonForExtendedDuration: string | null;
                    };
                } & {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    mouKey: string;
                    partyId: number;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                userId: number | null;
                applicationKey: string;
                mouId: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            duration: number;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            mouApplicationId: number;
        };
        input: {
            inputSubclass: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                inputId: number;
                subclassId: number;
                budget: number;
            }[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        description: string | null;
        projectId: number;
        startDate: Date;
        endDate: Date;
        implementer: string;
        implementerUnit: string;
        fiscalYear: number;
        domainInterventionId: number;
        inputId: number;
    }>;
    update(id: number, updateActivityDto: UpdateActivityDto): Promise<{
        domainIntervention: {
            parent: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                description: string | null;
                userId: number | null;
                parentId: number | null;
                domainName: string;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            userId: number | null;
            parentId: number | null;
            domainName: string;
        };
        project: {
            mouApplication: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                userId: number | null;
                applicationKey: string;
                mouId: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            duration: number;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            mouApplicationId: number;
        };
        input: {
            inputSubclass: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                inputId: number;
                subclassId: number;
                budget: number;
            }[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        description: string | null;
        projectId: number;
        startDate: Date;
        endDate: Date;
        implementer: string;
        implementerUnit: string;
        fiscalYear: number;
        domainInterventionId: number;
        inputId: number;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    getActivitiesByProject(projectId: number): Promise<({
        domainIntervention: {
            parent: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                description: string | null;
                userId: number | null;
                parentId: number | null;
                domainName: string;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            userId: number | null;
            parentId: number | null;
            domainName: string;
        };
        input: {
            inputSubclass: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                inputId: number;
                subclassId: number;
                budget: number;
            }[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        description: string | null;
        projectId: number;
        startDate: Date;
        endDate: Date;
        implementer: string;
        implementerUnit: string;
        fiscalYear: number;
        domainInterventionId: number;
        inputId: number;
    })[]>;
    getActivitiesByFiscalYear(fiscalYear: number): Promise<({
        domainIntervention: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            userId: number | null;
            parentId: number | null;
            domainName: string;
        };
        project: {
            organization: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                organizationName: string;
                organizationPhoneNumber: string;
                organizationEmail: string;
                organizationWebsite: string | null;
                homeCountryRepresentative: string;
                rwandaRepresentative: string;
                organizationRgbNumber: string | null;
                organizationTypeId: number;
                centralLevelInstitutionId: number | null;
            };
            mouApplication: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                userId: number | null;
                applicationKey: string;
                mouId: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            duration: number;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            mouApplicationId: number;
        };
        input: {
            inputSubclass: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                inputId: number;
                subclassId: number;
                budget: number;
            }[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        description: string | null;
        projectId: number;
        startDate: Date;
        endDate: Date;
        implementer: string;
        implementerUnit: string;
        fiscalYear: number;
        domainInterventionId: number;
        inputId: number;
    })[]>;
    private validateRelationships;
}
