import { Injectable, NotFoundException, ConflictException, ForbiddenException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateBudgetTypeDto, UpdateBudgetTypeDto } from './dto';

@Injectable()
export class BudgetTypesService {
    private readonly logger = new Logger(BudgetTypesService.name);

    constructor(private prisma: PrismaService) {}

    async findAll() {
        try {
            const budgetTypes = await this.prisma.budgetType.findMany({
                where: { deleted: false },
                orderBy: { createdAt: 'desc' }
            });

            return budgetTypes;
        } catch (error) {
            this.logger.error('Failed to fetch budget types', error.stack);
            throw new Error('Failed to fetch budget types');
        }
    }

    async findOne(id: number) {
        try {
            const budgetType = await this.prisma.budgetType.findUnique({
                where: { id, deleted: false }
            });

            if (!budgetType) {
                throw new NotFoundException('Budget type not found');
            }

            return budgetType;
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            this.logger.error(`Failed to fetch budget type with ID ${id}`, error.stack);
            throw new Error('Failed to fetch budget type');
        }
    }

    async create(createBudgetTypeDto: CreateBudgetTypeDto, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can create budget types
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can create budget types');
            }

            // Check if budget type with same name already exists
            const existingType = await this.prisma.budgetType.findFirst({
                where: { 
                    typeName: createBudgetTypeDto.typeName,
                    deleted: false 
                }
            });

            if (existingType) {
                throw new ConflictException('Budget type with this name already exists');
            }

            const budgetType = await this.prisma.budgetType.create({
                data: {
                    typeName: createBudgetTypeDto.typeName
                }
            });

            return budgetType;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to create budget type', error.stack);
            throw new Error('Failed to create budget type');
        }
    }

    async update(id: number, updateBudgetTypeDto: UpdateBudgetTypeDto, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can update budget types
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can update budget types');
            }

            // Check if budget type exists
            const existingType = await this.prisma.budgetType.findUnique({
                where: { id, deleted: false }
            });

            if (!existingType) {
                throw new NotFoundException('Budget type not found');
            }

            // Check if new name conflicts with existing types
            if (updateBudgetTypeDto.typeName && updateBudgetTypeDto.typeName !== existingType.typeName) {
                const nameConflict = await this.prisma.budgetType.findFirst({
                    where: { 
                        typeName: updateBudgetTypeDto.typeName,
                        deleted: false,
                        id: { not: id }
                    }
                });

                if (nameConflict) {
                    throw new ConflictException('Budget type with this name already exists');
                }
            }

            const budgetType = await this.prisma.budgetType.update({
                where: { id },
                data: updateBudgetTypeDto
            });

            return budgetType;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to update budget type with ID ${id}`, error.stack);
            throw new Error('Failed to update budget type');
        }
    }

    async remove(id: number, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can delete budget types
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can delete budget types');
            }

            // Check if budget type exists
            const existingType = await this.prisma.budgetType.findUnique({
                where: { id, deleted: false }
            });

            if (!existingType) {
                throw new NotFoundException('Budget type not found');
            }

            // Check if budget type is being used by any projects
            const projectsUsingType = await this.prisma.project.findFirst({
                where: { 
                    budgetTypeId: id,
                    deleted: false 
                }
            });

            if (projectsUsingType) {
                throw new ConflictException('Cannot delete budget type that is being used by projects');
            }

            // Soft delete the budget type
            await this.prisma.budgetType.update({
                where: { id },
                data: { deleted: true }
            });

            return { message: 'Budget type deleted successfully' };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to delete budget type with ID ${id}`, error.stack);
            throw new Error('Failed to delete budget type');
        }
    }
}
