"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  CheckCircle, 
  AlertTriangle, 
  Edit, 
  FileText, 
  Building, 
  User, 
  Calendar, 
  DollarSign,
  File,
  Send,
  Info
} from "lucide-react"
import { 
  Step5Data, 
  MouApplication, 
  Step1Data, 
  Step2Data, 
  Step3Data, 
  Step4Data 
} from "@/lib/services/mou-application.service"

interface Step5ReviewSubmitProps {
  data: Step5Data
  onChange: (data: Step5Data) => void
  application: MouApplication | null
  allStepData: {
    step1: Step1Data
    step2: Step2Data
    step3: Step3Data
    step4: Step4Data
    step5: Step5Data
  }
  onSubmit: () => void
}

export function Step5ReviewSubmit({ 
  data, 
  onChange, 
  application, 
  allStepData, 
  onSubmit 
}: Step5ReviewSubmitProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleTermsChange = (checked: boolean) => {
    onChange({
      ...data,
      termsAccepted: checked
    })
  }

  const handleFinalReviewChange = (checked: boolean) => {
    onChange({
      ...data,
      finalReview: checked
    })
  }

  const handleSubmit = async () => {
    if (!data.termsAccepted || !data.finalReview) {
      return
    }

    setIsSubmitting(true)
    try {
      await onSubmit()
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatCurrency = (amount: number | undefined, currency: string = "USD") => {
    if (!amount) return "Not specified"
    return `${amount.toLocaleString()} ${currency}`
  }

  const getTotalProjectBudget = () => {
    return allStepData.step3.projects.reduce((total, project) => {
      return total + (project.totalBudget || 0)
    }, 0)
  }

  const getValidationIssues = () => {
    const issues: string[] = []

    // Step 1 validation
    if (!allStepData.step1.mouDurationYears || allStepData.step1.mouDurationYears < 1) {
      issues.push("Valid MoU duration is required")
    }

    // Step 2 validation
    if (!allStepData.step2.partyName.trim()) {
      issues.push("Party name is required")
    }
    if (!allStepData.step2.signatoryName.trim()) {
      issues.push("Signatory name is required")
    }
    if (!allStepData.step2.signatoryPosition.trim()) {
      issues.push("Signatory position is required")
    }
    if (!allStepData.step2.responsibilities.some(r => r.trim())) {
      issues.push("At least one responsibility is required")
    }

    // Step 3 validation
    if (allStepData.step3.projects.length === 0) {
      issues.push("At least one project is required")
    }
    const hasValidProjects = allStepData.step3.projects.some(p => 
      p.projectName.trim() && p.startDate && p.endDate && p.totalBudget
    )
    if (!hasValidProjects) {
      issues.push("Complete project information is required")
    }

    return issues
  }

  const validationIssues = getValidationIssues()
  const canSubmit = validationIssues.length === 0 && data.termsAccepted && data.finalReview

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-cyan-900 mb-2">Review & Submit</h3>
        <p className="text-muted-foreground">
          Review all information before submitting your MoU application. Once submitted, changes will require approval.
        </p>
      </div>

      {/* Validation Issues */}
      {validationIssues.length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <p className="font-medium mb-2">Please resolve the following issues before submitting:</p>
            <ul className="list-disc list-inside space-y-1">
              {validationIssues.map((issue, index) => (
                <li key={index}>{issue}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Step 1 Review: MoU Information */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-cyan-600" />
              MoU Information
            </CardTitle>
            <Button variant="ghost" size="sm">
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Organization</Label>
              <p className="font-medium">{allStepData.step1.organizationName}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">MoU Duration</Label>
              <p className="font-medium">{allStepData.step1.mouDurationYears} years</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step 2 Review: Party Details */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5 text-cyan-600" />
              Party Details
            </CardTitle>
            <Button variant="ghost" size="sm">
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Party Name</Label>
              <p className="font-medium">{allStepData.step2.partyName}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Signatory</Label>
              <p className="font-medium">{allStepData.step2.signatoryName}</p>
              <p className="text-sm text-muted-foreground">{allStepData.step2.signatoryPosition}</p>
            </div>
          </div>
          
          <div>
            <Label className="text-sm font-medium text-muted-foreground">Responsibilities</Label>
            <div className="mt-2 space-y-2">
              {allStepData.step2.responsibilities
                .filter(r => r.trim())
                .map((responsibility, index) => (
                  <div key={index} className="p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm">{responsibility}</p>
                  </div>
                ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Step 3 Review: Projects */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-cyan-600" />
              Projects ({allStepData.step3.projects.length})
            </CardTitle>
            <Button variant="ghost" size="sm">
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-3 bg-cyan-50 rounded-lg">
            <span className="font-medium text-cyan-900">Total Budget</span>
            <span className="font-bold text-cyan-900">
              ${getTotalProjectBudget().toLocaleString()} USD
            </span>
          </div>

          {allStepData.step3.projects.map((project, index) => (
            <div key={index} className="border rounded-lg p-4 space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">{project.projectName || `Project ${index + 1}`}</h4>
                <Badge variant="outline">
                  {project.activities.length} activit{project.activities.length !== 1 ? 'ies' : 'y'}
                </Badge>
              </div>
              
              {project.projectDescription && (
                <p className="text-sm text-muted-foreground">{project.projectDescription}</p>
              )}
              
              <div className="grid gap-2 md:grid-cols-3 text-sm">
                <div>
                  <Label className="text-xs text-muted-foreground">Duration</Label>
                  <p>{project.startDate} to {project.endDate}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Budget</Label>
                  <p>{formatCurrency(project.totalBudget, project.currency)}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Currency</Label>
                  <p>{project.currency}</p>
                </div>
              </div>

              {project.activities.filter(a => a.activityName.trim()).length > 0 && (
                <div>
                  <Label className="text-xs text-muted-foreground">Activities</Label>
                  <div className="mt-1 space-y-1">
                    {project.activities
                      .filter(a => a.activityName.trim())
                      .map((activity, actIndex) => (
                        <div key={actIndex} className="text-sm p-2 bg-gray-50 rounded">
                          <span className="font-medium">{activity.activityName}</span>
                          {activity.budgetAllocation && (
                            <span className="ml-2 text-muted-foreground">
                              ({formatCurrency(activity.budgetAllocation, activity.currency)})
                            </span>
                          )}
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Step 4 Review: Documents */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <File className="h-5 w-5 text-cyan-600" />
              Documents
            </CardTitle>
            <Button variant="ghost" size="sm">
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {application?.documents && application.documents.length > 0 ? (
            <div className="space-y-2">
              {application.documents.map((doc, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <File className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">{doc.originalName}</p>
                      <p className="text-sm text-muted-foreground">{doc.documentType}</p>
                    </div>
                  </div>
                  {doc.isRequired && (
                    <Badge variant="destructive" className="text-xs">Required</Badge>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground text-center py-4">No documents uploaded</p>
          )}
        </CardContent>
      </Card>

      {/* Final Checks */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-cyan-600" />
            Final Confirmation
          </CardTitle>
          <CardDescription>
            Please confirm the following before submitting your application
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-start space-x-3">
            <Checkbox
              id="finalReview"
              checked={data.finalReview}
              onCheckedChange={handleFinalReviewChange}
            />
            <div className="space-y-1">
              <Label htmlFor="finalReview" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                I have reviewed all information and confirm it is accurate and complete
              </Label>
              <p className="text-xs text-muted-foreground">
                Please ensure all details are correct as changes after submission will require approval
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <Checkbox
              id="terms"
              checked={data.termsAccepted}
              onCheckedChange={handleTermsChange}
            />
            <div className="space-y-1">
              <Label htmlFor="terms" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                I accept the terms and conditions of the MoU application process
              </Label>
              <p className="text-xs text-muted-foreground">
                By submitting this application, you agree to the Ministry of Health's terms and conditions
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submission */}
      <Card className="bg-green-50 border-green-200">
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center">
              <Send className="h-8 w-8 text-green-600" />
            </div>
            <div>
              <h4 className="font-semibold text-green-900">Ready to Submit</h4>
              <p className="text-sm text-green-800 mt-1">
                Your application will be submitted to the Ministry of Health for review
              </p>
            </div>
            
            <Button
              onClick={handleSubmit}
              disabled={!canSubmit || isSubmitting}
              size="lg"
              className="bg-green-600 hover:bg-green-700"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Submitting...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Submit Application
                </>
              )}
            </Button>

            {!canSubmit && (
              <Alert className="bg-amber-50 border-amber-200 text-left">
                <Info className="h-4 w-4" />
                <AlertDescription className="text-amber-800">
                  Please complete all required fields and accept the terms and conditions to submit your application.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
