import { Injectable, CanActivate, ExecutionContext, SetMetadata, applyDecorators } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserRole } from '@prisma/client';
import { BaseRole, ReviewerRole, ApproverRole, ResourceType, PermissionLevel, hasPermission } from '../../common/enums/roles.enum';

// Mapping function to convert UserRole to permission system roles
function mapUserRoleToPermissionRoles(userRole: UserRole): (BaseRole | ReviewerRole | ApproverRole)[] {
  switch (userRole) {
    case UserRole.ADMIN:
      return [BaseRole.ADMIN];
    case UserRole.PARTNER:
      return [BaseRole.PARTNER];
    case UserRole.PARTNER_COORDINATOR:
      return [ReviewerRole.PARTNER_COORDINATOR];
    case UserRole.TECHNICAL_EXPERT:
      return [ReviewerRole.TECHNICAL_EXPERT];
    case UserRole.LEGAL_OFFICER:
      return [ReviewerRole.LEGAL_OFFICER, ApproverRole.LEGAL_OFFICER];
    case UserRole.HEAD_OF_DEPARTMENT:
      return [ApproverRole.HEAD_OF_DEPARTMENT];
    case UserRole.PermanentSecretary:
      return [ApproverRole.PERMANENT_SECRETARY];
    case UserRole.MINISTER:
      return [ApproverRole.MINISTER];
    default:
      return [BaseRole.STAFF];
  }
}

export const ROLES_KEY = 'roles';
export const RESOURCE_KEY = 'resource';
export const PERMISSION_KEY = 'permission';

export const Roles = (...roles: UserRole[]) => SetMetadata(ROLES_KEY, roles);
export const RequiresResource = (resource: ResourceType) => SetMetadata(RESOURCE_KEY, resource);
export const RequiresPermission = (level: PermissionLevel) => SetMetadata(PERMISSION_KEY, level);

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<UserRole[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    const requiredResource = this.reflector.getAllAndOverride<ResourceType>(RESOURCE_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    const requiredPermission = this.reflector.getAllAndOverride<PermissionLevel>(PERMISSION_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles && !requiredResource) {
      return true; // No role or resource requirements
    }

    const { user } = context.switchToHttp().getRequest();
    if (!user) {
      return false;
    }

    // Map UserRole to permission system roles
    const userPermissionRoles = mapUserRoleToPermissionRoles(user.role);

    // Check role requirements if specified
    if (requiredRoles) {
      // For role checking, we need to check if the user's UserRole matches any required roles
      const hasRequiredRole = requiredRoles.includes(user.role);
      if (!hasRequiredRole) {
        return false;
      }
    }

    // Check resource permission if specified
    if (requiredResource && requiredPermission) {
      return hasPermission(userPermissionRoles, requiredResource, requiredPermission);
    }

    return true;
  }
}

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const resource = this.reflector.getAllAndOverride<ResourceType>(RESOURCE_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    const requiredPermission = this.reflector.getAllAndOverride<PermissionLevel>(PERMISSION_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!resource || !requiredPermission) {
      return true; // No specific permission requirements
    }

    const { user } = context.switchToHttp().getRequest();
    if (!user) {
      return false;
    }

    const userPermissionRoles = mapUserRoleToPermissionRoles(user.role);
    return hasPermission(userPermissionRoles, resource, requiredPermission);
  }
}

export const Auth = (roles: UserRole[] = [], resource?: ResourceType, permission?: PermissionLevel) => {
  return applyDecorators(
    SetMetadata(ROLES_KEY, roles),
    ...(resource ? [SetMetadata(RESOURCE_KEY, resource)] : []),
    ...(permission ? [SetMetadata(PERMISSION_KEY, permission)] : [])
  );
};