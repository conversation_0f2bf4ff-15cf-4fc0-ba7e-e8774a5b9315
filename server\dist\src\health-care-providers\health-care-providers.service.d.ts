import { PrismaService } from '../prisma/prisma.service';
import { CreateHealthCareProviderDto, UpdateHealthCareProviderDto } from './dto';
export declare class HealthCareProvidersService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    create(createHealthCareProviderDto: CreateHealthCareProviderDto, userId: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        providerName: string;
        location: string | null;
        contactEmail: string | null;
        contactPhone: string | null;
        website: string | null;
    }>;
    findAll(): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        providerName: string;
        location: string | null;
        contactEmail: string | null;
        contactPhone: string | null;
        website: string | null;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        providerName: string;
        location: string | null;
        contactEmail: string | null;
        contactPhone: string | null;
        website: string | null;
    }>;
    update(id: number, updateHealthCareProviderDto: UpdateHealthCareProviderDto, userId: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        providerName: string;
        location: string | null;
        contactEmail: string | null;
        contactPhone: string | null;
        website: string | null;
    }>;
    remove(id: number, userId: string): Promise<{
        success: boolean;
    }>;
}
