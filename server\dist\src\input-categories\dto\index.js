"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InputCategoryTreeResponseDto = exports.InputCategoryResponseDto = exports.UpdateInputCategoryDto = exports.CreateInputCategoryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateInputCategoryDto {
}
exports.CreateInputCategoryDto = CreateInputCategoryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The name of the input category' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateInputCategoryDto.prototype, "categoryName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Description of the input category', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateInputCategoryDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID of the parent category (for hierarchical structure)', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateInputCategoryDto.prototype, "parentId", void 0);
class UpdateInputCategoryDto {
}
exports.UpdateInputCategoryDto = UpdateInputCategoryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The name of the input category', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateInputCategoryDto.prototype, "categoryName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Description of the input category', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateInputCategoryDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID of the parent category', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateInputCategoryDto.prototype, "parentId", void 0);
class InputCategoryResponseDto {
}
exports.InputCategoryResponseDto = InputCategoryResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], InputCategoryResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The name of the input category' }),
    __metadata("design:type", String)
], InputCategoryResponseDto.prototype, "categoryName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Description of the input category' }),
    __metadata("design:type", String)
], InputCategoryResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID of the parent category' }),
    __metadata("design:type", Number)
], InputCategoryResponseDto.prototype, "parentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Parent category details', required: false }),
    __metadata("design:type", InputCategoryResponseDto)
], InputCategoryResponseDto.prototype, "parent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Child categories', type: [InputCategoryResponseDto] }),
    __metadata("design:type", Array)
], InputCategoryResponseDto.prototype, "children", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'When the input category was created' }),
    __metadata("design:type", Date)
], InputCategoryResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'When the input category was last updated' }),
    __metadata("design:type", Date)
], InputCategoryResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Whether the input category has been soft deleted' }),
    __metadata("design:type", Boolean)
], InputCategoryResponseDto.prototype, "deleted", void 0);
class InputCategoryTreeResponseDto extends InputCategoryResponseDto {
}
exports.InputCategoryTreeResponseDto = InputCategoryTreeResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Child categories', type: [InputCategoryTreeResponseDto] }),
    __metadata("design:type", Array)
], InputCategoryTreeResponseDto.prototype, "children", void 0);
//# sourceMappingURL=index.js.map