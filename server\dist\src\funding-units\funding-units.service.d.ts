import { PrismaService } from '../prisma/prisma.service';
import { CreateFundingUnitDto, UpdateFundingUnitDto } from './dto';
export declare class FundingUnitsService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    findAll(): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        unitName: string;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        unitName: string;
    }>;
    create(createFundingUnitDto: CreateFundingUnitDto, currentUserId: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        unitName: string;
    }>;
    update(id: number, updateFundingUnitDto: UpdateFundingUnitDto, currentUserId: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        unitName: string;
    }>;
    remove(id: number, currentUserId: string): Promise<{
        message: string;
    }>;
}
