import { AdminDashboardService } from './admin-dashboard.service';
export declare class AdminDashboardController {
    private readonly dashboardService;
    constructor(dashboardService: AdminDashboardService);
    getDashboardOverview(): Promise<{
        userStats: {
            totalUsers: number;
            activeUsers: number;
            inactiveUsers: number;
            recentlyActive: number;
            roleDistribution: {
                roles: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.UserGroupByOutputType, "role"[]> & {
                    _count: {
                        role: number;
                    };
                })[];
            };
            departmentDistribution: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.UserGroupByOutputType, "organizationId"[]> & {
                _count: {
                    organizationId: number;
                };
            })[];
        };
        activityMetrics: {
            loginActivity: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.ActivityLogGroupByOutputType, "createdAt"[]> & {
                _count: {
                    action: number;
                };
            })[];
            userActions: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.ActivityLogGroupByOutputType, "action"[]> & {
                _count: {
                    action: number;
                };
            })[];
            reviewActivity: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.ApprovalStepGroupByOutputType, "status"[]> & {
                _count: {
                    status: number;
                };
            })[];
            approvalActivity: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.ApprovalStepGroupByOutputType, "status"[]> & {
                _count: {
                    status: number;
                };
            })[];
        };
        systemHealth: {
            activeApplications: number;
            pendingReviews: number;
            pendingApprovals: number;
            recentErrors: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                mouApplicationId: number | null;
                details: import("@prisma/client/runtime/library").JsonValue;
                action: string;
                category: string;
                importance: string;
                ipAddress: string | null;
                userAgent: string | null;
            }[];
            systemStatus: {
                status: string;
                lastCheck: Date;
                components: {
                    database: string;
                    email: string;
                    storage: string;
                    notifications: string;
                };
            };
        };
        performanceMetrics: {
            averageReviewTime: number;
            averageApprovalTime: number;
            applicationThroughput: {
                status: string;
                _count: {
                    status: number;
                };
            }[];
            userProductivity: any[];
        };
    }>;
    getUserStatistics(): Promise<{
        totalUsers: number;
        activeUsers: number;
        inactiveUsers: number;
        recentlyActive: number;
        roleDistribution: {
            roles: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.UserGroupByOutputType, "role"[]> & {
                _count: {
                    role: number;
                };
            })[];
        };
        departmentDistribution: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.UserGroupByOutputType, "organizationId"[]> & {
            _count: {
                organizationId: number;
            };
        })[];
    }>;
    getActivityMetrics(startDate?: string, endDate?: string): Promise<{
        loginActivity: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.ActivityLogGroupByOutputType, "createdAt"[]> & {
            _count: {
                action: number;
            };
        })[];
        userActions: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.ActivityLogGroupByOutputType, "action"[]> & {
            _count: {
                action: number;
            };
        })[];
        reviewActivity: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.ApprovalStepGroupByOutputType, "status"[]> & {
            _count: {
                status: number;
            };
        })[];
        approvalActivity: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.ApprovalStepGroupByOutputType, "status"[]> & {
            _count: {
                status: number;
            };
        })[];
    }>;
    getSystemHealth(): Promise<{
        activeApplications: number;
        pendingReviews: number;
        pendingApprovals: number;
        recentErrors: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            mouApplicationId: number | null;
            details: import("@prisma/client/runtime/library").JsonValue;
            action: string;
            category: string;
            importance: string;
            ipAddress: string | null;
            userAgent: string | null;
        }[];
        systemStatus: {
            status: string;
            lastCheck: Date;
            components: {
                database: string;
                email: string;
                storage: string;
                notifications: string;
            };
        };
    }>;
    getPerformanceMetrics(): Promise<{
        averageReviewTime: number;
        averageApprovalTime: number;
        applicationThroughput: {
            status: string;
            _count: {
                status: number;
            };
        }[];
        userProductivity: any[];
    }>;
    getReviewStatistics(startDate?: string, endDate?: string): Promise<(import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.ApprovalStepGroupByOutputType, "status"[]> & {
        _count: {
            status: number;
        };
    })[]>;
    getApprovalStatistics(startDate?: string, endDate?: string): Promise<(import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.ApprovalStepGroupByOutputType, "status"[]> & {
        _count: {
            status: number;
        };
    })[]>;
    getActiveUsers(period?: 'daily' | 'weekly' | 'monthly', startDate?: string, endDate?: string): Promise<(import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.ActivityLogGroupByOutputType, "createdAt"[]> & {
        _count: {
            action: number;
        };
    })[]>;
    getApplicationThroughput(period?: 'daily' | 'weekly' | 'monthly', startDate?: string, endDate?: string): Promise<{
        status: string;
        _count: {
            status: number;
        };
    }[]>;
    getUserProductivity(startDate?: string, endDate?: string, department?: string, role?: string): Promise<any[]>;
    getSystemErrors(startDate?: string, endDate?: string, severity?: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        mouApplicationId: number | null;
        details: import("@prisma/client/runtime/library").JsonValue;
        action: string;
        category: string;
        importance: string;
        ipAddress: string | null;
        userAgent: string | null;
    }[]>;
    getDepartmentPerformance(startDate?: string, endDate?: string): Promise<{
        reviewTimes: number;
        approvalTimes: number;
        throughput: {
            status: string;
            _count: {
                status: number;
            };
        }[];
    }>;
    getRoleActivity(startDate?: string, endDate?: string): Promise<{
        distribution: {
            roles: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.UserGroupByOutputType, "role"[]> & {
                _count: {
                    role: number;
                };
            })[];
        };
        activity: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.ActivityLogGroupByOutputType, "action"[]> & {
            _count: {
                action: number;
            };
        })[];
    }>;
    getNotificationStatistics(startDate?: string, endDate?: string): Promise<{}>;
    getAuditSummary(startDate?: string, endDate?: string): Promise<{}>;
}
