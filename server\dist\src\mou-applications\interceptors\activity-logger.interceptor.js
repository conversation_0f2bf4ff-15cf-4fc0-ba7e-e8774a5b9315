"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityLoggerInterceptor = void 0;
const common_1 = require("@nestjs/common");
const operators_1 = require("rxjs/operators");
const prisma_service_1 = require("../../prisma/prisma.service");
let ActivityLoggerInterceptor = class ActivityLoggerInterceptor {
    constructor(prisma) {
        this.prisma = prisma;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        const method = request.method;
        const url = request.url;
        const applicationId = this.extractApplicationId(url);
        return next.handle().pipe((0, operators_1.tap)(async () => {
            if (applicationId && user) {
                const action = this.determineAction(method, url);
                await this.prisma.activityLog.create({
                    data: {
                        mouApplicationId: applicationId,
                        action,
                        details: {
                            userId: user.id,
                            method,
                            url,
                            userRole: user.baseRole,
                            reviewerRole: user.reviewerRole,
                            approverRole: user.approverRole
                        },
                        category: this.determineCategory(url),
                        importance: this.determineImportance(method, url),
                        ipAddress: request.ip,
                        userAgent: request.headers['user-agent']
                    }
                });
            }
        }));
    }
    extractApplicationId(url) {
        const match = url.match(/\/mou-applications\/(\d+)/);
        return match ? parseInt(match[1]) : null;
    }
    determineAction(method, url) {
        if (url.includes('/review')) {
            return 'REVIEW_ADDED';
        }
        if (url.includes('/assign-expert')) {
            return 'EXPERT_ASSIGNED';
        }
        if (url.includes('/request-modification')) {
            return 'MODIFICATION_REQUESTED';
        }
        if (url.includes('/approve')) {
            return 'APPLICATION_APPROVED';
        }
        switch (method) {
            case 'POST':
                return 'CREATE';
            case 'PUT':
                return 'UPDATE';
            case 'DELETE':
                return 'DELETE';
            default:
                return 'VIEW';
        }
    }
    determineCategory(url) {
        if (url.includes('/review')) {
            return 'REVIEW';
        }
        if (url.includes('/assign-expert')) {
            return 'ASSIGNMENT';
        }
        if (url.includes('/request-modification')) {
            return 'MODIFICATION';
        }
        if (url.includes('/approve')) {
            return 'APPROVAL';
        }
        return 'GENERAL';
    }
    determineImportance(method, url) {
        if (url.includes('/approve') || url.includes('/review')) {
            return 'HIGH';
        }
        if (method === 'POST' || method === 'PUT') {
            return 'MEDIUM';
        }
        return 'INFO';
    }
};
exports.ActivityLoggerInterceptor = ActivityLoggerInterceptor;
exports.ActivityLoggerInterceptor = ActivityLoggerInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ActivityLoggerInterceptor);
//# sourceMappingURL=activity-logger.interceptor.js.map