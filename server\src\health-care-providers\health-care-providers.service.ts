import { Injectable, ConflictException, NotFoundException, ForbiddenException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateHealthCareProviderDto, UpdateHealthCareProviderDto } from './dto';

@Injectable()
export class HealthCareProvidersService {
    private readonly logger = new Logger(HealthCareProvidersService.name);

    constructor(private prisma: PrismaService) {}

    async create(createHealthCareProviderDto: CreateHealthCareProviderDto, userId: string) {
        try {
            // Check if user has admin role
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            if (user.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can create healthcare providers');
            }

            // Check if healthcare provider already exists
            const existing = await this.prisma.healthCareProvider.findFirst({
                where: {
                    providerName: createHealthCareProviderDto.providerName,
                    deleted: false
                }
            });

            if (existing) {
                throw new ConflictException('Healthcare provider with this name already exists');
            }

            // Create the healthcare provider
            const provider = await this.prisma.healthCareProvider.create({
                data: {
                    providerName: createHealthCareProviderDto.providerName,
                    description: createHealthCareProviderDto.description,
                    location: createHealthCareProviderDto.location,
                    contactEmail: createHealthCareProviderDto.contactEmail,
                    contactPhone: createHealthCareProviderDto.contactPhone,
                    website: createHealthCareProviderDto.website
                }
            });

            return provider;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to create healthcare provider', error.stack);
            throw new Error('Failed to create healthcare provider');
        }
    }

    async findAll() {
        return this.prisma.healthCareProvider.findMany({
            where: {
                deleted: false
            },
            orderBy: {
                providerName: 'asc'
            }
        });
    }

    async findOne(id: number) {
        const provider = await this.prisma.healthCareProvider.findFirst({
            where: {
                id,
                deleted: false
            }
        });

        if (!provider) {
            throw new NotFoundException('Healthcare provider not found');
        }

        return provider;
    }

    async update(id: number, updateHealthCareProviderDto: UpdateHealthCareProviderDto, userId: string) {
        try {
            // Check if user has admin role
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            if (user.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can update healthcare providers');
            }

            // Check if healthcare provider exists
            const existing = await this.prisma.healthCareProvider.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });

            if (!existing) {
                throw new NotFoundException('Healthcare provider not found');
            }

            // Check if name is being changed and if so, if it conflicts
            if (updateHealthCareProviderDto.providerName && 
                updateHealthCareProviderDto.providerName !== existing.providerName) {
                const nameConflict = await this.prisma.healthCareProvider.findFirst({
                    where: {
                        providerName: updateHealthCareProviderDto.providerName,
                        deleted: false,
                        id: { not: id }
                    }
                });

                if (nameConflict) {
                    throw new ConflictException('Another healthcare provider with this name already exists');
                }
            }

            // Update the healthcare provider
            const updated = await this.prisma.healthCareProvider.update({
                where: { id },
                data: {
                    providerName: updateHealthCareProviderDto.providerName,
                    description: updateHealthCareProviderDto.description,
                    location: updateHealthCareProviderDto.location,
                    contactEmail: updateHealthCareProviderDto.contactEmail,
                    contactPhone: updateHealthCareProviderDto.contactPhone,
                    website: updateHealthCareProviderDto.website
                }
            });

            return updated;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to update healthcare provider', error.stack);
            throw new Error('Failed to update healthcare provider');
        }
    }

    async remove(id: number, userId: string) {
        try {
            // Check if user has admin role
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            if (user.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can delete healthcare providers');
            }

            // Check if healthcare provider exists
            const existing = await this.prisma.healthCareProvider.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });

            if (!existing) {
                throw new NotFoundException('Healthcare provider not found');
            }

            // For now, just soft delete the healthcare provider as there are no direct dependencies
            // If dependencies are added later, add checks here
            await this.prisma.healthCareProvider.update({
                where: { id },
                data: { deleted: true }
            });

            return { success: true };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to delete healthcare provider', error.stack);
            throw new Error('Failed to delete healthcare provider');
        }
    }
}
