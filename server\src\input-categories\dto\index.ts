import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsNumber } from 'class-validator';

export class CreateInputCategoryDto {
    @ApiProperty({ description: 'The name of the input category' })
    @IsNotEmpty()
    @IsString()
    categoryName: string;

    @ApiProperty({ description: 'Description of the input category', required: false })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'ID of the parent category (for hierarchical structure)', required: false })
    @IsOptional()
    @IsNumber()
    parentId?: number;
}

export class UpdateInputCategoryDto {
    @ApiProperty({ description: 'The name of the input category', required: false })
    @IsOptional()
    @IsString()
    categoryName?: string;

    @ApiProperty({ description: 'Description of the input category', required: false })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'ID of the parent category', required: false })
    @IsOptional()
    @IsNumber()
    parentId?: number;
}

export class InputCategoryResponseDto {
    @ApiProperty()
    id: number;

    @ApiProperty({ description: 'The name of the input category' })
    categoryName: string;

    @ApiProperty({ description: 'Description of the input category' })
    description?: string;

    @ApiProperty({ description: 'ID of the parent category' })
    parentId?: number;

    @ApiProperty({ description: 'Parent category details', required: false })
    parent?: InputCategoryResponseDto;

    @ApiProperty({ description: 'Child categories', type: [InputCategoryResponseDto] })
    children?: InputCategoryResponseDto[];

    @ApiProperty({ description: 'When the input category was created' })
    createdAt: Date;

    @ApiProperty({ description: 'When the input category was last updated' })
    updatedAt: Date;

    @ApiProperty({ description: 'Whether the input category has been soft deleted' })
    deleted: boolean;
}

// Special response type for tree structure
export class InputCategoryTreeResponseDto extends InputCategoryResponseDto {
    @ApiProperty({ description: 'Child categories', type: [InputCategoryTreeResponseDto] })
    children: InputCategoryTreeResponseDto[];
}
