import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { ActivitiesService } from './activities.service';
import { CreateActivityDto, UpdateActivityDto, ActivityResponseDto, ActivityWithRelationsDto } from './dto/create-activity.dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { RolesGuard } from '../auth/guard/roles.guard';
import { Roles } from '../auth/decorator/roles.decorator';
import { UserRole } from '../auth/dto';

@ApiTags('Activities')
@ApiBearerAuth()
@UseGuards(JwtGuard, RolesGuard)
@Controller('activities')
export class ActivitiesController {
  constructor(private readonly activitiesService: ActivitiesService) {}

  @Post()
  @Roles(UserRole.PARTNER, UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new activity' })
  @ApiResponse({
    status: 201,
    description: 'Activity created successfully',
    type: ActivityWithRelationsDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Related entity not found' })
  async create(@Body() createActivityDto: CreateActivityDto) {
    return this.activitiesService.create(createActivityDto);
  }

  @Get()
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get all activities with pagination' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10)' })
  @ApiQuery({ name: 'projectId', required: false, type: Number, description: 'Filter by project ID' })
  @ApiQuery({ name: 'fiscalYear', required: false, type: Number, description: 'Filter by fiscal year' })
  @ApiResponse({
    status: 200,
    description: 'List of activities',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/ActivityWithRelationsDto' },
        },
        meta: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            page: { type: 'number' },
            limit: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
      },
    },
  })
  async findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('projectId') projectId?: string,
    @Query('fiscalYear') fiscalYear?: string,
  ) {
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 10;
    const projectIdNum = projectId ? parseInt(projectId, 10) : undefined;
    const fiscalYearNum = fiscalYear ? parseInt(fiscalYear, 10) : undefined;

    return this.activitiesService.findAll(pageNum, limitNum, projectIdNum, fiscalYearNum);
  }

  @Get('by-project/:projectId')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get activities by project ID' })
  @ApiResponse({
    status: 200,
    description: 'List of activities for the project',
    type: [ActivityWithRelationsDto],
  })
  async getActivitiesByProject(@Param('projectId', ParseIntPipe) projectId: number) {
    return this.activitiesService.getActivitiesByProject(projectId);
  }

  @Get('by-fiscal-year/:fiscalYear')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get activities by fiscal year' })
  @ApiResponse({
    status: 200,
    description: 'List of activities for the fiscal year',
    type: [ActivityWithRelationsDto],
  })
  async getActivitiesByFiscalYear(@Param('fiscalYear', ParseIntPipe) fiscalYear: number) {
    return this.activitiesService.getActivitiesByFiscalYear(fiscalYear);
  }

  @Get(':id')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get an activity by ID' })
  @ApiResponse({
    status: 200,
    description: 'Activity details',
    type: ActivityWithRelationsDto,
  })
  @ApiResponse({ status: 404, description: 'Activity not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.activitiesService.findOne(id);
  }

  @Patch(':id')
  @Roles(UserRole.PARTNER, UserRole.ADMIN)
  @ApiOperation({ summary: 'Update an activity' })
  @ApiResponse({
    status: 200,
    description: 'Activity updated successfully',
    type: ActivityWithRelationsDto,
  })
  @ApiResponse({ status: 404, description: 'Activity not found' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateActivityDto: UpdateActivityDto,
  ) {
    return this.activitiesService.update(id, updateActivityDto);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete an activity (soft delete)' })
  @ApiResponse({
    status: 200,
    description: 'Activity deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Activity not found' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.activitiesService.remove(id);
  }
}
