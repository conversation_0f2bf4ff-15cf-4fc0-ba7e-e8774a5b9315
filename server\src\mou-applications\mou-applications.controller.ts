import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { MouApplicationsService } from './mou-applications.service';
import { CreateMouApplicationDto, UpdateMouApplicationDto, MouApplicationResponseDto, MouApplicationWithRelationsDto } from './dto/create-mou-application.dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { RolesGuard } from '../auth/guard/roles.guard';
import { Roles } from '../auth/decorator/roles.decorator';
import { UserRole } from '../auth/dto';

@ApiTags('MoU Applications')
@ApiBearerAuth()
@UseGuards(JwtGuard, RolesGuard)
@Controller('mou-applications')
export class MouApplicationsController {
  constructor(private readonly mouApplicationsService: MouApplicationsService) {}

  @Post()
  @Roles(UserRole.PARTNER, UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new MoU application' })
  @ApiResponse({
    status: 201,
    description: 'MoU application created successfully',
    type: MouApplicationWithRelationsDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'MoU or User not found' })
  @ApiResponse({ status: 409, description: 'Application key already exists' })
  async create(@Body() createMouApplicationDto: CreateMouApplicationDto) {
    return this.mouApplicationsService.create(createMouApplicationDto);
  }

  @Get()
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get all MoU applications with pagination' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10)' })
  @ApiQuery({ name: 'userId', required: false, type: Number, description: 'Filter by user ID' })
  @ApiResponse({
    status: 200,
    description: 'List of MoU applications',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/MouApplicationWithRelationsDto' },
        },
        meta: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            page: { type: 'number' },
            limit: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
      },
    },
  })
  async findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('userId') userId?: string,
    @Request() req?: any,
  ) {
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 10;
    const userIdNum = userId ? parseInt(userId, 10) : undefined;

    // If user is PARTNER, only show their applications
    const finalUserId = req.user?.role === UserRole.PARTNER ? req.user.id : userIdNum;

    return this.mouApplicationsService.findAll(pageNum, limitNum, finalUserId);
  }

  @Get('stats')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get MoU application statistics' })
  @ApiQuery({ name: 'userId', required: false, type: Number, description: 'Filter by user ID' })
  @ApiResponse({
    status: 200,
    description: 'Application statistics',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number' },
        pending: { type: 'number' },
        approved: { type: 'number' },
        rejected: { type: 'number' },
      },
    },
  })
  async getStats(@Query('userId') userId?: string, @Request() req?: any) {
    const userIdNum = userId ? parseInt(userId, 10) : undefined;
    
    // If user is PARTNER, only show their stats
    const finalUserId = req.user?.role === UserRole.PARTNER ? req.user.id : userIdNum;

    return this.mouApplicationsService.getApplicationStats(finalUserId);
  }

  @Get(':id')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get a MoU application by ID' })
  @ApiResponse({
    status: 200,
    description: 'MoU application details',
    type: MouApplicationWithRelationsDto,
  })
  @ApiResponse({ status: 404, description: 'MoU application not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.mouApplicationsService.findOne(id);
  }

  @Get('by-key/:applicationKey')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get a MoU application by application key' })
  @ApiResponse({
    status: 200,
    description: 'MoU application details',
    type: MouApplicationWithRelationsDto,
  })
  @ApiResponse({ status: 404, description: 'MoU application not found' })
  async findByKey(@Param('applicationKey') applicationKey: string) {
    return this.mouApplicationsService.findByApplicationKey(applicationKey);
  }

  @Patch(':id')
  @Roles(UserRole.PARTNER, UserRole.ADMIN)
  @ApiOperation({ summary: 'Update a MoU application' })
  @ApiResponse({
    status: 200,
    description: 'MoU application updated successfully',
    type: MouApplicationWithRelationsDto,
  })
  @ApiResponse({ status: 404, description: 'MoU application not found' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateMouApplicationDto: UpdateMouApplicationDto,
  ) {
    return this.mouApplicationsService.update(id, updateMouApplicationDto);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete a MoU application (soft delete)' })
  @ApiResponse({
    status: 200,
    description: 'MoU application deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'MoU application not found' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.mouApplicationsService.remove(id);
  }
}
