"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var DomainInterventionsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DomainInterventionsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let DomainInterventionsService = DomainInterventionsService_1 = class DomainInterventionsService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(DomainInterventionsService_1.name);
    }
    async create(createDomainInterventionDto, userId) {
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can create domain interventions');
            }
            if (createDomainInterventionDto.parentId) {
                const parent = await this.prisma.domainIntervention.findFirst({
                    where: {
                        id: createDomainInterventionDto.parentId,
                        deleted: false
                    }
                });
                if (!parent) {
                    throw new common_1.BadRequestException('Parent domain intervention not found');
                }
            }
            const existing = await this.prisma.domainIntervention.findFirst({
                where: {
                    domainName: createDomainInterventionDto.domainName,
                    parentId: createDomainInterventionDto.parentId,
                    deleted: false
                }
            });
            if (existing) {
                throw new common_1.ConflictException('Domain intervention with this name already exists at this level');
            }
            const domain = await this.prisma.domainIntervention.create({
                data: {
                    domainName: createDomainInterventionDto.domainName,
                    description: createDomainInterventionDto.description,
                    parentId: createDomainInterventionDto.parentId
                },
                include: {
                    parent: true,
                    children: true
                }
            });
            return domain;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to create domain intervention', error.stack);
            throw new Error('Failed to create domain intervention');
        }
    }
    async findAll() {
        return this.prisma.domainIntervention.findMany({
            where: {
                deleted: false
            },
            include: {
                parent: true,
                children: true
            },
            orderBy: {
                domainName: 'asc'
            }
        });
    }
    async findTree() {
        const domains = await this.prisma.domainIntervention.findMany({
            where: {
                deleted: false
            },
            orderBy: {
                domainName: 'asc'
            }
        });
        const buildTree = (parentId = null) => {
            return domains
                .filter(d => d.parentId === parentId)
                .map(d => ({
                ...d,
                children: buildTree(d.id)
            }));
        };
        return buildTree(null);
    }
    async findOne(id) {
        const domain = await this.prisma.domainIntervention.findFirst({
            where: {
                id,
                deleted: false
            },
            include: {
                parent: true,
                children: true
            }
        });
        if (!domain) {
            throw new common_1.NotFoundException('Domain intervention not found');
        }
        return domain;
    }
    async update(id, updateDomainInterventionDto, userId) {
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can update domain interventions');
            }
            const existing = await this.prisma.domainIntervention.findFirst({
                where: {
                    id,
                    deleted: false
                },
                include: {
                    children: true
                }
            });
            if (!existing) {
                throw new common_1.NotFoundException('Domain intervention not found');
            }
            if (updateDomainInterventionDto.parentId !== undefined) {
                if (updateDomainInterventionDto.parentId === id) {
                    throw new common_1.BadRequestException('Cannot set domain intervention as its own parent');
                }
                const isDescendant = async (childId, potentialParentId) => {
                    const child = await this.prisma.domainIntervention.findUnique({
                        where: { id: childId },
                        include: { children: true }
                    });
                    if (!child)
                        return false;
                    if (child.id === potentialParentId)
                        return true;
                    for (const descendant of child.children) {
                        if (await isDescendant(descendant.id, potentialParentId)) {
                            return true;
                        }
                    }
                    return false;
                };
                if (updateDomainInterventionDto.parentId &&
                    await isDescendant(updateDomainInterventionDto.parentId, id)) {
                    throw new common_1.BadRequestException('Cannot set a descendant as parent');
                }
                if (updateDomainInterventionDto.parentId) {
                    const parent = await this.prisma.domainIntervention.findFirst({
                        where: {
                            id: updateDomainInterventionDto.parentId,
                            deleted: false
                        }
                    });
                    if (!parent) {
                        throw new common_1.BadRequestException('Parent domain intervention not found');
                    }
                }
            }
            if (updateDomainInterventionDto.domainName &&
                updateDomainInterventionDto.domainName !== existing.domainName) {
                const nameConflict = await this.prisma.domainIntervention.findFirst({
                    where: {
                        domainName: updateDomainInterventionDto.domainName,
                        parentId: updateDomainInterventionDto.parentId ?? existing.parentId,
                        deleted: false,
                        id: { not: id }
                    }
                });
                if (nameConflict) {
                    throw new common_1.ConflictException('Another domain intervention with this name exists at this level');
                }
            }
            const updated = await this.prisma.domainIntervention.update({
                where: { id },
                data: {
                    domainName: updateDomainInterventionDto.domainName,
                    description: updateDomainInterventionDto.description,
                    parentId: updateDomainInterventionDto.parentId
                },
                include: {
                    parent: true,
                    children: true
                }
            });
            return updated;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException ||
                error instanceof common_1.ForbiddenException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to update domain intervention', error.stack);
            throw new Error('Failed to update domain intervention');
        }
    }
    async remove(id, userId) {
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can delete domain interventions');
            }
            const existing = await this.prisma.domainIntervention.findFirst({
                where: {
                    id,
                    deleted: false
                },
                include: {
                    children: {
                        where: {
                            deleted: false
                        }
                    },
                    activities: {
                        where: {
                            deleted: false
                        }
                    }
                }
            });
            if (!existing) {
                throw new common_1.NotFoundException('Domain intervention not found');
            }
            if (existing.children.length > 0) {
                throw new common_1.ConflictException('Cannot delete domain intervention that has child domains');
            }
            if (existing.activities.length > 0) {
                throw new common_1.ConflictException('Cannot delete domain intervention that is being used in activities');
            }
            await this.prisma.domainIntervention.update({
                where: { id },
                data: { deleted: true }
            });
            return { success: true };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException ||
                error instanceof common_1.ForbiddenException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to delete domain intervention', error.stack);
            throw new Error('Failed to delete domain intervention');
        }
    }
};
exports.DomainInterventionsService = DomainInterventionsService;
exports.DomainInterventionsService = DomainInterventionsService = DomainInterventionsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], DomainInterventionsService);
//# sourceMappingURL=domain-interventions.service.js.map