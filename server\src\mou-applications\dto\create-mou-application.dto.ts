import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsInt, IsNotEmpty, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateMouApplicationDto {
  @ApiProperty({
    description: 'Unique application key',
    example: 'MOU-APP-2024-001',
  })
  @IsString()
  @IsNotEmpty()
  applicationKey: string;

  @ApiProperty({
    description: 'MoU ID this application belongs to',
    example: 1,
  })
  @IsInt()
  mouId: number;

  @ApiProperty({
    description: 'Responsibility description',
    example: 'Primary healthcare service delivery',
    required: false,
  })
  @IsString()
  @IsOptional()
  responsibility?: string;

  @ApiProperty({
    description: 'User ID of the applicant',
    example: 1,
    required: false,
  })
  @IsInt()
  @IsOptional()
  userId?: number;
}

export class UpdateMouApplicationDto {
  @ApiProperty({
    description: 'Responsibility description',
    example: 'Updated healthcare service delivery',
    required: false,
  })
  @IsString()
  @IsOptional()
  responsibility?: string;

  @ApiProperty({
    description: 'User ID of the applicant',
    example: 1,
    required: false,
  })
  @IsInt()
  @IsOptional()
  userId?: number;
}

export class MouApplicationResponseDto {
  @ApiProperty({ description: 'Application ID', example: 1 })
  id: number;

  @ApiProperty({ description: 'Application key', example: 'MOU-APP-2024-001' })
  applicationKey: string;

  @ApiProperty({ description: 'MoU ID', example: 1 })
  mouId: number;

  @ApiProperty({ description: 'Responsibility', example: 'Healthcare delivery', required: false })
  responsibility?: string;

  @ApiProperty({ description: 'User ID', example: 1, required: false })
  userId?: number;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;

  @ApiProperty({ description: 'Soft delete flag' })
  deleted: boolean;
}

export class MouApplicationWithRelationsDto extends MouApplicationResponseDto {
  @ApiProperty({ description: 'Associated MoU information' })
  mou?: {
    id: number;
    mouKey: string;
    party: {
      id: number;
      name: string;
      signatory: string;
      position: string;
    };
  };

  @ApiProperty({ description: 'Associated user information' })
  user?: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    organization?: {
      id: number;
      organizationName: string;
    };
  };

  @ApiProperty({ description: 'Associated projects' })
  projects?: Array<{
    id: number;
    name: string;
    description?: string;
    duration: number;
  }>;

  @ApiProperty({ description: 'Approval steps' })
  approvalSteps?: Array<{
    id: number;
    status: string;
    comment?: string;
    role: string;
    reviewer: {
      id: number;
      firstName: string;
      lastName: string;
    };
  }>;
}
