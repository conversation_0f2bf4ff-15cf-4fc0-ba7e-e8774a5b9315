import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsInt, IsNotEmpty, IsPositive } from 'class-validator';

export class CreateProjectDto {
  @ApiProperty({
    description: 'Project name',
    example: 'Primary Healthcare Enhancement Project',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Project description',
    example: 'A comprehensive project to enhance primary healthcare services in rural areas',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Project duration in months',
    example: 12,
  })
  @IsInt()
  @IsPositive()
  duration: number;

  @ApiProperty({
    description: 'Budget type ID',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  budgetTypeId: number;

  @ApiProperty({
    description: 'Funding unit ID',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  fundingUnitId: number;

  @ApiProperty({
    description: 'Funding source ID',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  fundingSourceId: number;

  @ApiProperty({
    description: 'Organization ID',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  organizationId: number;

  @ApiProperty({
    description: 'Project document ID',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  projectDocumentId: number;

  @ApiProperty({
    description: 'MoU application ID',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  mouApplicationId: number;
}

export class UpdateProjectDto {
  @ApiProperty({
    description: 'Project name',
    example: 'Updated Primary Healthcare Enhancement Project',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Project description',
    example: 'Updated project description',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Project duration in months',
    example: 18,
    required: false,
  })
  @IsInt()
  @IsPositive()
  @IsOptional()
  duration?: number;

  @ApiProperty({
    description: 'Budget type ID',
    example: 2,
    required: false,
  })
  @IsInt()
  @IsPositive()
  @IsOptional()
  budgetTypeId?: number;

  @ApiProperty({
    description: 'Funding unit ID',
    example: 2,
    required: false,
  })
  @IsInt()
  @IsPositive()
  @IsOptional()
  fundingUnitId?: number;

  @ApiProperty({
    description: 'Funding source ID',
    example: 2,
    required: false,
  })
  @IsInt()
  @IsPositive()
  @IsOptional()
  fundingSourceId?: number;

  @ApiProperty({
    description: 'Project document ID',
    example: 2,
    required: false,
  })
  @IsInt()
  @IsPositive()
  @IsOptional()
  projectDocumentId?: number;
}

export class ProjectResponseDto {
  @ApiProperty({ description: 'Project ID', example: 1 })
  id: number;

  @ApiProperty({ description: 'Project name', example: 'Primary Healthcare Enhancement Project' })
  name: string;

  @ApiProperty({ description: 'Project description', example: 'A comprehensive project...', required: false })
  description?: string;

  @ApiProperty({ description: 'Project duration in months', example: 12 })
  duration: number;

  @ApiProperty({ description: 'Budget type ID', example: 1 })
  budgetTypeId: number;

  @ApiProperty({ description: 'Funding unit ID', example: 1 })
  fundingUnitId: number;

  @ApiProperty({ description: 'Funding source ID', example: 1 })
  fundingSourceId: number;

  @ApiProperty({ description: 'Organization ID', example: 1 })
  organizationId: number;

  @ApiProperty({ description: 'Project document ID', example: 1 })
  projectDocumentId: number;

  @ApiProperty({ description: 'MoU application ID', example: 1 })
  mouApplicationId: number;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;

  @ApiProperty({ description: 'Soft delete flag' })
  deleted: boolean;
}

export class ProjectWithRelationsDto extends ProjectResponseDto {
  @ApiProperty({ description: 'Budget type information' })
  budgetType?: {
    id: number;
    typeName: string;
  };

  @ApiProperty({ description: 'Funding unit information' })
  fundingUnit?: {
    id: number;
    unitName: string;
  };

  @ApiProperty({ description: 'Funding source information' })
  fundingSource?: {
    id: number;
    sourceName: string;
  };

  @ApiProperty({ description: 'Organization information' })
  organization?: {
    id: number;
    organizationName: string;
  };

  @ApiProperty({ description: 'Project document information' })
  projectDocument?: {
    id: number;
    name: string;
    description?: string;
  };

  @ApiProperty({ description: 'MoU application information' })
  mouApplication?: {
    id: number;
    applicationKey: string;
  };

  @ApiProperty({ description: 'Project activities' })
  activities?: Array<{
    id: number;
    name: string;
    description?: string;
    startDate: Date;
    endDate: Date;
    implementer: string;
    implementerUnit: string;
    fiscalYear: number;
  }>;

  @ApiProperty({ description: 'Approval steps' })
  approvalSteps?: Array<{
    id: number;
    status: string;
    comment?: string;
    role: string;
    reviewer: {
      id: number;
      firstName: string;
      lastName: string;
    };
  }>;
}
