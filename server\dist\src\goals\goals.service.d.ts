import { PrismaService } from '../prisma/prisma.service';
import { CreateGoalDto, UpdateGoalDto } from './dto/create-goal.dto';
export declare class GoalsService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createGoalDto: CreateGoalDto): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
    }>;
    findAll(): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
    }[]>;
    findOne(id: number): Promise<{
        parties: ({
            organization: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                organizationName: string;
                organizationPhoneNumber: string;
                organizationEmail: string;
                organizationWebsite: string | null;
                homeCountryRepresentative: string;
                rwandaRepresentative: string;
                organizationRgbNumber: string | null;
                organizationTypeId: number;
                centralLevelInstitutionId: number | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            duration: number;
            partyId: string;
            responsibilityId: number | null;
            objectiveId: number | null;
            goalId: number;
            signatory: string;
            position: string;
            reasonForExtendedDuration: string | null;
        })[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
    }>;
    update(id: number, updateGoalDto: UpdateGoalDto): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
}
