import { format } from 'date-fns'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { useMouStore } from '@/store/mou-store'
import {
  mockFundingSources,
  mockFundingUnits,
  mockBudgetTypes,
  mockCurrencies,
  mockDomains,
  mockInputCategories,
  mockProvinces,
  mockCentralLevels
} from '@/data/mock-data'
import { FileText, Pencil } from 'lucide-react'
import { useStepNavigation } from '@/hooks/use-mou-form'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'

const formatDocumentType = (type: string) => {
  return type.replace(/([A-Z])/g, ' $1').trim()
}

const getLookupValue = (id: string, items: Array<{ id: string, name: string }>) => {
  return items.find(item => item.id === id)?.name || id
}

export function ReviewStep() {
  const {
    organizationName,
    mouDuration,
    durationReason,
    parties,
    projects,
    activities,
    documents,
    isSubmitting,
    setCurrentStep
  } = useMouStore()

  const handleEditSection = (step: number) => {
    setCurrentStep(step)
  }

  return (
    <div className="space-y-8">
      {/* MoU Details Section */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">MoU Details</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditSection(1)}
          >
            <Pencil className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
        <dl className="grid grid-cols-2 gap-4">
          <div>
            <dt className="text-sm text-muted-foreground">Organization Name</dt>
            <dd className="text-sm font-medium">{organizationName}</dd>
          </div>
          <div>
            <dt className="text-sm text-muted-foreground">MoU Duration</dt>
            <dd className="text-sm font-medium">{mouDuration} {mouDuration === 1 ? 'Year' : 'Years'}</dd>
          </div>
          {mouDuration > 1 && durationReason && (
            <div className="col-span-2">
              <dt className="text-sm text-muted-foreground">Duration Reason</dt>
              <dd className="text-sm font-medium">{durationReason}</dd>
            </div>
          )}
        </dl>
      </Card>

      {/* Parties Section */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Parties</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditSection(2)}
          >
            <Pencil className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
        <div className="space-y-6">
          {parties.map((party, index) => (
            <div key={party.id} className="border-b last:border-0 pb-4 last:pb-0">
              <h3 className="font-medium mb-2">Party {index + 1}</h3>
              <dl className="grid grid-cols-2 gap-4">
                <div>
                  <dt className="text-sm text-muted-foreground">Name</dt>
                  <dd className="text-sm font-medium">{party.name}</dd>
                </div>
                <div>
                  <dt className="text-sm text-muted-foreground">Signatory</dt>
                  <dd className="text-sm font-medium">{party.signatoryName}</dd>
                </div>
                <div>
                  <dt className="text-sm text-muted-foreground">Position</dt>
                  <dd className="text-sm font-medium">{party.position}</dd>
                </div>
                <div>
                  <dt className="text-sm text-muted-foreground">Responsibilities</dt>
                  <dd className="text-sm font-medium">{party.responsibilities}</dd>
                </div>
              </dl>
            </div>
          ))}
        </div>
      </Card>

      {/* Projects Section */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Projects</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditSection(3)}
          >
            <Pencil className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
        <div className="space-y-6">
          {projects.map((project, index) => (
            <div key={project.id} className="border-b last:border-0 pb-4 last:pb-0">
              <h3 className="font-medium mb-2">{project.name}</h3>
              <dl className="grid grid-cols-2 gap-4">
                <div>
                  <dt className="text-sm text-muted-foreground">Funding Source</dt>
                  <dd className="text-sm font-medium">
                    {getLookupValue(project.fundingSource, mockFundingSources)}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm text-muted-foreground">Funding Unit</dt>
                  <dd className="text-sm font-medium">
                    {getLookupValue(project.fundingUnit, mockFundingUnits)}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm text-muted-foreground">Budget Type</dt>
                  <dd className="text-sm font-medium">
                    {getLookupValue(project.budgetType, mockBudgetTypes)}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm text-muted-foreground">Currency</dt>
                  <dd className="text-sm font-medium">
                    {getLookupValue(project.currency, mockCurrencies)}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm text-muted-foreground">Duration</dt>
                  <dd className="text-sm font-medium">
                    {format(new Date(project.startDate), 'PPP')} - {format(new Date(project.endDate), 'PPP')}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm text-muted-foreground">Total Budget</dt>
                  <dd className="text-sm font-medium">
                    {mockCurrencies.find(c => c.id === project.currency)?.symbol}
                    {project.totalBudget.toLocaleString()}
                  </dd>
                </div>
                <div className="col-span-2">
                  <dt className="text-sm text-muted-foreground mb-2">Goals</dt>
                  <dd className="space-y-2">
                    {project.goals.map((goal, goalIndex) => (
                      <div key={goal.id} className="text-sm">
                        <span className="font-medium">
                          {goal.isOverallGoal ? 'Overall Goal: ' : `Goal ${goalIndex + 1}: `}
                        </span>
                        {goal.description}
                      </div>
                    ))}
                  </dd>
                </div>
              </dl>
            </div>
          ))}
        </div>
      </Card>

      {/* Activities Section */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Activities</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditSection(4)}
          >
            <Pencil className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
        <div className="space-y-6">
          {projects.map((project) => (
            <div key={project.id} className="border-b last:border-0 pb-4 last:pb-0">
              <h3 className="font-medium mb-4">{project.name}</h3>
              <div className="space-y-4 pl-4">
                {activities
                  .filter(activity => activity.projectId === project.id)
                  .map((activity) => (
                    <div key={activity.id} className="border-l-2 pl-4">
                      <h4 className="font-medium mb-2">{activity.name}</h4>
                      <dl className="grid grid-cols-2 gap-4">
                        <div>
                          <dt className="text-sm text-muted-foreground">Implementor</dt>
                          <dd className="text-sm font-medium">{activity.implementor}</dd>
                        </div>
                        <div>
                          <dt className="text-sm text-muted-foreground">Implementing Unit</dt>
                          <dd className="text-sm font-medium">{activity.implementingUnit}</dd>
                        </div>
                        <div>
                          <dt className="text-sm text-muted-foreground">Duration</dt>
                          <dd className="text-sm font-medium">
                            {format(new Date(activity.startDate), 'PPP')} - {format(new Date(activity.endDate), 'PPP')}
                          </dd>
                        </div>
                        <div>
                          <dt className="text-sm text-muted-foreground">Domain</dt>
                          <dd className="text-sm font-medium">
                            {getLookupValue(activity.domain, mockDomains)}
                          </dd>
                        </div>
                        <div className="col-span-2">
                          <dt className="text-sm text-muted-foreground">Budget Allocations</dt>
                          <dd className="mt-1">
                            <div className="text-sm space-y-1">
                              {activity.budgetAllocations.map((allocation) => (
                                <div key={allocation.id} className="flex justify-between">
                                  <span>{allocation.location}</span>
                                  <span className="font-medium">
                                    {mockCurrencies.find(c => c.id === project.currency)?.symbol}
                                    {allocation.budget.toLocaleString()}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </dd>
                        </div>
                      </dl>
                    </div>
                  ))}
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Documents Section */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Documents</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditSection(5)}
          >
            <Pencil className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
        <div className="grid grid-cols-2 gap-4">
          {documents.map((doc) => (
            <div key={doc.id} className="flex items-center space-x-4">
              <FileText className="h-8 w-8 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">{formatDocumentType(doc.type)}</p>
                {doc.file && (
                  <p className="text-sm text-muted-foreground">
                    {doc.file.name} ({(doc.file.size / 1024 / 1024).toFixed(2)} MB)
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Submit Dialog */}
      <Dialog>
        <DialogTrigger asChild>
          <Button className="w-full" disabled={isSubmitting}>
            {isSubmitting ? 'Submitting...' : 'Submit MoU Application'}
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Submit MoU Application</DialogTitle>
            <DialogDescription>
              Are you sure you want to submit this MoU Application? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" disabled={isSubmitting}>
              Cancel
            </Button>
            <Button disabled={isSubmitting} onClick={() => {
              useMouStore.getState().setSubmitting(true)
            }}>
              {isSubmitting ? 'Submitting...' : 'Confirm Submit'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
