"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var BudgetTypesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BudgetTypesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let BudgetTypesService = BudgetTypesService_1 = class BudgetTypesService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(BudgetTypesService_1.name);
    }
    async findAll() {
        try {
            const budgetTypes = await this.prisma.budgetType.findMany({
                where: { deleted: false },
                orderBy: { createdAt: 'desc' }
            });
            return budgetTypes;
        }
        catch (error) {
            this.logger.error('Failed to fetch budget types', error.stack);
            throw new Error('Failed to fetch budget types');
        }
    }
    async findOne(id) {
        try {
            const budgetType = await this.prisma.budgetType.findUnique({
                where: { id, deleted: false }
            });
            if (!budgetType) {
                throw new common_1.NotFoundException('Budget type not found');
            }
            return budgetType;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Failed to fetch budget type with ID ${id}`, error.stack);
            throw new Error('Failed to fetch budget type');
        }
    }
    async create(createBudgetTypeDto, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can create budget types');
            }
            const existingType = await this.prisma.budgetType.findFirst({
                where: {
                    typeName: createBudgetTypeDto.typeName,
                    deleted: false
                }
            });
            if (existingType) {
                throw new common_1.ConflictException('Budget type with this name already exists');
            }
            const budgetType = await this.prisma.budgetType.create({
                data: {
                    typeName: createBudgetTypeDto.typeName
                }
            });
            return budgetType;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to create budget type', error.stack);
            throw new Error('Failed to create budget type');
        }
    }
    async update(id, updateBudgetTypeDto, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can update budget types');
            }
            const existingType = await this.prisma.budgetType.findUnique({
                where: { id, deleted: false }
            });
            if (!existingType) {
                throw new common_1.NotFoundException('Budget type not found');
            }
            if (updateBudgetTypeDto.typeName && updateBudgetTypeDto.typeName !== existingType.typeName) {
                const nameConflict = await this.prisma.budgetType.findFirst({
                    where: {
                        typeName: updateBudgetTypeDto.typeName,
                        deleted: false,
                        id: { not: id }
                    }
                });
                if (nameConflict) {
                    throw new common_1.ConflictException('Budget type with this name already exists');
                }
            }
            const budgetType = await this.prisma.budgetType.update({
                where: { id },
                data: updateBudgetTypeDto
            });
            return budgetType;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to update budget type with ID ${id}`, error.stack);
            throw new Error('Failed to update budget type');
        }
    }
    async remove(id, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can delete budget types');
            }
            const existingType = await this.prisma.budgetType.findUnique({
                where: { id, deleted: false }
            });
            if (!existingType) {
                throw new common_1.NotFoundException('Budget type not found');
            }
            const projectsUsingType = await this.prisma.project.findFirst({
                where: {
                    budgetTypeId: id,
                    deleted: false
                }
            });
            if (projectsUsingType) {
                throw new common_1.ConflictException('Cannot delete budget type that is being used by projects');
            }
            await this.prisma.budgetType.update({
                where: { id },
                data: { deleted: true }
            });
            return { message: 'Budget type deleted successfully' };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to delete budget type with ID ${id}`, error.stack);
            throw new Error('Failed to delete budget type');
        }
    }
};
exports.BudgetTypesService = BudgetTypesService;
exports.BudgetTypesService = BudgetTypesService = BudgetTypesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], BudgetTypesService);
//# sourceMappingURL=budget-types.service.js.map