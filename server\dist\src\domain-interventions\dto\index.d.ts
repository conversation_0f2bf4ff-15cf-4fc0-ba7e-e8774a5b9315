export declare class CreateDomainInterventionDto {
    domainName: string;
    description?: string;
    parentId?: number;
}
export declare class UpdateDomainInterventionDto {
    domainName?: string;
    description?: string;
    parentId?: number;
}
export declare class DomainInterventionResponseDto {
    id: number;
    domainName: string;
    description?: string;
    parentId?: number;
    parent?: DomainInterventionResponseDto;
    children?: DomainInterventionResponseDto[];
    createdAt: Date;
    updatedAt: Date;
    deleted: boolean;
}
export declare class DomainInterventionTreeResponseDto extends DomainInterventionResponseDto {
    children: DomainInterventionTreeResponseDto[];
}
