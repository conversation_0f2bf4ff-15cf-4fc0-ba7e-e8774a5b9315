"use client"

import { useEffect, useState } from "react"
import { useAuth } from "@/contexts/auth-context"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  FileText, 
  Plus, 
  Clock, 
  CheckCircle, 
  XCircle, 
  FileEdit,
  ArrowRight,
  Building,
  Mail,
  Phone
} from "lucide-react"
import Link from "next/link"
import { mouApplicationService } from "@/lib/services/mou-application.service"
import { Loader2 } from "lucide-react"

interface ApplicationStats {
  total: number
  pending: number
  approved: number
  rejected: number
  draft: number
}

export default function PartnerDashboardPage() {
  const { user } = useAuth()
  const [stats, setStats] = useState<ApplicationStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")

  useEffect(() => {
    const loadStats = async () => {
      try {
        setLoading(true)
        const statsData = await mouApplicationService.getApplicationStats()
        setStats(statsData)
      } catch (err: any) {
        console.error("Failed to load application stats:", err)
        setError("Failed to load dashboard data")
      } finally {
        setLoading(false)
      }
    }

    loadStats()
  }, [])

  if (!user?.emailVerified) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold tracking-tight text-cyan-900">Welcome, {user?.firstName}!</h2>
          <p className="text-muted-foreground">Partner Dashboard - Ministry of Health MoU Management System</p>
        </div>

        <Alert className="bg-amber-50 border-amber-200">
          <Mail className="h-4 w-4" />
          <AlertDescription className="text-amber-800">
            <strong>Email Verification Required:</strong> Please check your email and click the verification link to activate your account and access all features.
          </AlertDescription>
        </Alert>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5 text-cyan-600" />
              Account Setup
            </CardTitle>
            <CardDescription>
              Complete your account verification to start managing MoU applications
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground">
              <p>Once your email is verified, you will be able to:</p>
              <ul className="list-disc list-inside mt-2 space-y-1 ml-4">
                <li>Create and submit MoU applications</li>
                <li>Track application status and progress</li>
                <li>Manage your organization's projects</li>
                <li>Communicate with Ministry reviewers</li>
              </ul>
            </div>
            <Button asChild className="w-full">
              <Link href="/auth/verify-email">
                Check Verification Status
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight text-cyan-900">Welcome back, {user?.firstName}!</h2>
        <p className="text-muted-foreground">Partner Dashboard - Ministry of Health MoU Management System</p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-cyan-600" />
        </div>
      ) : (
        <>
          {/* Statistics Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-cyan-600">{stats?.total || 0}</div>
                <p className="text-xs text-muted-foreground">All time</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Draft</CardTitle>
                <FileEdit className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-600">{stats?.draft || 0}</div>
                <p className="text-xs text-muted-foreground">In progress</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-amber-600">{stats?.pending || 0}</div>
                <p className="text-xs text-muted-foreground">Under review</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Approved</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats?.approved || 0}</div>
                <p className="text-xs text-muted-foreground">Successful</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Rejected</CardTitle>
                <XCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{stats?.rejected || 0}</div>
                <p className="text-xs text-muted-foreground">Need revision</p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="h-5 w-5 text-cyan-600" />
                  Create New Application
                </CardTitle>
                <CardDescription>
                  Start a new MoU application for your organization
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild className="w-full bg-cyan-600 hover:bg-cyan-700">
                  <Link href="/dashboard/partner/applications/new">
                    <Plus className="mr-2 h-4 w-4" />
                    New MoU Application
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-cyan-600" />
                  Manage Applications
                </CardTitle>
                <CardDescription>
                  View and manage your existing MoU applications
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild variant="outline" className="w-full">
                  <Link href="/dashboard/partner/applications">
                    View All Applications
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest updates on your MoU applications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No recent activity</p>
                <p className="text-sm">Your application updates will appear here</p>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
