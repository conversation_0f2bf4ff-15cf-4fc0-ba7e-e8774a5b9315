"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartiesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let PartiesService = class PartiesService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createPartyDto) {
        try {
            if (createPartyDto.duration > 1 && !createPartyDto.reasonForExtendedDuration) {
                throw new common_1.BadRequestException('Reason for extended duration is required when duration is more than 1 year');
            }
            await this.validateRelationships(createPartyDto);
            const existingParty = await this.prisma.party.findUnique({
                where: { partyId: createPartyDto.partyId },
            });
            if (existingParty) {
                throw new common_1.ConflictException(`Party with ID ${createPartyDto.partyId} already exists`);
            }
            const party = await this.prisma.party.create({
                data: createPartyDto,
                include: {
                    organization: {
                        include: {
                            organizationType: true,
                        },
                    },
                    responsibility: true,
                    objective: true,
                    goal: true,
                    mou: {
                        include: {
                            mouApplications: true,
                        },
                    },
                },
            });
            return party;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException('Party ID must be unique');
                }
                if (error.code === 'P2003') {
                    throw new common_1.BadRequestException('Invalid foreign key reference');
                }
            }
            throw error;
        }
    }
    async findAll(page = 1, limit = 10, organizationId) {
        const skip = (page - 1) * limit;
        const where = {
            deleted: false,
            ...(organizationId && { organizationId }),
        };
        const [parties, total] = await Promise.all([
            this.prisma.party.findMany({
                where,
                skip,
                take: limit,
                include: {
                    organization: {
                        include: {
                            organizationType: true,
                        },
                    },
                    responsibility: true,
                    objective: true,
                    goal: true,
                    mou: {
                        include: {
                            mouApplications: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: 'desc',
                },
            }),
            this.prisma.party.count({ where }),
        ]);
        return {
            data: parties,
            meta: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id) {
        const party = await this.prisma.party.findFirst({
            where: {
                id,
                deleted: false,
            },
            include: {
                organization: {
                    include: {
                        organizationType: true,
                        addresses: true,
                    },
                },
                responsibility: true,
                objective: true,
                goal: true,
                mou: {
                    include: {
                        mouApplications: {
                            include: {
                                projects: true,
                                approvalSteps: {
                                    include: {
                                        reviewer: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        if (!party) {
            throw new common_1.NotFoundException(`Party with ID ${id} not found`);
        }
        return party;
    }
    async findByPartyId(partyId) {
        const party = await this.prisma.party.findFirst({
            where: {
                partyId,
                deleted: false,
            },
            include: {
                organization: {
                    include: {
                        organizationType: true,
                    },
                },
                responsibility: true,
                objective: true,
                goal: true,
                mou: {
                    include: {
                        mouApplications: true,
                    },
                },
            },
        });
        if (!party) {
            throw new common_1.NotFoundException(`Party with ID ${partyId} not found`);
        }
        return party;
    }
    async update(id, updatePartyDto) {
        const existingParty = await this.prisma.party.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingParty) {
            throw new common_1.NotFoundException(`Party with ID ${id} not found`);
        }
        const newDuration = updatePartyDto.duration ?? existingParty.duration;
        const newReason = updatePartyDto.reasonForExtendedDuration ?? existingParty.reasonForExtendedDuration;
        if (newDuration > 1 && !newReason) {
            throw new common_1.BadRequestException('Reason for extended duration is required when duration is more than 1 year');
        }
        if (Object.keys(updatePartyDto).some(key => ['organizationId', 'responsibilityId', 'objectiveId', 'goalId'].includes(key))) {
            await this.validateRelationships({
                ...existingParty,
                ...updatePartyDto,
            });
        }
        try {
            const updatedParty = await this.prisma.party.update({
                where: { id },
                data: updatePartyDto,
                include: {
                    organization: {
                        include: {
                            organizationType: true,
                        },
                    },
                    responsibility: true,
                    objective: true,
                    goal: true,
                    mou: {
                        include: {
                            mouApplications: true,
                        },
                    },
                },
            });
            return updatedParty;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2003') {
                    throw new common_1.BadRequestException('Invalid foreign key reference');
                }
            }
            throw error;
        }
    }
    async remove(id) {
        const existingParty = await this.prisma.party.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingParty) {
            throw new common_1.NotFoundException(`Party with ID ${id} not found`);
        }
        const mouWithParty = await this.prisma.mou.findUnique({
            where: { partyId: id },
        });
        if (mouWithParty) {
            throw new common_1.ConflictException('Cannot delete party that has an associated MoU');
        }
        await this.prisma.party.update({
            where: { id },
            data: { deleted: true },
        });
        return { message: 'Party deleted successfully' };
    }
    async getPartiesByOrganization(organizationId) {
        const parties = await this.prisma.party.findMany({
            where: {
                organizationId,
                deleted: false,
            },
            include: {
                responsibility: true,
                objective: true,
                goal: true,
                mou: {
                    include: {
                        mouApplications: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
        return parties;
    }
    async validateRelationships(dto) {
        const [organization, responsibility, objective, goal] = await Promise.all([
            this.prisma.organization.findUnique({ where: { id: dto.organizationId } }),
            this.prisma.responsibility.findUnique({ where: { id: dto.responsibilityId } }),
            this.prisma.objective.findUnique({ where: { id: dto.objectiveId } }),
            this.prisma.goal.findUnique({ where: { id: dto.goalId } }),
        ]);
        if (!organization) {
            throw new common_1.NotFoundException(`Organization with ID ${dto.organizationId} not found`);
        }
        if (!responsibility) {
            throw new common_1.NotFoundException(`Responsibility with ID ${dto.responsibilityId} not found`);
        }
        if (!objective) {
            throw new common_1.NotFoundException(`Objective with ID ${dto.objectiveId} not found`);
        }
        if (!goal) {
            throw new common_1.NotFoundException(`Goal with ID ${dto.goalId} not found`);
        }
    }
};
exports.PartiesService = PartiesService;
exports.PartiesService = PartiesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], PartiesService);
//# sourceMappingURL=parties.service.js.map