import { UserRole } from '@prisma/client';
export declare class CreateUserDto {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    role: UserRole;
    department?: string;
    organizationId?: number;
    isActive?: boolean;
}
export declare class UpdateUserDto {
    firstName?: string;
    lastName?: string;
    email?: string;
    role?: UserRole;
    department?: string;
    organizationId?: number;
    isActive?: boolean;
}
export declare class AssignRolesDto {
    role?: UserRole;
}
export declare class FilterUsersDto {
    roles?: UserRole[];
    search?: string;
    department?: string;
    organizationId?: number;
    isActive?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export declare class UserActivityDto {
    userId: number;
    startDate?: string;
    endDate?: string;
    activityType?: string;
    page?: number;
    limit?: number;
}
export declare class BulkUserActionDto {
    userIds: number[];
    isActive?: boolean;
    role?: UserRole;
}
