import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class CreateFundingUnitDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    unitName: string;
}

export class UpdateFundingUnitDto {
    @ApiProperty({ required: false })
    @IsOptional()
    @IsString()
    unitName?: string;
}

export class FundingUnitResponseDto {
    @ApiProperty()
    id: number;

    @ApiProperty()
    unitName: string;

    @ApiProperty()
    createAt: Date;

    @ApiProperty()
    updatedAt: Date;

    @ApiProperty()
    deleted: boolean;
}
