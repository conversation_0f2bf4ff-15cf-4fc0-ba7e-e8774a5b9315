import api from "../api"

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  firstName: string
  lastName: string
  email: string
  password: string
  organization: {
    organizationName: string
    organizationRegistrationNumber: string
    organizationPhoneNumber: string
    organizationEmail: string
    organizationWebsite?: string
    homeCountryRepresentative: string
    rwandaRepresentative: string
    organizationRgbNumber: string
    organizationTypeId: number
    addresses: Array<{
      addressType: "HEADQUARTERS" | "RWANDA"
      country: string
      province?: string
      district?: string
      sector?: string
      cell?: string
      village?: string
      street: string
      avenue?: string
      poBox: string
      postalCode?: string
    }>
  }
}

export interface CreateUserByAdminRequest {
  firstName: string
  lastName: string
  email: string
  role: string
  organizationId?: string
}

export interface AuthResponse {
  user: {
    id: string
    firstName: string
    lastName: string
    email: string
    role: string
    emailVerified: boolean
    organizationId?: string
  }
  accessToken: string
  refreshToken: string
}

export const authService = {
  async login(data: LoginRequest): Promise<AuthResponse> {
    const response = await api.post("/auth/login", data)
    return response.data
  },

  async register(data: RegisterRequest): Promise<{ user: any; message: string }> {
    const response = await api.post("/auth/register", data)
    return response.data
  },

  async createUserByAdmin(data: CreateUserByAdminRequest): Promise<{ user: any; tempPassword: string }> {
    const response = await api.post("/auth/create-user", data)
    return response.data
  },

  async verifyEmail(token: string): Promise<{ message: string }> {
    const response = await api.post("/auth/verify-email", { token })
    return response.data
  },

  async forgotPassword(email: string): Promise<{ message: string }> {
    const response = await api.post("/auth/forgot-password", { email })
    return response.data
  },

  async resetPassword(token: string, password: string): Promise<{ message: string }> {
    const response = await api.post("/auth/reset-password", { token, password })
    return response.data
  },

  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    const response = await api.post("/auth/refresh-token", { refreshToken })
    return response.data
  },

  async logout(): Promise<void> {
    await api.post("/auth/logout")
  },

  async getCurrentUser(): Promise<any> {
    const response = await api.get("/auth/me")
    return response.data
  },

  async resendVerification(): Promise<{ message: string }> {
    const response = await api.post("/auth/resend-verification")
    return response.data
  },
}
