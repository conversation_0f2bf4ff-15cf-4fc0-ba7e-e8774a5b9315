"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityWithRelationsDto = exports.ActivityResponseDto = exports.UpdateActivityDto = exports.CreateActivityDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateActivityDto {
}
exports.CreateActivityDto = CreateActivityDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Activity name',
        example: 'Community Health Worker Training',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateActivityDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Activity description',
        example: 'Training program for community health workers in rural areas',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateActivityDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project ID this activity belongs to',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateActivityDto.prototype, "projectId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Activity start date',
        example: '2024-01-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateActivityDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Activity end date',
        example: '2024-12-31T23:59:59.999Z',
    }),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateActivityDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the implementer',
        example: 'Rwanda Health Partners Initiative',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateActivityDto.prototype, "implementer", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Implementing unit',
        example: 'Community Health Department',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateActivityDto.prototype, "implementerUnit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Fiscal year',
        example: 2024,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateActivityDto.prototype, "fiscalYear", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Domain intervention ID',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateActivityDto.prototype, "domainInterventionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Input ID',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateActivityDto.prototype, "inputId", void 0);
class UpdateActivityDto {
}
exports.UpdateActivityDto = UpdateActivityDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Activity name',
        example: 'Updated Community Health Worker Training',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateActivityDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Activity description',
        example: 'Updated training program description',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateActivityDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Activity start date',
        example: '2024-02-01T00:00:00.000Z',
        required: false,
    }),
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateActivityDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Activity end date',
        example: '2024-11-30T23:59:59.999Z',
        required: false,
    }),
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateActivityDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the implementer',
        example: 'Updated implementer name',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateActivityDto.prototype, "implementer", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Implementing unit',
        example: 'Updated implementing unit',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateActivityDto.prototype, "implementerUnit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Fiscal year',
        example: 2025,
        required: false,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateActivityDto.prototype, "fiscalYear", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Domain intervention ID',
        example: 2,
        required: false,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateActivityDto.prototype, "domainInterventionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Input ID',
        example: 2,
        required: false,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateActivityDto.prototype, "inputId", void 0);
class ActivityResponseDto {
}
exports.ActivityResponseDto = ActivityResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Activity ID', example: 1 }),
    __metadata("design:type", Number)
], ActivityResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Activity name', example: 'Community Health Worker Training' }),
    __metadata("design:type", String)
], ActivityResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Activity description', example: 'Training program...', required: false }),
    __metadata("design:type", String)
], ActivityResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project ID', example: 1 }),
    __metadata("design:type", Number)
], ActivityResponseDto.prototype, "projectId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Start date' }),
    __metadata("design:type", Date)
], ActivityResponseDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'End date' }),
    __metadata("design:type", Date)
], ActivityResponseDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Implementer', example: 'Rwanda Health Partners Initiative' }),
    __metadata("design:type", String)
], ActivityResponseDto.prototype, "implementer", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Implementer unit', example: 'Community Health Department' }),
    __metadata("design:type", String)
], ActivityResponseDto.prototype, "implementerUnit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Fiscal year', example: 2024 }),
    __metadata("design:type", Number)
], ActivityResponseDto.prototype, "fiscalYear", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Domain intervention ID', example: 1 }),
    __metadata("design:type", Number)
], ActivityResponseDto.prototype, "domainInterventionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Input ID', example: 1 }),
    __metadata("design:type", Number)
], ActivityResponseDto.prototype, "inputId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation date' }),
    __metadata("design:type", Date)
], ActivityResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update date' }),
    __metadata("design:type", Date)
], ActivityResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Soft delete flag' }),
    __metadata("design:type", Boolean)
], ActivityResponseDto.prototype, "deleted", void 0);
class ActivityWithRelationsDto extends ActivityResponseDto {
}
exports.ActivityWithRelationsDto = ActivityWithRelationsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project information' }),
    __metadata("design:type", Object)
], ActivityWithRelationsDto.prototype, "project", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Domain intervention information' }),
    __metadata("design:type", Object)
], ActivityWithRelationsDto.prototype, "domainIntervention", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Input information' }),
    __metadata("design:type", Object)
], ActivityWithRelationsDto.prototype, "input", void 0);
//# sourceMappingURL=create-activity.dto.js.map