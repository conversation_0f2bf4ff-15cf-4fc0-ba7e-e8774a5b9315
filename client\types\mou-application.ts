// Comprehensive type definitions for MoU Application

export interface MouApplicationData {
  id: string;
  organizationId: number;
  organizationName: string;
  mouDuration: number;
  extendedDurationReason?: string;
  parties: Party[];
  projects: Project[];
  activities: Activity[];
  documents: Document[];
  currentStep: number;
  status: ApplicationStatus;
  lastSaved?: Date;
  isSubmitting?: boolean;
}

export interface Party {
  id: string;
  name: string;
  signatoryName: string;
  position: string;
  responsibilities: string;
}

export interface Project {
  id: string;
  name: string;
  fundingSourceId: string;
  fundingUnitId: string;
  budgetTypeId: string;
  currencyId: string;
  startDate: string;
  endDate: string;
  fiscalYearBudgets: FiscalYearBudget[];
  goals: ProjectGoal[];
  totalBudget: number;
}

export interface FiscalYearBudget {
  id: string;
  fiscalYear: string;
  budget: number;
}

export interface ProjectGoal {
  id: string;
  description: string;
  isOverallGoal: boolean;
}

export interface Activity {
  id: string;
  projectId: string;
  name: string;
  implementor: string;
  implementingUnit: string;
  fiscalYear: string;
  startDate: string;
  endDate: string;
  domain: string;
  subDomain: string;
  subDomainFunction: string;
  subFunction: string;
  inputCategory: string;
  activityInput: string;
  geographicLevel: 'Provinces' | 'Central';
  budgetAllocations: BudgetAllocation[];
}

export interface BudgetAllocation {
  id: string;
  location: string; // district ID or central level ID
  budget: number;
}

export interface Document {
  id: string;
  type: DocumentType;
  file: File | null;
  uploaded: boolean;
  uploadProgress?: number;
}

export type DocumentType =
  | "MEMO_OBJECTIVE"
  | "STRATEGIC_PLAN"
  | "CAPACITY_BUILDING"
  | "MEMO_FUNDS";

export type ApplicationStatus =
  | "DRAFT"
  | "SUBMITTED"
  | "UNDER_REVIEW"
  | "APPROVED"
  | "REJECTED"
  | "REQUIRES_MODIFICATION";

// Form validation interfaces
export interface FormErrors {
  [key: string]: string | FormErrors | FormErrors[];
}

export interface StepValidation {
  isValid: boolean;
  errors: FormErrors;
}

// Domain intervention cascading types
export interface Domain {
  id: string;
  name: string;
  subDomains: SubDomain[];
}

export interface SubDomain {
  id: string;
  name: string;
  functions: DomainFunction[];
}

export interface DomainFunction {
  id: string;
  name: string;
  subFunctions: SubFunction[];
}

export interface SubFunction {
  id: string;
  name: string;
}

// Input category types
export interface InputCategory {
  id: string;
  name: string;
  activityInputs: ActivityInput[];
}

export interface ActivityInput {
  id: string;
  name: string;
}

// Geographic types
export interface Province {
  id: string;
  name: string;
  districts: District[];
}

export interface District {
  id: string;
  name: string;
}

export interface CentralLevel {
  id: string;
  name: string;
}

// Lookup types
export interface FundingSource {
  id: string;
  name: string;
}

export interface FundingUnit {
  id: string;
  name: string;
}

export interface BudgetType {
  id: string;
  name: string;
}

export interface Currency {
  id: string;
  code: string;
  name: string;
  symbol: string;
}

export interface FiscalYear {
  id: string;
  name: string;
}

// Auto-save types
export interface AutoSaveConfig {
  interval: number; // milliseconds
  enabled: boolean;
  lastSaved?: Date;
}

// File upload types
export interface FileUploadConfig {
  maxSize: number;
  acceptedTypes: string[];
  acceptedExtensions: string[];
}

export interface DocumentTypeConfig {
  id: DocumentType;
  name: string;
  description: string;
  required: boolean;
  order: number;
}

// Step navigation types
export interface StepConfig {
  id: number;
  title: string;
  description?: string;
  isCompleted: boolean;
  isAccessible: boolean;
}

// Form submission types
export interface SubmissionResult {
  success: boolean;
  message: string;
  applicationId?: string;
  errors?: FormErrors;
}

// Progress tracking
export interface ProgressInfo {
  currentStep: number;
  totalSteps: number;
  completedSteps: number;
  percentage: number;
}

// Demo/Mock data organization
export interface DemoOrganization {
  id: number;
  name: string;
  type: string;
  registrationNumber: string;
  contactPerson: string;
  email: string;
  phone: string;
}