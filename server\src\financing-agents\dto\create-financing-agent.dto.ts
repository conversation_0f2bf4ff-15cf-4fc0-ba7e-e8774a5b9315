import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class CreateFinancingAgentDto {
  @ApiProperty({
    description: 'Name of the financing agent',
    example: 'World Bank',
  })
  @IsString()
  @IsNotEmpty()
  agentName: string;

  @ApiProperty({
    description: 'Description of the financing agent',
    example: 'International financial institution providing loans and grants',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Contact information for the financing agent',
    example: '<EMAIL>, +1-202-473-1000',
    required: false,
  })
  @IsString()
  @IsOptional()
  contactInfo?: string;
}
