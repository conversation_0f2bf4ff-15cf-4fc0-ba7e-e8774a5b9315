"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
const fs = require("fs");
const path = require("path");
let DocumentsService = class DocumentsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createDocumentDto) {
        try {
            await this.validateRelationships(createDocumentDto);
            const document = await this.prisma.document.create({
                data: createDocumentDto,
                include: {
                    organization: true,
                    documentType: true,
                    project: {
                        include: {
                            mouApplication: true,
                        },
                    },
                },
            });
            return document;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2003') {
                    throw new common_1.BadRequestException('Invalid foreign key reference');
                }
            }
            throw error;
        }
    }
    async findAll(page = 1, limit = 10, organizationId, documentTypeId) {
        const skip = (page - 1) * limit;
        const where = {
            deleted: false,
            ...(organizationId && { organizationId }),
            ...(documentTypeId && { documentTypeId }),
        };
        const [documents, total] = await Promise.all([
            this.prisma.document.findMany({
                where,
                skip,
                take: limit,
                include: {
                    organization: true,
                    documentType: true,
                    project: {
                        include: {
                            mouApplication: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: 'desc',
                },
            }),
            this.prisma.document.count({ where }),
        ]);
        return {
            data: documents,
            meta: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id) {
        const document = await this.prisma.document.findFirst({
            where: {
                id,
                deleted: false,
            },
            include: {
                organization: {
                    include: {
                        organizationType: true,
                        addresses: true,
                    },
                },
                documentType: true,
                project: {
                    include: {
                        mouApplication: {
                            include: {
                                mou: {
                                    include: {
                                        party: true,
                                    },
                                },
                            },
                        },
                        activities: true,
                    },
                },
            },
        });
        if (!document) {
            throw new common_1.NotFoundException(`Document with ID ${id} not found`);
        }
        return document;
    }
    async update(id, updateDocumentDto) {
        const existingDocument = await this.prisma.document.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingDocument) {
            throw new common_1.NotFoundException(`Document with ID ${id} not found`);
        }
        if (updateDocumentDto.documentTypeId) {
            const documentType = await this.prisma.documentType.findUnique({
                where: { id: updateDocumentDto.documentTypeId },
            });
            if (!documentType) {
                throw new common_1.NotFoundException(`Document type with ID ${updateDocumentDto.documentTypeId} not found`);
            }
        }
        try {
            const updatedDocument = await this.prisma.document.update({
                where: { id },
                data: updateDocumentDto,
                include: {
                    organization: true,
                    documentType: true,
                    project: {
                        include: {
                            mouApplication: true,
                        },
                    },
                },
            });
            return updatedDocument;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2003') {
                    throw new common_1.BadRequestException('Invalid foreign key reference');
                }
            }
            throw error;
        }
    }
    async remove(id) {
        const existingDocument = await this.prisma.document.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingDocument) {
            throw new common_1.NotFoundException(`Document with ID ${id} not found`);
        }
        const projectUsingDocument = await this.prisma.project.findFirst({
            where: {
                documents: {
                    some: { id: id }
                }
            },
        });
        if (projectUsingDocument) {
            throw new common_1.ConflictException('Cannot delete document that is being used by a project');
        }
        await this.prisma.document.update({
            where: { id },
            data: { deleted: true },
        });
        return { message: 'Document deleted successfully' };
    }
    async getDocumentsByOrganization(organizationId) {
        const documents = await this.prisma.document.findMany({
            where: {
                organizationId,
                deleted: false,
            },
            include: {
                documentType: true,
                project: {
                    include: {
                        mouApplication: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
        return documents;
    }
    async getDocumentsByType(documentTypeId) {
        const documents = await this.prisma.document.findMany({
            where: {
                documentTypeId,
                deleted: false,
            },
            include: {
                organization: true,
                project: {
                    include: {
                        mouApplication: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
        return documents;
    }
    async uploadFile(file, createDocumentDto) {
        try {
            if (!file) {
                throw new common_1.BadRequestException('No file provided');
            }
            const allowedMimeTypes = [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'image/jpeg',
                'image/png',
                'image/gif'
            ];
            if (!allowedMimeTypes.includes(file.mimetype)) {
                throw new common_1.BadRequestException('Invalid file type. Only PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, and GIF files are allowed');
            }
            const maxSize = 10 * 1024 * 1024;
            if (file.size > maxSize) {
                throw new common_1.BadRequestException('File size exceeds 10MB limit');
            }
            const document = await this.create(createDocumentDto);
            const fileExtension = path.extname(file.originalname);
            const fileName = `doc_${document.id}_${Date.now()}${fileExtension}`;
            const uploadPath = path.join(process.cwd(), 'uploads', 'documents');
            const filePath = path.join(uploadPath, fileName);
            if (!fs.existsSync(uploadPath)) {
                fs.mkdirSync(uploadPath, { recursive: true });
            }
            fs.writeFileSync(filePath, file.buffer);
            return {
                document,
                file: {
                    fileName,
                    originalName: file.originalname,
                    size: file.size,
                    mimeType: file.mimetype,
                    path: `/uploads/documents/${fileName}`,
                    uploadedAt: new Date(),
                },
            };
        }
        catch (error) {
            throw error;
        }
    }
    async downloadFile(id) {
        const document = await this.findOne(id);
        throw new common_1.NotFoundException('File download functionality not implemented yet');
    }
    async validateRelationships(dto) {
        const [organization, documentType] = await Promise.all([
            this.prisma.organization.findUnique({ where: { id: dto.organizationId } }),
            this.prisma.documentType.findUnique({ where: { id: dto.documentTypeId } }),
        ]);
        if (!organization) {
            throw new common_1.NotFoundException(`Organization with ID ${dto.organizationId} not found`);
        }
        if (!documentType) {
            throw new common_1.NotFoundException(`Document type with ID ${dto.documentTypeId} not found`);
        }
    }
};
exports.DocumentsService = DocumentsService;
exports.DocumentsService = DocumentsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], DocumentsService);
//# sourceMappingURL=documents.service.js.map