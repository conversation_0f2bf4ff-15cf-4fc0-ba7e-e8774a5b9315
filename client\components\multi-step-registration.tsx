"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, ArrowLeft, ArrowRight, Check } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { masterDataService } from "@/lib/services/master-data.service"

interface Country {
  name: {
    common: string
    official: string
  }
  cca2: string
  cca3: string
}

interface RegistrationData {
  // Step 1: Account Information
  firstName: string
  lastName: string
  email: string
  password: string
  confirmPassword: string

  // Step 2: Organization Information
  organizationName: string
  organizationCountry: string
  organizationPhoneNumber: string
  organizationEmail: string
  organizationWebsite: string
  homeCountryRepresentative: string
  rwandaRepresentative: string
  organizationRgbNumber: string
  organizationTypeId: number

  // Step 3: Address Information
  addresses: Array<{
    addressType: "HEADQUARTERS" | "RWANDA"
    country: string
    province?: string
    district?: string
    sector?: string
    cell?: string
    village?: string
    street: string
    avenue?: string
    poBox: string
    postalCode?: string
  }>
}

interface MultiStepRegistrationProps {
  onSuccess: () => void
  onCancel: () => void
}

export function MultiStepRegistration({ onSuccess, onCancel }: MultiStepRegistrationProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [organizationTypes, setOrganizationTypes] = useState<any[]>([])
  const [countries, setCountries] = useState<Country[]>([])
  const [loadingCountries, setLoadingCountries] = useState(false)
  const { register } = useAuth()

  const [formData, setFormData] = useState<RegistrationData>({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    organizationName: "",
    organizationCountry: "",
    organizationPhoneNumber: "",
    organizationEmail: "",
    organizationWebsite: "",
    homeCountryRepresentative: "",
    rwandaRepresentative: "",
    organizationRgbNumber: "",
    organizationTypeId: 0,
    addresses: [
      {
        addressType: "HEADQUARTERS",
        country: "",
        street: "",
        poBox: "",
      }
    ]
  })

  useEffect(() => {
    const loadOrganizationTypes = async () => {
      try {
        const types = await masterDataService.getOrganizationTypes()
        setOrganizationTypes(types)
      } catch (error) {
        console.error("Failed to load organization types:", error)
      }
    }
    loadOrganizationTypes()
  }, [])

  useEffect(() => {
    const loadCountries = async () => {
      setLoadingCountries(true)
      try {
        const response = await fetch('https://restcountries.com/v3.1/all?fields=name,cca2,cca3')
        const countriesData: Country[] = await response.json()
        // Sort countries alphabetically by common name
        const sortedCountries = countriesData.sort((a, b) =>
          a.name.common.localeCompare(b.name.common)
        )
        setCountries(sortedCountries)
      } catch (error) {
        console.error("Failed to load countries:", error)
        setError("Failed to load countries. Please refresh the page.")
      } finally {
        setLoadingCountries(false)
      }
    }
    loadCountries()
  }, [])

  // Update address types when organization country changes
  useEffect(() => {
    if (formData.organizationCountry) {
      const newAddressType = isLocalOrganization() ? "HEADQUARTERS" : "RWANDA"
      const newCountry = isLocalOrganization() ? formData.organizationCountry : "Rwanda"

      setFormData(prev => ({
        ...prev,
        addresses: prev.addresses.map(addr => ({
          ...addr,
          addressType: newAddressType,
          country: newCountry
        }))
      }))
    }
  }, [formData.organizationCountry])

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const updateAddress = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      addresses: prev.addresses.map((addr, i) =>
        i === index ? { ...addr, [field]: value } : addr
      )
    }))
  }

  // Helper function to determine if organization is local (Rwanda-based)
  const isLocalOrganization = () => {
    return formData.organizationCountry.toLowerCase() === 'rwanda'
  }

  // Get the appropriate address label based on organization type and address type
  const getAddressLabel = (addressType: "HEADQUARTERS" | "RWANDA") => {
    if (addressType === "RWANDA") {
      return "Rwanda Address"
    }

    if (isLocalOrganization()) {
      return "Headquarters Address"
    } else {
      return "Rwanda Address"
    }
  }

  const validateStep = (step: number): boolean => {
    setError("")

    switch (step) {
      case 1:
        if (!formData.firstName || !formData.lastName || !formData.email || !formData.password) {
          setError("Please fill in all required fields")
          return false
        }
        if (formData.password !== formData.confirmPassword) {
          setError("Passwords do not match")
          return false
        }
        if (formData.password.length < 8) {
          setError("Password must be at least 8 characters long")
          return false
        }
        break

      case 2:
        // Basic required fields for all organizations
        if (!formData.organizationName || !formData.organizationCountry ||
            !formData.organizationPhoneNumber || !formData.organizationEmail ||
            !formData.homeCountryRepresentative || !formData.rwandaRepresentative ||
            !formData.organizationTypeId) {
          setError("Please fill in all required organization fields")
          return false
        }

        // RGB number is only required for local (Rwanda-based) organizations
        if (isLocalOrganization() && !formData.organizationRgbNumber) {
          setError("RGB number is required for Rwanda-based organizations")
          return false
        }
        break

      case 3:
        if (formData.addresses.length === 0) {
          setError("At least one address is required")
          return false
        }
        for (const addr of formData.addresses) {
          if (!addr.country || !addr.street || !addr.poBox) {
            setError("Please fill in all required address fields")
            return false
          }
        }
        break
    }

    return true
  }

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1)
    }
  }

  const handlePrevious = () => {
    setCurrentStep(prev => prev - 1)
  }

  const handleSubmit = async () => {
    if (!validateStep(3)) return

    setLoading(true)
    try {
      const registrationData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        password: formData.password,
        organization: {
          organizationName: formData.organizationName,
          organizationCountry: formData.organizationCountry,
          organizationPhoneNumber: formData.organizationPhoneNumber,
          organizationEmail: formData.organizationEmail,
          organizationWebsite: formData.organizationWebsite || undefined,
          homeCountryRepresentative: formData.homeCountryRepresentative,
          rwandaRepresentative: formData.rwandaRepresentative,
          organizationRgbNumber: isLocalOrganization() ? formData.organizationRgbNumber : undefined,
          organizationTypeId: formData.organizationTypeId,
          addresses: formData.addresses
        }
      }

      await register(registrationData)
      onSuccess()
    } catch (error: any) {
      setError(error.response?.data?.message || "Registration failed. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {[1, 2, 3].map((step) => (
        <React.Fragment key={step}>
          <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
            step <= currentStep ? 'bg-cyan-600 text-white' : 'bg-gray-200 text-gray-600'
          }`}>
            {step < currentStep ? <Check className="w-4 h-4" /> : step}
          </div>
          {step < 3 && (
            <div className={`w-16 h-1 ${
              step < currentStep ? 'bg-cyan-600' : 'bg-gray-200'
            }`} />
          )}
        </React.Fragment>
      ))}
    </div>
  )

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">Partner Registration</CardTitle>
        <CardDescription className="text-center">
          Register your organization as a partner with the Ministry of Health
        </CardDescription>
        {renderStepIndicator()}
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Step 1: Account Information */}
        {currentStep === 1 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold mb-4">Account Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => updateFormData("firstName", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => updateFormData("lastName", e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email Address *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => updateFormData("email", e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password *</Label>
              <Input
                id="password"
                type="password"
                value={formData.password}
                onChange={(e) => updateFormData("password", e.target.value)}
                required
              />
              <p className="text-sm text-gray-600">
                Password must be at least 8 characters with uppercase, lowercase, and number/special character
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password *</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => updateFormData("confirmPassword", e.target.value)}
                required
              />
            </div>
          </div>
        )}

        {/* Step 2: Organization Information */}
        {currentStep === 2 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold mb-4">Organization Information</h3>
            <div className="space-y-2">
              <Label htmlFor="organizationName">Organization Name *</Label>
              <Input
                id="organizationName"
                value={formData.organizationName}
                onChange={(e) => updateFormData("organizationName", e.target.value)}
                required
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="organizationCountry">Organization Country *</Label>
                <Select
                  value={formData.organizationCountry}
                  onValueChange={(value) => updateFormData("organizationCountry", value)}
                  disabled={loadingCountries}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={loadingCountries ? "Loading countries..." : "Select country"} />
                  </SelectTrigger>
                  <SelectContent>
                    {countries.map((country) => (
                      <SelectItem key={country.cca2} value={country.name.common}>
                        {country.name.common}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {isLocalOrganization() && (
                <div className="space-y-2">
                  <Label htmlFor="organizationRgbNumber">RGB Number *</Label>
                  <Input
                    id="organizationRgbNumber"
                    value={formData.organizationRgbNumber}
                    onChange={(e) => updateFormData("organizationRgbNumber", e.target.value)}
                    required
                    placeholder="Required for Rwanda-based organizations"
                  />
                </div>
              )}
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="organizationPhoneNumber">Phone Number *</Label>
                <Input
                  id="organizationPhoneNumber"
                  value={formData.organizationPhoneNumber}
                  onChange={(e) => updateFormData("organizationPhoneNumber", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="organizationEmail">Organization Email *</Label>
                <Input
                  id="organizationEmail"
                  type="email"
                  value={formData.organizationEmail}
                  onChange={(e) => updateFormData("organizationEmail", e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="organizationWebsite">Website (Optional)</Label>
              <Input
                id="organizationWebsite"
                value={formData.organizationWebsite}
                onChange={(e) => updateFormData("organizationWebsite", e.target.value)}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="homeCountryRepresentative">Home Country Representative *</Label>
                <Input
                  id="homeCountryRepresentative"
                  value={formData.homeCountryRepresentative}
                  onChange={(e) => updateFormData("homeCountryRepresentative", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="rwandaRepresentative">Rwanda Representative *</Label>
                <Input
                  id="rwandaRepresentative"
                  value={formData.rwandaRepresentative}
                  onChange={(e) => updateFormData("rwandaRepresentative", e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="organizationTypeId">Organization Type *</Label>
              <Select
                value={formData.organizationTypeId.toString()}
                onValueChange={(value) => updateFormData("organizationTypeId", parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select organization type" />
                </SelectTrigger>
                <SelectContent>
                  {organizationTypes.map((type) => (
                    <SelectItem key={type.id} value={type.id.toString()}>
                      {type.typeName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {/* Step 3: Address Information */}
        {currentStep === 3 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold mb-4">Address Information</h3>
            <p className="text-sm text-gray-600 mb-4">
              {isLocalOrganization()
                ? "Provide your organization's headquarters address in Rwanda."
                : "Provide your organization's address in Rwanda (local presence address)."
              }
            </p>

            {formData.addresses.map((address, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-4">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">
                    {getAddressLabel(address.addressType)}
                  </h4>
                </div>

                {/* Address type is automatically determined based on organization type */}
                <input
                  type="hidden"
                  value={isLocalOrganization() ? "HEADQUARTERS" : "RWANDA"}
                  onChange={(e) => updateAddress(index, "addressType", e.target.value)}
                />

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Country *</Label>
                    <Input
                      value={isLocalOrganization() ? formData.organizationCountry : "Rwanda"}
                      disabled
                      className="bg-gray-50"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Province/State</Label>
                    <Input
                      value={address.province || ""}
                      onChange={(e) => updateAddress(index, "province", e.target.value)}
                    />
                  </div>
                </div>

                {/* Show Rwanda-specific address fields for all addresses since they're all in Rwanda */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>District</Label>
                    <Input
                      value={address.district || ""}
                      onChange={(e) => updateAddress(index, "district", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Sector</Label>
                    <Input
                      value={address.sector || ""}
                      onChange={(e) => updateAddress(index, "sector", e.target.value)}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Cell</Label>
                    <Input
                      value={address.cell || ""}
                      onChange={(e) => updateAddress(index, "cell", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Village</Label>
                    <Input
                      value={address.village || ""}
                      onChange={(e) => updateAddress(index, "village", e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Street *</Label>
                    <Input
                      value={address.street}
                      onChange={(e) => updateAddress(index, "street", e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Avenue</Label>
                    <Input
                      value={address.avenue || ""}
                      onChange={(e) => updateAddress(index, "avenue", e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>P.O. Box *</Label>
                    <Input
                      value={address.poBox}
                      onChange={(e) => updateAddress(index, "poBox", e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Postal Code</Label>
                    <Input
                      value={address.postalCode || ""}
                      onChange={(e) => updateAddress(index, "postalCode", e.target.value)}
                    />
                  </div>
                </div>
              </div>
            ))}


          </div>
        )}
        
        <div className="flex justify-between mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={currentStep === 1 ? onCancel : handlePrevious}
            disabled={loading}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            {currentStep === 1 ? "Cancel" : "Previous"}
          </Button>

          {currentStep < 3 ? (
            <Button type="button" onClick={handleNext} disabled={loading}>
              Next
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          ) : (
            <Button type="button" onClick={handleSubmit} disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Registering...
                </>
              ) : (
                "Complete Registration"
              )}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
