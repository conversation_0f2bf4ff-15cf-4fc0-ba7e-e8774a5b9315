"use client"

import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { MouApplicationWizard } from "@/components/mou-application-wizard"
import { MouApplication } from "@/lib/services/mou-application.service"

export default function NewMouApplicationPage() {
  const { user } = useAuth()
  const router = useRouter()

  const handleComplete = (application: MouApplication) => {
    router.push(`/dashboard/partner/applications/${application.id}`)
  }

  const handleCancel = () => {
    router.push("/dashboard/partner/applications")
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Button asChild variant="ghost" size="sm">
          <Link href="/dashboard/partner/applications">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Applications
          </Link>
        </Button>
      </div>

      <MouApplicationWizard
        onComplete={handleComplete}
        onCancel={handleCancel}
      />
    </div>
  )
}
