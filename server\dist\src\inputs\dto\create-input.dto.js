"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InputSubclassWithRelationsDto = exports.InputSubclassResponseDto = exports.UpdateInputSubclassDto = exports.CreateInputSubclassDto = exports.InputWithRelationsDto = exports.InputResponseDto = exports.UpdateInputDto = exports.CreateInputDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateInputDto {
}
exports.CreateInputDto = CreateInputDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Input name',
        example: 'Medical Equipment',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateInputDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Input subclass ID',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateInputDto.prototype, "inputSubclassId", void 0);
class UpdateInputDto {
}
exports.UpdateInputDto = UpdateInputDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Input name',
        example: 'Updated Medical Equipment',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UpdateInputDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Input subclass ID',
        example: 2,
        required: false,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], UpdateInputDto.prototype, "inputSubclassId", void 0);
class InputResponseDto {
}
exports.InputResponseDto = InputResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Input ID', example: 1 }),
    __metadata("design:type", Number)
], InputResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Input name', example: 'Medical Equipment' }),
    __metadata("design:type", String)
], InputResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Input subclass ID', example: 1 }),
    __metadata("design:type", Number)
], InputResponseDto.prototype, "inputSubclassId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation date' }),
    __metadata("design:type", Date)
], InputResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update date' }),
    __metadata("design:type", Date)
], InputResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Soft delete flag' }),
    __metadata("design:type", Boolean)
], InputResponseDto.prototype, "deleted", void 0);
class InputWithRelationsDto extends InputResponseDto {
}
exports.InputWithRelationsDto = InputWithRelationsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Input subclass information' }),
    __metadata("design:type", Object)
], InputWithRelationsDto.prototype, "inputSubclass", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Associated activities' }),
    __metadata("design:type", Array)
], InputWithRelationsDto.prototype, "activities", void 0);
class CreateInputSubclassDto {
}
exports.CreateInputSubclassDto = CreateInputSubclassDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Subclass ID',
        example: 101,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateInputSubclassDto.prototype, "subclassId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Subclass name',
        example: 'Diagnostic Equipment',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateInputSubclassDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Budget amount',
        example: 50000.00,
    }),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreateInputSubclassDto.prototype, "budget", void 0);
class UpdateInputSubclassDto {
}
exports.UpdateInputSubclassDto = UpdateInputSubclassDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Subclass name',
        example: 'Updated Diagnostic Equipment',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UpdateInputSubclassDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Budget amount',
        example: 75000.00,
        required: false,
    }),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], UpdateInputSubclassDto.prototype, "budget", void 0);
class InputSubclassResponseDto {
}
exports.InputSubclassResponseDto = InputSubclassResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Input subclass ID', example: 1 }),
    __metadata("design:type", Number)
], InputSubclassResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Subclass ID', example: 101 }),
    __metadata("design:type", Number)
], InputSubclassResponseDto.prototype, "subclassId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Subclass name', example: 'Diagnostic Equipment' }),
    __metadata("design:type", String)
], InputSubclassResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Budget amount', example: 50000.00 }),
    __metadata("design:type", Number)
], InputSubclassResponseDto.prototype, "budget", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation date' }),
    __metadata("design:type", Date)
], InputSubclassResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update date' }),
    __metadata("design:type", Date)
], InputSubclassResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Soft delete flag' }),
    __metadata("design:type", Boolean)
], InputSubclassResponseDto.prototype, "deleted", void 0);
class InputSubclassWithRelationsDto extends InputSubclassResponseDto {
}
exports.InputSubclassWithRelationsDto = InputSubclassWithRelationsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Associated inputs' }),
    __metadata("design:type", Array)
], InputSubclassWithRelationsDto.prototype, "inputs", void 0);
//# sourceMappingURL=create-input.dto.js.map