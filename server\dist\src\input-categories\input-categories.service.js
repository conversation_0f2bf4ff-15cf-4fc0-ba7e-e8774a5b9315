"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var InputCategoriesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.InputCategoriesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let InputCategoriesService = InputCategoriesService_1 = class InputCategoriesService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(InputCategoriesService_1.name);
    }
    async create(createInputCategoryDto, userId) {
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can create input categories');
            }
            if (createInputCategoryDto.parentId) {
                const parent = await this.prisma.inputCategory.findFirst({
                    where: {
                        id: createInputCategoryDto.parentId,
                        deleted: false
                    }
                });
                if (!parent) {
                    throw new common_1.BadRequestException('Parent input category not found');
                }
            }
            const existing = await this.prisma.inputCategory.findFirst({
                where: {
                    categoryName: createInputCategoryDto.categoryName,
                    parentId: createInputCategoryDto.parentId,
                    deleted: false
                }
            });
            if (existing) {
                throw new common_1.ConflictException('Input category with this name already exists at this level');
            }
            const category = await this.prisma.inputCategory.create({
                data: {
                    categoryName: createInputCategoryDto.categoryName,
                    description: createInputCategoryDto.description,
                    parentId: createInputCategoryDto.parentId
                },
                include: {
                    parent: true,
                    children: true
                }
            });
            return category;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException ||
                error instanceof common_1.ForbiddenException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to create input category', error.stack);
            throw new Error('Failed to create input category');
        }
    }
    async findAll() {
        return this.prisma.inputCategory.findMany({
            where: {
                deleted: false
            },
            include: {
                parent: true,
                children: true
            },
            orderBy: {
                categoryName: 'asc'
            }
        });
    }
    async findTree() {
        const categories = await this.prisma.inputCategory.findMany({
            where: {
                deleted: false
            },
            orderBy: {
                categoryName: 'asc'
            }
        });
        const buildTree = (parentId = null) => {
            return categories
                .filter(c => c.parentId === parentId)
                .map(c => ({
                ...c,
                children: buildTree(c.id)
            }));
        };
        return buildTree(null);
    }
    async findOne(id) {
        const category = await this.prisma.inputCategory.findFirst({
            where: {
                id,
                deleted: false
            },
            include: {
                parent: true,
                children: true
            }
        });
        if (!category) {
            throw new common_1.NotFoundException('Input category not found');
        }
        return category;
    }
    async update(id, updateInputCategoryDto, userId) {
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can update input categories');
            }
            const existing = await this.prisma.inputCategory.findFirst({
                where: {
                    id,
                    deleted: false
                },
                include: {
                    children: true
                }
            });
            if (!existing) {
                throw new common_1.NotFoundException('Input category not found');
            }
            if (updateInputCategoryDto.parentId !== undefined) {
                if (updateInputCategoryDto.parentId === id) {
                    throw new common_1.BadRequestException('Cannot set category as its own parent');
                }
                const isDescendant = async (childId, potentialParentId) => {
                    const child = await this.prisma.inputCategory.findUnique({
                        where: { id: childId },
                        include: { children: true }
                    });
                    if (!child)
                        return false;
                    if (child.id === potentialParentId)
                        return true;
                    for (const descendant of child.children) {
                        if (await isDescendant(descendant.id, potentialParentId)) {
                            return true;
                        }
                    }
                    return false;
                };
                if (updateInputCategoryDto.parentId &&
                    await isDescendant(updateInputCategoryDto.parentId, id)) {
                    throw new common_1.BadRequestException('Cannot set a descendant as parent');
                }
                if (updateInputCategoryDto.parentId) {
                    const parent = await this.prisma.inputCategory.findFirst({
                        where: {
                            id: updateInputCategoryDto.parentId,
                            deleted: false
                        }
                    });
                    if (!parent) {
                        throw new common_1.BadRequestException('Parent input category not found');
                    }
                }
            }
            if (updateInputCategoryDto.categoryName &&
                updateInputCategoryDto.categoryName !== existing.categoryName) {
                const nameConflict = await this.prisma.inputCategory.findFirst({
                    where: {
                        categoryName: updateInputCategoryDto.categoryName,
                        parentId: updateInputCategoryDto.parentId ?? existing.parentId,
                        deleted: false,
                        id: { not: id }
                    }
                });
                if (nameConflict) {
                    throw new common_1.ConflictException('Another input category with this name exists at this level');
                }
            }
            const updated = await this.prisma.inputCategory.update({
                where: { id },
                data: {
                    categoryName: updateInputCategoryDto.categoryName,
                    description: updateInputCategoryDto.description,
                    parentId: updateInputCategoryDto.parentId
                },
                include: {
                    parent: true,
                    children: true
                }
            });
            return updated;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException ||
                error instanceof common_1.ForbiddenException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to update input category', error.stack);
            throw new Error('Failed to update input category');
        }
    }
    async remove(id, userId) {
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can delete input categories');
            }
            const existing = await this.prisma.inputCategory.findFirst({
                where: {
                    id,
                    deleted: false
                },
                include: {
                    children: {
                        where: {
                            deleted: false
                        }
                    }
                }
            });
            if (!existing) {
                throw new common_1.NotFoundException('Input category not found');
            }
            if (existing.children.length > 0) {
                throw new common_1.ConflictException('Cannot delete input category that has child categories');
            }
            await this.prisma.inputCategory.update({
                where: { id },
                data: { deleted: true }
            });
            return { success: true };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException ||
                error instanceof common_1.ForbiddenException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to delete input category', error.stack);
            throw new Error('Failed to delete input category');
        }
    }
};
exports.InputCategoriesService = InputCategoriesService;
exports.InputCategoriesService = InputCategoriesService = InputCategoriesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], InputCategoriesService);
//# sourceMappingURL=input-categories.service.js.map