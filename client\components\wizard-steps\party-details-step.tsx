import React, { use<PERSON><PERSON>back, useState } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useFieldArray, useForm } from 'react-hook-form'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useFieldBlurAutoSave } from '@/hooks/use-mou-form'
import { useMouStore } from '@/store/mou-store'
import { partySchema } from '@/lib/validations/mou'
import { Trash2, Plus, X, Info, Calendar, Lock, Mail, AlertTriangle, Edit } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/contexts/auth-context'

type FormData = {
  parties: z.infer<typeof partySchema>[]
}

// Create a modified party schema that makes responsibilities optional for form validation
const formPartySchema = z.object({
  name: z.string().min(1, 'Organization name is required'),
  signatoryName: z.string().min(1, 'Signatory name is required'),
  position: z.string().min(1, 'Position is required'),
  responsibilities: z.string().optional() // Optional for form validation
})

// Dynamic form schema based on user role and organization
const createFormSchema = (isPartner: boolean, canHaveMultipleParties: boolean) => z.object({
  parties: z.array(formPartySchema).min(1, 'At least one party is required').max(
    !isPartner ? 10 : (canHaveMultipleParties ? 2 : 1),
    !isPartner
      ? 'Maximum 10 parties allowed'
      : canHaveMultipleParties
        ? 'Your organization can have maximum 2 parties'
        : 'Your organization can only have one party'
  )
})

export function PartyDetailsStep() {
  const { toast } = useToast()
  const { user } = useAuth()
  const { onBlur } = useFieldBlurAutoSave()
  const parties = useMouStore(state => state.parties)
  const addParty = useMouStore(state => state.addParty)
  const removeParty = useMouStore(state => state.removeParty)
  const updateParty = useMouStore(state => state.updateParty)
  const initializeDefaultParties = useMouStore(state => state.initializeDefaultParties)

  // Check if current user is a partner
  const isPartner = user?.role === 'PARTNER'

  // Organizations that can have multiple parties (configurable list)
  const multiPartyOrganizations = [
    'Ministry of Health',
    'World Health Organization',
    'UNICEF',
    'Government Agency',
    'International Organization'
  ]

  // Check if current organization can have multiple parties
  const canHaveMultipleParties = React.useMemo(() => {
    if (!isPartner) return true // Admins can always have multiple parties

    // Check demo flag first
    if (localStorage.getItem('demoMultiPartyOrg') === 'true') return true

    const userOrgName = user?.organization?.organizationName || ''
    return multiPartyOrganizations.some(org =>
      userOrgName.toLowerCase().includes(org.toLowerCase()) ||
      org.toLowerCase().includes(userOrgName.toLowerCase())
    )
  }, [isPartner, user?.organization?.organizationName])

  // For partners, check if they can make changes (one year rule)
  const canMakeChanges = React.useMemo(() => {
    if (!isPartner) return true

    // For demo purposes, we'll simulate a last change date
    // In real implementation, this would come from the user's profile or MoU data
    const lastChangeDate = localStorage.getItem('lastPartyChangeDate')
    if (!lastChangeDate) return true

    const oneYearAgo = new Date()
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1)

    return new Date(lastChangeDate) <= oneYearAgo
  }, [isPartner])

  // Get next allowed change date for partners
  const nextAllowedChangeDate = React.useMemo(() => {
    if (!isPartner) return null

    const lastChangeDate = localStorage.getItem('lastPartyChangeDate')
    if (!lastChangeDate) return null

    const nextDate = new Date(lastChangeDate)
    nextDate.setFullYear(nextDate.getFullYear() + 1)

    return nextDate
  }, [isPartner])

  // State for managing responsibilities for each party
  const [partyResponsibilities, setPartyResponsibilities] = useState<{[key: string]: string[]}>({})
  const [newResponsibility, setNewResponsibility] = useState<{[key: string]: string}>({})

  const {
    register,
    control,
    formState: { errors },
    watch,
    setValue,
    reset
  } = useForm<FormData>({
    resolver: zodResolver(createFormSchema(isPartner, canHaveMultipleParties)),
    defaultValues: {
      parties: parties.length > 0 ? parties : [
        { name: '', signatoryName: '', position: '', responsibilities: '' }
      ]
    }
  })

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'parties'
  })

  // Initialize default parties if none exist
  React.useEffect(() => {
    if (parties.length === 0) {
      // Everyone starts with one party - Party 1 always gets organization name
      const defaultParty = {
        name: user?.organization?.organizationName || '',
        signatoryName: `${user?.firstName || ''} ${user?.lastName || ''}`.trim(),
        position: '',
        responsibilities: ''
      }
      addParty(defaultParty)
    }
  }, [parties.length, user, addParty])

  // Ensure Party 1 always has the organization name (auto-populate if empty)
  React.useEffect(() => {
    if (parties.length > 0 && user?.organization?.organizationName) {
      const primaryParty = parties[0]
      if (!primaryParty.name || primaryParty.name.trim() === '') {
        updateParty(primaryParty.id, {
          name: user.organization.organizationName,
          signatoryName: primaryParty.signatoryName || `${user?.firstName || ''} ${user?.lastName || ''}`.trim()
        })
      }
    }
  }, [parties, user, updateParty])

  // Initialize responsibilities from existing parties and sync form
  React.useEffect(() => {
    if (parties.length > 0) {
      const responsibilitiesMap: {[key: string]: string[]} = {}
      parties.forEach((party) => {
        if (party.responsibilities) {
          responsibilitiesMap[party.id] = party.responsibilities.split('; ').filter(r => r.trim())
        }
      })
      setPartyResponsibilities(responsibilitiesMap)

      // Reset form with current parties
      reset({ parties })
    }
  }, [parties, reset])

  // Sync form fields with store when parties change
  React.useEffect(() => {
    if (parties.length > 0 && parties.length !== fields.length) {
      reset({ parties })
    }
  }, [parties, fields.length, reset])

  const handleAddParty = useCallback(() => {
    // Check organization-specific party limits
    const maxParties = !isPartner ? 10 : (canHaveMultipleParties ? 2 : 1)

    if (parties.length >= maxParties) {
      const message = !isPartner
        ? "Maximum 10 parties allowed"
        : canHaveMultipleParties
          ? "Your organization can have maximum 2 parties"
          : "Your organization can only have one party. Contact admin if you need to add a second party."

      toast({
        title: "Cannot Add Party",
        description: message,
        variant: "destructive",
        duration: 5000,
      })
      return
    }

    // Check if partner can make changes
    if (isPartner && !canMakeChanges) {
      toast({
        title: "Cannot Add Party",
        description: `You can only make changes once per year. Next allowed change: ${nextAllowedChangeDate?.toLocaleDateString()}`,
        variant: "destructive",
        duration: 5000,
      })
      return
    }

    const newParty = {
      name: '',
      signatoryName: '',
      position: '',
      responsibilities: ''
    }

    // Add to form
    append(newParty)

    // Add to store
    addParty(newParty)

    // Update last change date for partners
    if (isPartner) {
      localStorage.setItem('lastPartyChangeDate', new Date().toISOString())
    }

    // Show success toast
    toast({
      title: "Party Added",
      description: "A new party has been added to the MoU application.",
      duration: 3000,
    })
  }, [append, addParty, toast, isPartner, parties.length, canMakeChanges, nextAllowedChangeDate])

  const handleRemoveParty = useCallback((index: number) => {
    const party = parties[index]
    const partyName = party?.name || `Party ${index + 1}`

    // Check minimum party requirement (always at least 1)
    if (parties.length <= 1) {
      toast({
        title: "Cannot Remove Party",
        description: "At least one party is required in the MoU application.",
        variant: "destructive",
        duration: 4000,
      })
      return
    }

    // Check if partner can make changes
    if (isPartner && !canMakeChanges) {
      toast({
        title: "Cannot Remove Party",
        description: `You can only make changes once per year. Next allowed change: ${nextAllowedChangeDate?.toLocaleDateString()}`,
        variant: "destructive",
        duration: 5000,
      })
      return
    }

    if (party && party.id) {
      // Remove from store
      removeParty(party.id)

      // Clean up responsibilities state
      const newResponsibilities = { ...partyResponsibilities }
      delete newResponsibilities[party.id]
      setPartyResponsibilities(newResponsibilities)

      const newResponsibilityInputs = { ...newResponsibility }
      delete newResponsibilityInputs[party.id]
      setNewResponsibility(newResponsibilityInputs)
    }

    // Remove from form
    remove(index)

    // Update last change date for partners
    if (isPartner) {
      localStorage.setItem('lastPartyChangeDate', new Date().toISOString())
    }

    // Show success toast
    toast({
      title: "Party Removed",
      description: `${partyName} has been removed from the MoU application.`,
      duration: 3000,
    })
  }, [remove, removeParty, parties, partyResponsibilities, newResponsibility, toast, isPartner, canMakeChanges, nextAllowedChangeDate])

  // Handle field changes and update store
  const handleFieldChange = useCallback((index: number, field: string, value: string) => {
    // Check if partner can make changes (only for organization name changes)
    if (isPartner && !canMakeChanges && field === 'name') {
      toast({
        title: "Cannot Modify Organization Name",
        description: `You can only make changes to organization name once per year. Next allowed change: ${nextAllowedChangeDate?.toLocaleDateString()}`,
        variant: "destructive",
        duration: 5000,
      })
      return
    }

    const party = parties[index]
    if (party && party.id) {
      updateParty(party.id, { [field]: value })

      // Update last change date for partners on organization name changes only
      if (isPartner && field === 'name') {
        localStorage.setItem('lastPartyChangeDate', new Date().toISOString())
      }
    } else {
      // For new parties without ID, update the form value
      setValue(`parties.${index}.${field}` as any, value)
    }
  }, [parties, updateParty, setValue, isPartner, canMakeChanges, nextAllowedChangeDate, toast])

  // Responsibility management functions
  const addResponsibility = useCallback((partyIndex: number) => {
    const party = parties[partyIndex]
    if (!party) return

    const partyId = party.id || `temp-${partyIndex}`
    const responsibility = newResponsibility[partyId]?.trim()
    if (!responsibility) return

    const currentResponsibilities = partyResponsibilities[partyId] || []
    const updatedResponsibilities = [...currentResponsibilities, responsibility]

    setPartyResponsibilities(prev => ({
      ...prev,
      [partyId]: updatedResponsibilities
    }))

    setNewResponsibility(prev => ({
      ...prev,
      [partyId]: ''
    }))

    // Update the party in the store with the new responsibilities
    if (party.id) {
      updateParty(party.id, { responsibilities: updatedResponsibilities.join('; ') })
    }

    // Also update the form field
    setValue(`parties.${partyIndex}.responsibilities`, updatedResponsibilities.join('; '))
  }, [newResponsibility, partyResponsibilities, parties, updateParty, setValue])

  const removeResponsibility = useCallback((partyIndex: number, responsibilityIndex: number) => {
    const party = parties[partyIndex]
    if (!party) return

    const partyId = party.id || `temp-${partyIndex}`
    const currentResponsibilities = partyResponsibilities[partyId] || []
    const updatedResponsibilities = currentResponsibilities.filter((_, index) => index !== responsibilityIndex)

    setPartyResponsibilities(prev => ({
      ...prev,
      [partyId]: updatedResponsibilities
    }))

    // Update the party in the store
    if (party.id) {
      updateParty(party.id, { responsibilities: updatedResponsibilities.join('; ') })
    }

    // Also update the form field
    setValue(`parties.${partyIndex}.responsibilities`, updatedResponsibilities.join('; '))
  }, [partyResponsibilities, parties, updateParty, setValue])

  const handleResponsibilityInputChange = useCallback((partyIndex: number, value: string) => {
    const party = parties[partyIndex]
    if (!party) return

    const partyId = party.id || `temp-${partyIndex}`
    setNewResponsibility(prev => ({
      ...prev,
      [partyId]: value
    }))
  }, [parties])

  const handleResponsibilityKeyPress = useCallback((e: React.KeyboardEvent, partyIndex: number) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addResponsibility(partyIndex)
    }
  }, [addResponsibility])

  // Get responsibilities for a party
  const getPartyResponsibilities = useCallback((partyIndex: number) => {
    const party = parties[partyIndex]
    if (!party) return []

    const partyId = party.id || `temp-${partyIndex}`
    return partyResponsibilities[partyId] || []
  }, [parties, partyResponsibilities])

  // Get new responsibility input value for a party
  const getNewResponsibilityValue = useCallback((partyIndex: number) => {
    const party = parties[partyIndex]
    if (!party) return ''

    const partyId = party.id || `temp-${partyIndex}`
    return newResponsibility[partyId] || ''
  }, [parties, newResponsibility])

  // Helper functions for goals
  const [newGoalInputs, setNewGoalInputs] = useState<Record<string, string>>({})

  const getPartyGoal = useCallback((partyIndex: number): string => {
    const party = parties[partyIndex]
    return party?.goal || ''
  }, [parties])

  const getNewGoalValue = useCallback((partyIndex: number): string => {
    const party = parties[partyIndex]
    if (!party) return ''
    const partyId = party.id || `temp-${partyIndex}`
    return newGoalInputs[partyId] || ''
  }, [parties, newGoalInputs])

  const handleGoalInputChange = useCallback((partyIndex: number, value: string) => {
    const party = parties[partyIndex]
    if (!party) return
    const partyId = party.id || `temp-${partyIndex}`
    setNewGoalInputs(prev => ({
      ...prev,
      [partyId]: value
    }))
  }, [parties])

  const setGoal = useCallback((partyIndex: number) => {
    const newGoal = getNewGoalValue(partyIndex).trim()
    if (!newGoal) return

    const party = parties[partyIndex]
    if (party?.id) {
      updateParty(party.id, { goal: newGoal })
    }

    // Clear the input
    const partyId = party?.id || `temp-${partyIndex}`
    setNewGoalInputs(prev => ({
      ...prev,
      [partyId]: ''
    }))
  }, [parties, updateParty, getNewGoalValue])

  const removeGoal = useCallback((partyIndex: number) => {
    const party = parties[partyIndex]
    if (party?.id) {
      updateParty(party.id, { goal: '' })
    }
  }, [parties, updateParty])

  const handleGoalKeyPress = useCallback((e: React.KeyboardEvent, partyIndex: number) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      setGoal(partyIndex)
    }
  }, [setGoal])

  // Helper functions for objectives
  const [partyObjectives, setPartyObjectives] = useState<Record<string, string[]>>({})
  const [newObjectiveInputs, setNewObjectiveInputs] = useState<Record<string, string>>({})

  const getPartyObjectives = useCallback((partyIndex: number): string[] => {
    const party = parties[partyIndex]
    if (!party) return []
    const partyId = party.id || `temp-${partyIndex}`
    return partyObjectives[partyId] || []
  }, [parties, partyObjectives])

  const getNewObjectiveValue = useCallback((partyIndex: number): string => {
    const party = parties[partyIndex]
    if (!party) return ''
    const partyId = party.id || `temp-${partyIndex}`
    return newObjectiveInputs[partyId] || ''
  }, [parties, newObjectiveInputs])

  const handleObjectiveInputChange = useCallback((partyIndex: number, value: string) => {
    const party = parties[partyIndex]
    if (!party) return
    const partyId = party.id || `temp-${partyIndex}`
    setNewObjectiveInputs(prev => ({
      ...prev,
      [partyId]: value
    }))
  }, [parties])

  const addObjective = useCallback((partyIndex: number) => {
    const newObj = getNewObjectiveValue(partyIndex).trim()
    if (!newObj) return

    const party = parties[partyIndex]
    if (!party) return

    const partyId = party.id || `temp-${partyIndex}`
    const currentObjectives = partyObjectives[partyId] || []
    const updatedObjectives = [...currentObjectives, newObj]

    setPartyObjectives(prev => ({
      ...prev,
      [partyId]: updatedObjectives
    }))

    // Update the party in the store
    if (party.id) {
      updateParty(party.id, { objectives: updatedObjectives.join('; ') })
    }

    // Clear the input
    setNewObjectiveInputs(prev => ({
      ...prev,
      [partyId]: ''
    }))
  }, [parties, partyObjectives, updateParty, getNewObjectiveValue])

  const removeObjective = useCallback((partyIndex: number, objIndex: number) => {
    const party = parties[partyIndex]
    if (!party) return

    const partyId = party.id || `temp-${partyIndex}`
    const currentObjectives = partyObjectives[partyId] || []
    const updatedObjectives = currentObjectives.filter((_, index) => index !== objIndex)

    setPartyObjectives(prev => ({
      ...prev,
      [partyId]: updatedObjectives
    }))

    // Update the party in the store
    if (party.id) {
      updateParty(party.id, { objectives: updatedObjectives.join('; ') })
    }
  }, [parties, partyObjectives, updateParty])

  const handleObjectiveKeyPress = useCallback((e: React.KeyboardEvent, partyIndex: number) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addObjective(partyIndex)
    }
  }, [addObjective])

  // Handle contact admin for multi-party request
  const handleContactAdmin = useCallback(() => {
    const subject = encodeURIComponent('Request for Multi-Party MoU Access')
    const body = encodeURIComponent(
      `Dear Admin,\n\n` +
      `I am requesting access to add a second party to our MoU application.\n\n` +
      `Organization: ${user?.organization?.organizationName || 'N/A'}\n` +
      `User: ${user?.firstName} ${user?.lastName}\n` +
      `Email: ${user?.email}\n\n` +
      `Reason for second party: [Please describe why your organization needs a second party]\n\n` +
      `Thank you for your consideration.\n\n` +
      `Best regards,\n${user?.firstName} ${user?.lastName}`
    )

    // Open email client
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`)

    // Show confirmation toast
    toast({
      title: "Email Client Opened",
      description: "Please complete and send the email to request multi-party access.",
      duration: 5000,
    })
  }, [user, toast])

  return (
    <div className="space-y-6">
      {/* Debug Panel - Remove in production */}
      <Alert className="border-yellow-200 bg-yellow-50">
        <Info className="h-4 w-4 text-yellow-600" />
        <AlertDescription className="text-yellow-800">
          <div className="space-y-2">
            <p className="font-semibold text-sm">Debug Info:</p>
            <div className="text-xs space-y-1">
              <p><strong>Parties Count:</strong> {parties.length}</p>
              {parties.length > 0 && (
                <div>
                  <p><strong>Primary Party:</strong></p>
                  <ul className="ml-4 space-y-1">
                    <li>ID: "{parties[0]?.id || 'no-id'}"</li>
                    <li>Name: "{parties[0]?.name || 'empty'}" (length: {parties[0]?.name?.length || 0})</li>
                    <li>Signatory: "{parties[0]?.signatoryName || 'empty'}" (length: {parties[0]?.signatoryName?.length || 0})</li>
                    <li>Position: "{parties[0]?.position || 'empty'}" (length: {parties[0]?.position?.length || 0})</li>
                    <li>Responsibilities: "{parties[0]?.responsibilities || 'empty'}"</li>
                  </ul>
                  <p className="mt-2"><strong>Validation Check:</strong></p>
                  <ul className="ml-4 space-y-1">
                    <li>Name valid: {parties[0]?.name?.trim() ? '✅' : '❌'}</li>
                    <li>Signatory valid: {parties[0]?.signatoryName?.trim() ? '✅' : '❌'}</li>
                    <li>Position valid: {parties[0]?.position?.trim() ? '✅' : '❌'}</li>
                    <li>All required fields: {(parties[0]?.name?.trim() && parties[0]?.signatoryName?.trim() && parties[0]?.position?.trim()) ? '✅ Ready' : '❌ Missing'}</li>
                  </ul>
                  <div className="mt-2 space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        console.log('Manual validation test:', parties)
                        console.log('Primary party:', parties[0])
                        console.log('Validation status:', {
                          hasName: !!parties[0]?.name?.trim(),
                          hasSignatory: !!parties[0]?.signatoryName?.trim(),
                          hasPosition: !!parties[0]?.position?.trim(),
                          allValid: !!(parties[0]?.name?.trim() && parties[0]?.signatoryName?.trim() && parties[0]?.position?.trim())
                        })
                      }}
                      className="text-xs"
                    >
                      Test Validation
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={async () => {
                        try {
                          // Import the validation function
                          const { useStepNavigation } = await import('@/hooks/use-mou-form')
                          console.log('Testing step navigation validation...')
                        } catch (error) {
                          console.log('Direct validation test error:', error)
                        }
                      }}
                      className="text-xs bg-blue-50"
                    >
                      Test Next Logic
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </AlertDescription>
      </Alert>

      {/* Partner-specific information */}
      {isPartner && (
        <Alert className="border-blue-200 bg-blue-50">
          <Info className="h-4 w-4 text-blue-600" />
          <AlertDescription className="text-blue-800">
            <div className="space-y-2">
              <p className="font-semibold">Organization Party Rules:</p>
              <div className="mb-3 p-2 rounded-md bg-white border border-blue-200">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Your Organization Type:</span>
                  <Badge
                    variant={canHaveMultipleParties ? "default" : "secondary"}
                    className={canHaveMultipleParties ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}
                  >
                    {canHaveMultipleParties ? "Multi-Party Enabled" : "Standard Organization"}
                  </Badge>
                </div>
                <p className="text-xs text-gray-600 mt-1">
                  {user?.organization?.organizationName || 'Your Organization'}
                </p>
              </div>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>
                  <strong>Default:</strong> One party is sufficient for most organizations
                </li>
                <li>
                  <strong>Your Access:</strong> {canHaveMultipleParties ? 'Your organization can have up to 2 parties' : 'Your organization is limited to 1 party'}
                </li>
                <li>Changes to party details can only be made <strong>once per year</strong></li>
                <li>Minor changes (position, responsibilities) are allowed anytime</li>
              </ul>
              {!canMakeChanges && nextAllowedChangeDate && (
                <div className="flex items-center gap-2 mt-3 p-2 bg-amber-100 rounded-md">
                  <Lock className="h-4 w-4 text-amber-600" />
                  <span className="text-sm text-amber-800">
                    <strong>Change Restriction:</strong> Next allowed change date is{' '}
                    <span className="font-semibold">{nextAllowedChangeDate.toLocaleDateString()}</span>
                  </span>
                </div>
              )}

              {/* Demo buttons for testing */}
              <div className="flex flex-wrap gap-2 mt-3 pt-2 border-t border-blue-200">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    localStorage.removeItem('lastPartyChangeDate')
                    window.location.reload()
                  }}
                  className="text-xs"
                >
                  Reset Change Date (Demo)
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const recentDate = new Date()
                    recentDate.setMonth(recentDate.getMonth() - 6) // 6 months ago
                    localStorage.setItem('lastPartyChangeDate', recentDate.toISOString())
                    window.location.reload()
                  }}
                  className="text-xs"
                >
                  Simulate Recent Change (Demo)
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Simulate being a multi-party organization
                    localStorage.setItem('demoMultiPartyOrg', 'true')
                    window.location.reload()
                  }}
                  className="text-xs bg-green-50 border-green-200 text-green-700"
                >
                  Simulate Multi-Party Org (Demo)
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    localStorage.removeItem('demoMultiPartyOrg')
                    window.location.reload()
                  }}
                  className="text-xs bg-red-50 border-red-200 text-red-700"
                >
                  Reset to Standard Org (Demo)
                </Button>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* General information about party requirements */}
      {!isPartner && (
        <Alert className="border-gray-200 bg-gray-50">
          <Info className="h-4 w-4 text-gray-600" />
          <AlertDescription className="text-gray-700">
            <div className="space-y-2">
              <p className="font-semibold">Party Requirements:</p>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li><strong>Default:</strong> One party is sufficient for most MoU applications</li>
                <li><strong>Party 1 (Primary):</strong> Complete details including responsibilities</li>
                <li><strong>Party 2+ (Additional):</strong> Basic information only (Organization, Signatory, Position)</li>
                <li>Add additional parties only when necessary for the agreement</li>
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {errors.parties?.root && (
        <Alert variant="destructive">
          <AlertDescription>
            {errors.parties.root.message}
          </AlertDescription>
        </Alert>
      )}

      {fields.map((field, index) => (
        <Card key={field.id} className={`p-6 relative border-2 hover:border-gray-300 transition-colors ${
          index === 0
            ? 'border-blue-200 bg-blue-50/30'
            : 'border-gray-200 bg-gray-50/30'
        }`}>
          {/* Header with Party Title and Delete Button */}
          <div className="flex justify-between items-center mb-6 pb-4 border-b">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Party {index + 1}
                {parties[index]?.name && (
                  <span className="text-sm font-normal text-gray-500 ml-2">
                    ({parties[index].name})
                  </span>
                )}
              </h3>
              {index === 0 && (
                <p className="text-sm text-blue-600 font-medium mt-1">
                  Primary Party - Full Details Required
                </p>
              )}
              {index === 1 && (
                <p className="text-sm text-gray-600 font-medium mt-1">
                  Secondary Party - Basic Information Only
                </p>
              )}
            </div>
            <Button
              variant="destructive"
              size="sm"
              className="bg-red-500 hover:bg-red-600 text-white shadow-md"
              onClick={() => handleRemoveParty(index)}
              disabled={
                parties.length <= 1 ||
                (isPartner && !canMakeChanges)
              }
              title={
                parties.length <= 1
                  ? "At least one party is required"
                  : isPartner && !canMakeChanges
                  ? `Changes restricted until ${nextAllowedChangeDate?.toLocaleDateString()}`
                  : "Remove this party"
              }
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Remove Party
              {isPartner && !canMakeChanges && (
                <Lock className="h-3 w-3 ml-1" />
              )}
            </Button>
          </div>

          <div className={`grid gap-6 ${
            index === 0
              ? 'grid-cols-1 md:grid-cols-2'
              : 'grid-cols-1 md:grid-cols-3'
          }`}>
            <div className="space-y-2">
              <Label htmlFor={`parties.${index}.name`}>
                Organization Name
                {index === 0 && (
                  <span className="text-xs text-blue-600 ml-2">(Auto-populated from your login)</span>
                )}
                {isPartner && !canMakeChanges && (
                  <Lock className="inline h-3 w-3 ml-1 text-amber-600" />
                )}
              </Label>
              <Input
                {...register(`parties.${index}.name`)}
                onBlur={(e) => {
                  onBlur()
                  handleFieldChange(index, 'name', e.target.value)
                }}
                className={`border-gray-300 focus:border-blue-500 ${
                  index === 0 ? 'bg-blue-50 border-blue-200' : ''
                } ${
                  isPartner && !canMakeChanges ? 'bg-gray-50 cursor-not-allowed' : ''
                }`}
                disabled={isPartner && !canMakeChanges}
                title={
                  index === 0
                    ? "Organization name is automatically populated from your login"
                    : isPartner && !canMakeChanges
                    ? `Changes restricted until ${nextAllowedChangeDate?.toLocaleDateString()}`
                    : undefined
                }
                placeholder={index === 0 ? "Your organization name" : "Enter organization name"}
              />
              {errors.parties?.[index]?.name && (
                <p className="text-sm text-red-500">
                  {errors.parties[index]?.name?.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor={`parties.${index}.signatoryName`}>
                Signatory Name
                {index === 0 && (
                  <span className="text-xs text-blue-600 ml-2">(Auto-populated from your profile)</span>
                )}
              </Label>
              <Input
                {...register(`parties.${index}.signatoryName`)}
                onBlur={(e) => {
                  onBlur()
                  handleFieldChange(index, 'signatoryName', e.target.value)
                }}
                className="border-gray-300 focus:border-blue-500"
                placeholder={index === 0 ? "Your full name" : "Enter signatory name"}
              />
              {errors.parties?.[index]?.signatoryName && (
                <p className="text-sm text-red-500">
                  {errors.parties[index]?.signatoryName?.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor={`parties.${index}.position`}>Position</Label>
              <Input
                {...register(`parties.${index}.position`)}
                onBlur={(e) => {
                  onBlur()
                  handleFieldChange(index, 'position', e.target.value)
                }}
                className="border-gray-300 focus:border-blue-500"
              />
              {errors.parties?.[index]?.position && (
                <p className="text-sm text-red-500">
                  {errors.parties[index]?.position?.message}
                </p>
              )}
            </div>

            {/* Enhanced Party Details Section - Only show for Party 1 (index 0) */}
            {index === 0 && (
              <>
                {/* Responsibilities Section */}
                <div className="space-y-2 md:col-span-2">
                  <Label className="text-base font-semibold text-gray-900">
                    Responsibilities <span className="text-red-500">*</span>
                    <span className="text-xs font-normal text-gray-500 ml-2">(At least one required)</span>
                  </Label>

                  {/* Display existing responsibilities as cards */}
                  <div className="space-y-3">
                    {getPartyResponsibilities(index).length > 0 && (
                      <div className="grid gap-2">
                        {getPartyResponsibilities(index).map((responsibility, respIndex) => (
                          <div
                            key={respIndex}
                            className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg"
                          >
                            <span className="text-sm text-blue-900 flex-1">{responsibility}</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 hover:bg-red-100 text-red-500 hover:text-red-700"
                              onClick={() => removeResponsibility(index, respIndex)}
                              title="Remove responsibility"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Add new responsibility input */}
                    <div className="flex gap-2">
                      <Input
                        placeholder="e.g., Deliver Quality Care, Ensure Patient Safety, Coordinate with Insurers..."
                        value={getNewResponsibilityValue(index)}
                        onChange={(e) => handleResponsibilityInputChange(index, e.target.value)}
                        onKeyPress={(e) => handleResponsibilityKeyPress(e, index)}
                        className="border-gray-300 focus:border-blue-500"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => addResponsibility(index)}
                        disabled={!getNewResponsibilityValue(index)?.trim()}
                        className="shrink-0 bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>

                    {getPartyResponsibilities(index).length === 0 && (
                      <Alert className="border-amber-200 bg-amber-50">
                        <AlertTriangle className="h-4 w-4 text-amber-600" />
                        <AlertDescription className="text-amber-800">
                          <strong>Required:</strong> Please add at least one responsibility for the primary party.
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>

                  {errors.parties?.[index]?.responsibilities && (
                    <p className="text-sm text-red-500">
                      {errors.parties[index]?.responsibilities?.message}
                    </p>
                  )}
                </div>

                {/* Goal Section */}
                <div className="space-y-2 md:col-span-1">
                  <Label className="text-base font-semibold text-gray-900">
                    Primary Goal <span className="text-red-500">*</span>
                    <span className="text-xs font-normal text-gray-500 ml-2">(One main goal)</span>
                  </Label>

                  <div className="space-y-3">
                    {/* Display current goal */}
                    {getPartyGoal(index) ? (
                      <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-green-900 flex-1">{getPartyGoal(index)}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 hover:bg-red-100 text-red-500 hover:text-red-700"
                            onClick={() => removeGoal(index)}
                            title="Remove goal"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <Alert className="border-amber-200 bg-amber-50">
                        <AlertTriangle className="h-4 w-4 text-amber-600" />
                        <AlertDescription className="text-amber-800">
                          <strong>Required:</strong> Please set the primary goal for this party.
                        </AlertDescription>
                      </Alert>
                    )}

                    {/* Add/Edit goal input */}
                    <div className="flex gap-2">
                      <Input
                        placeholder="e.g., Improve Healthcare Access, Enhance Patient Outcomes, Strengthen Health Systems..."
                        value={getNewGoalValue(index)}
                        onChange={(e) => handleGoalInputChange(index, e.target.value)}
                        onKeyPress={(e) => handleGoalKeyPress(e, index)}
                        className="border-gray-300 focus:border-green-500"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setGoal(index)}
                        disabled={!getNewGoalValue(index)?.trim()}
                        className="shrink-0 bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
                      >
                        {getPartyGoal(index) ? <Edit className="h-4 w-4" /> : <Plus className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>

                  {errors.parties?.[index]?.goal && (
                    <p className="text-sm text-red-500">
                      {errors.parties[index]?.goal?.message}
                    </p>
                  )}
                </div>

                {/* Objectives Section */}
                <div className="space-y-2 md:col-span-2">
                  <Label className="text-base font-semibold text-gray-900">
                    Objectives <span className="text-red-500">*</span>
                    <span className="text-xs font-normal text-gray-500 ml-2">(At least one required)</span>
                  </Label>

                  {/* Display existing objectives as cards */}
                  <div className="space-y-3">
                    {getPartyObjectives(index).length > 0 && (
                      <div className="grid gap-2">
                        {getPartyObjectives(index).map((objective, objIndex) => (
                          <div
                            key={objIndex}
                            className="flex items-center justify-between p-3 bg-purple-50 border border-purple-200 rounded-lg"
                          >
                            <span className="text-sm text-purple-900 flex-1">{objective}</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 hover:bg-red-100 text-red-500 hover:text-red-700"
                              onClick={() => removeObjective(index, objIndex)}
                              title="Remove objective"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Add new objective input */}
                    <div className="flex gap-2">
                      <Input
                        placeholder="e.g., Reduce Patient Wait Times, Implement Digital Health Records, Train Healthcare Staff..."
                        value={getNewObjectiveValue(index)}
                        onChange={(e) => handleObjectiveInputChange(index, e.target.value)}
                        onKeyPress={(e) => handleObjectiveKeyPress(e, index)}
                        className="border-gray-300 focus:border-purple-500"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => addObjective(index)}
                        disabled={!getNewObjectiveValue(index)?.trim()}
                        className="shrink-0 bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>

                    {getPartyObjectives(index).length === 0 && (
                      <Alert className="border-amber-200 bg-amber-50">
                        <AlertTriangle className="h-4 w-4 text-amber-600" />
                        <AlertDescription className="text-amber-800">
                          <strong>Required:</strong> Please add at least one objective for the primary party.
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>

                  {errors.parties?.[index]?.objectives && (
                    <p className="text-sm text-red-500">
                      {errors.parties[index]?.objectives?.message}
                    </p>
                  )}
                </div>
              </>
            )}

            {/* Information message for Party 2 and beyond */}
            {index >= 1 && (
              <div className={index === 0 ? "md:col-span-1" : "md:col-span-3"}>
                <Alert className="border-blue-200 bg-blue-50">
                  <Info className="h-4 w-4 text-blue-600" />
                  <AlertDescription className="text-blue-800">
                    <div className="space-y-1">
                      <p className="font-semibold text-sm">
                        {index === 1 ? 'Secondary Party' : `Party ${index + 1}`} - Simplified Requirements
                      </p>
                      <p className="text-xs">
                        For {index === 1 ? 'the second party' : `party ${index + 1}`}, only basic information is required:
                        <strong> Organization Name, Signatory Name, and Position</strong>.
                      </p>
                      <p className="text-xs text-blue-600">
                        📋 Responsibilities and detailed terms are managed by the Primary Party (Party 1).
                      </p>
                    </div>
                  </AlertDescription>
                </Alert>
              </div>
            )}
          </div>
        </Card>
      ))}

      {/* Add Party Button - Show based on organization rules */}
      <div className="flex justify-center">
        <div className="w-full max-w-md space-y-3">
          {/* Add Party Button */}
          <Button
            type="button"
            variant="outline"
            className={`w-full shadow-md ${
              // Show as enabled if user can add more parties
              (!isPartner || canHaveMultipleParties) && parties.length < (!isPartner ? 10 : 2) && (!isPartner || canMakeChanges)
                ? 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100 hover:border-green-300'
                : 'bg-gray-50 border-gray-200 text-gray-400 cursor-not-allowed'
            }`}
            onClick={handleAddParty}
            disabled={
              // Disable if: partner with single party limit, or at max parties, or change restricted
              (isPartner && !canHaveMultipleParties) ||
              parties.length >= (!isPartner ? 10 : (canHaveMultipleParties ? 2 : 1)) ||
              (isPartner && !canMakeChanges)
            }
            title={
              isPartner && !canHaveMultipleParties
                ? "Your organization is limited to one party. Use 'Request Second Party Access' if needed."
                : parties.length >= (!isPartner ? 10 : (canHaveMultipleParties ? 2 : 1))
                ? !isPartner
                  ? "Maximum 10 parties allowed"
                  : "Maximum parties reached for your organization"
                : isPartner && !canMakeChanges
                ? `Changes restricted until ${nextAllowedChangeDate?.toLocaleDateString()}`
                : "Add another party to the MoU"
            }
          >
            <Plus className="h-4 w-4 mr-2" />
            Add {parties.length === 0 ? 'Party' : 'Another Party'}
            {isPartner && !canMakeChanges && (
              <Lock className="h-3 w-3 ml-2" />
            )}
          </Button>

          {/* Organization-specific information */}
          {isPartner && parties.length > 0 && (
            <Alert className="border-blue-200 bg-blue-50">
              <Info className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800 text-center">
                {canHaveMultipleParties ? (
                  <div>
                    <strong>Special Organization:</strong> Your organization can have up to 2 parties.
                    {parties.length >= 2 && (
                      <div className="mt-1 text-sm">Maximum parties reached for your organization.</div>
                    )}
                  </div>
                ) : (
                  <div>
                    <strong>Standard Organization:</strong> One party is sufficient for your MoU.
                    <div className="mt-3 flex justify-center">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleContactAdmin}
                        className="bg-white border-blue-300 text-blue-700 hover:bg-blue-50"
                      >
                        <Mail className="h-4 w-4 mr-2" />
                        Request Second Party Access
                      </Button>
                    </div>
                    <div className="mt-2 text-xs text-blue-600">
                      Click above to email admin for special circumstances requiring a second party.
                    </div>
                  </div>
                )}
                {!canMakeChanges && (
                  <div className="mt-2 text-sm border-t border-blue-200 pt-2">
                    Changes are restricted until <strong>{nextAllowedChangeDate?.toLocaleDateString()}</strong>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}
        </div>
      </div>
    </div>
  )
}
