import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class CreateDocumentTypeDto {
  @ApiProperty({
    description: 'Document type name',
    example: 'Strategic Plan',
  })
  @IsString()
  @IsNotEmpty()
  typeName: string;
}

export class UpdateDocumentTypeDto {
  @ApiProperty({
    description: 'Document type name',
    example: 'Updated Strategic Plan',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  typeName?: string;
}

export class DocumentTypeResponseDto {
  @ApiProperty({ description: 'Document type ID', example: 1 })
  id: number;

  @ApiProperty({ description: 'Document type name', example: 'Strategic Plan' })
  typeName: string;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;

  @ApiProperty({ description: 'Soft delete flag' })
  deleted: boolean;
}
