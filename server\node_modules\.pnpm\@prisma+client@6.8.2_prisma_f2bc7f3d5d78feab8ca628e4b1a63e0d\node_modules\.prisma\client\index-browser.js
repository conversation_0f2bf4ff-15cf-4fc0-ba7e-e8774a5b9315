
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.CurrencyScalarFieldEnum = {
  id: 'id',
  currencyCode: 'currencyCode',
  currencyName: 'currencyName',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.DocumentTypeScalarFieldEnum = {
  id: 'id',
  typeName: 'typeName',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.BudgetTypeScalarFieldEnum = {
  id: 'id',
  typeName: 'typeName',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.FundingUnitScalarFieldEnum = {
  id: 'id',
  unitName: 'unitName',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.FundingSourceScalarFieldEnum = {
  id: 'id',
  sourceName: 'sourceName',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.FinancingAgentScalarFieldEnum = {
  id: 'id',
  agentName: 'agentName',
  description: 'description',
  contactInfo: 'contactInfo',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.FinancingSchemeScalarFieldEnum = {
  id: 'id',
  schemeName: 'schemeName',
  description: 'description',
  terms: 'terms',
  conditions: 'conditions',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.HealthCareProviderScalarFieldEnum = {
  id: 'id',
  providerName: 'providerName',
  description: 'description',
  location: 'location',
  contactEmail: 'contactEmail',
  contactPhone: 'contactPhone',
  website: 'website',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.InputCategoryScalarFieldEnum = {
  id: 'id',
  categoryName: 'categoryName',
  description: 'description',
  parentId: 'parentId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.DomainInterventionScalarFieldEnum = {
  id: 'id',
  domainName: 'domainName',
  description: 'description',
  parentId: 'parentId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted',
  userId: 'userId'
};

exports.Prisma.OrganizationTypeScalarFieldEnum = {
  id: 'id',
  typeName: 'typeName',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.CentralLevelInstitutionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.OrganizationScalarFieldEnum = {
  id: 'id',
  organizationName: 'organizationName',
  organizationPhoneNumber: 'organizationPhoneNumber',
  organizationEmail: 'organizationEmail',
  organizationWebsite: 'organizationWebsite',
  homeCountryRepresentative: 'homeCountryRepresentative',
  rwandaRepresentative: 'rwandaRepresentative',
  organizationRgbNumber: 'organizationRgbNumber',
  organizationTypeId: 'organizationTypeId',
  centralLevelInstitutionId: 'centralLevelInstitutionId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.AddressScalarFieldEnum = {
  id: 'id',
  addressType: 'addressType',
  country: 'country',
  province: 'province',
  district: 'district',
  sector: 'sector',
  cell: 'cell',
  street: 'street',
  poBox: 'poBox',
  postalCode: 'postalCode',
  organizationId: 'organizationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  password: 'password',
  emailVerified: 'emailVerified',
  verifiedAt: 'verifiedAt',
  verificationToken: 'verificationToken',
  verificationTokenExpiryTime: 'verificationTokenExpiryTime',
  refreshToken: 'refreshToken',
  passwordResetToken: 'passwordResetToken',
  passwordResetExpires: 'passwordResetExpires',
  invitationToken: 'invitationToken',
  invitationExpires: 'invitationExpires',
  invitedBy: 'invitedBy',
  role: 'role',
  isActive: 'isActive',
  organizationId: 'organizationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.ProjectScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  duration: 'duration',
  budgetTypeId: 'budgetTypeId',
  fundingUnitId: 'fundingUnitId',
  fundingSourceId: 'fundingSourceId',
  organizationId: 'organizationId',
  mouApplicationId: 'mouApplicationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.DocumentScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  organizationId: 'organizationId',
  documentTypeId: 'documentTypeId',
  projectId: 'projectId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.ProvinceScalarFieldEnum = {
  id: 'id',
  name: 'name',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.ActivityProvinceScalarFieldEnum = {
  id: 'id',
  activityId: 'activityId',
  provinceId: 'provinceId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ActivityCentralLevelInstitutionScalarFieldEnum = {
  id: 'id',
  activityId: 'activityId',
  centralLevelInstitutionId: 'centralLevelInstitutionId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ActivityScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  projectId: 'projectId',
  startDate: 'startDate',
  endDate: 'endDate',
  implementer: 'implementer',
  implementerUnit: 'implementerUnit',
  fiscalYear: 'fiscalYear',
  domainInterventionId: 'domainInterventionId',
  inputId: 'inputId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.InputScalarFieldEnum = {
  id: 'id',
  name: 'name',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.InputSubclassScalarFieldEnum = {
  id: 'id',
  subclassId: 'subclassId',
  name: 'name',
  budget: 'budget',
  inputId: 'inputId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.PartyScalarFieldEnum = {
  id: 'id',
  partyId: 'partyId',
  name: 'name',
  responsibilityId: 'responsibilityId',
  organizationId: 'organizationId',
  objectiveId: 'objectiveId',
  goalId: 'goalId',
  signatory: 'signatory',
  position: 'position',
  duration: 'duration',
  reasonForExtendedDuration: 'reasonForExtendedDuration',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.ResponsibilityScalarFieldEnum = {
  id: 'id',
  name: 'name',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.ObjectiveScalarFieldEnum = {
  id: 'id',
  name: 'name',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.GoalScalarFieldEnum = {
  id: 'id',
  name: 'name',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.MouScalarFieldEnum = {
  id: 'id',
  mouKey: 'mouKey',
  partyId: 'partyId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.MouApplicationScalarFieldEnum = {
  id: 'id',
  applicationKey: 'applicationKey',
  mouId: 'mouId',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.ApprovalStepScalarFieldEnum = {
  id: 'id',
  mouApplicationId: 'mouApplicationId',
  reviewerId: 'reviewerId',
  projectId: 'projectId',
  status: 'status',
  comment: 'comment',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.DraftMouApplicationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  organizationId: 'organizationId',
  currentStep: 'currentStep',
  mouDetails: 'mouDetails',
  parties: 'parties',
  projects: 'projects',
  activities: 'activities',
  documents: 'documents',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.ActivityLogScalarFieldEnum = {
  id: 'id',
  action: 'action',
  details: 'details',
  category: 'category',
  importance: 'importance',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  mouApplicationId: 'mouApplicationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.DomainFunctionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  subDomainInterventionId: 'subDomainInterventionId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.SubDomainFunctionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  domainFunctionId: 'domainFunctionId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.SubDomainInterventionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  domainInterventionId: 'domainInterventionId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  deleted: 'deleted'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.AddressType = exports.$Enums.AddressType = {
  HEADQUARTERS: 'HEADQUARTERS',
  RWANDA: 'RWANDA'
};

exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  PARTNER: 'PARTNER',
  PARTNER_COORDINATOR: 'PARTNER_COORDINATOR',
  TECHNICAL_EXPERT: 'TECHNICAL_EXPERT',
  LEGAL_OFFICER: 'LEGAL_OFFICER',
  HEAD_OF_DEPARTMENT: 'HEAD_OF_DEPARTMENT',
  PermanentSecretary: 'PermanentSecretary',
  MINISTER: 'MINISTER'
};

exports.ApprovalStatusType = exports.$Enums.ApprovalStatusType = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  MODIFICATIONS_REQUESTED: 'MODIFICATIONS_REQUESTED'
};

exports.Prisma.ModelName = {
  Currency: 'Currency',
  DocumentType: 'DocumentType',
  BudgetType: 'BudgetType',
  FundingUnit: 'FundingUnit',
  FundingSource: 'FundingSource',
  FinancingAgent: 'FinancingAgent',
  FinancingScheme: 'FinancingScheme',
  HealthCareProvider: 'HealthCareProvider',
  InputCategory: 'InputCategory',
  DomainIntervention: 'DomainIntervention',
  OrganizationType: 'OrganizationType',
  CentralLevelInstitution: 'CentralLevelInstitution',
  Organization: 'Organization',
  Address: 'Address',
  User: 'User',
  Project: 'Project',
  Document: 'Document',
  Province: 'Province',
  ActivityProvince: 'ActivityProvince',
  ActivityCentralLevelInstitution: 'ActivityCentralLevelInstitution',
  Activity: 'Activity',
  Input: 'Input',
  InputSubclass: 'InputSubclass',
  Party: 'Party',
  Responsibility: 'Responsibility',
  Objective: 'Objective',
  Goal: 'Goal',
  Mou: 'Mou',
  MouApplication: 'MouApplication',
  ApprovalStep: 'ApprovalStep',
  DraftMouApplication: 'DraftMouApplication',
  ActivityLog: 'ActivityLog',
  DomainFunction: 'DomainFunction',
  SubDomainFunction: 'SubDomainFunction',
  SubDomainIntervention: 'SubDomainIntervention'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
