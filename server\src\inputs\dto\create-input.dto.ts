import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsInt, IsPositive } from 'class-validator';

export class CreateInputDto {
  @ApiProperty({
    description: 'Input name',
    example: 'Medical Equipment',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Input subclass ID',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  inputSubclassId: number;
}

export class UpdateInputDto {
  @ApiProperty({
    description: 'Input name',
    example: 'Updated Medical Equipment',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiProperty({
    description: 'Input subclass ID',
    example: 2,
    required: false,
  })
  @IsInt()
  @IsPositive()
  inputSubclassId?: number;
}

export class InputResponseDto {
  @ApiProperty({ description: 'Input ID', example: 1 })
  id: number;

  @ApiProperty({ description: 'Input name', example: 'Medical Equipment' })
  name: string;

  @ApiProperty({ description: 'Input subclass ID', example: 1 })
  inputSubclassId: number;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;

  @ApiProperty({ description: 'Soft delete flag' })
  deleted: boolean;
}

export class InputWithRelationsDto extends InputResponseDto {
  @ApiProperty({ description: 'Input subclass information' })
  inputSubclass?: {
    id: number;
    subclassId: number;
    name: string;
    budget: number;
  };

  @ApiProperty({ description: 'Associated activities' })
  activities?: Array<{
    id: number;
    name: string;
    description?: string;
    project: {
      id: number;
      name: string;
    };
  }>;
}

// Input Subclass DTOs
export class CreateInputSubclassDto {
  @ApiProperty({
    description: 'Subclass ID',
    example: 101,
  })
  @IsInt()
  @IsPositive()
  subclassId: number;

  @ApiProperty({
    description: 'Subclass name',
    example: 'Diagnostic Equipment',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Budget amount',
    example: 50000.00,
  })
  @IsPositive()
  budget: number;
}

export class UpdateInputSubclassDto {
  @ApiProperty({
    description: 'Subclass name',
    example: 'Updated Diagnostic Equipment',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiProperty({
    description: 'Budget amount',
    example: 75000.00,
    required: false,
  })
  @IsPositive()
  budget?: number;
}

export class InputSubclassResponseDto {
  @ApiProperty({ description: 'Input subclass ID', example: 1 })
  id: number;

  @ApiProperty({ description: 'Subclass ID', example: 101 })
  subclassId: number;

  @ApiProperty({ description: 'Subclass name', example: 'Diagnostic Equipment' })
  name: string;

  @ApiProperty({ description: 'Budget amount', example: 50000.00 })
  budget: number;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;

  @ApiProperty({ description: 'Soft delete flag' })
  deleted: boolean;
}

export class InputSubclassWithRelationsDto extends InputSubclassResponseDto {
  @ApiProperty({ description: 'Associated inputs' })
  inputs?: Array<{
    id: number;
    name: string;
  }>;
}
