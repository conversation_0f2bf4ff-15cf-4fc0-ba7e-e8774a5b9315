"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartyWithRelationsDto = exports.PartyResponseDto = exports.UpdatePartyDto = exports.CreatePartyDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreatePartyDto {
    constructor() {
        this.duration = 1;
    }
}
exports.CreatePartyDto = CreatePartyDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique party identifier',
        example: 'PARTY-001-2024',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreatePartyDto.prototype, "partyId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Party name',
        example: 'Ministry of Health',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreatePartyDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Responsibility ID',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreatePartyDto.prototype, "responsibilityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Organization ID',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreatePartyDto.prototype, "organizationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Objective ID',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreatePartyDto.prototype, "objectiveId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Goal ID',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    __metadata("design:type", Number)
], CreatePartyDto.prototype, "goalId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Signatory name',
        example: 'Dr. John Doe',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreatePartyDto.prototype, "signatory", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Signatory position',
        example: 'Director General',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreatePartyDto.prototype, "position", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Duration in years',
        example: 1,
        default: 1,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(10),
    __metadata("design:type", Number)
], CreatePartyDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Reason for extended duration (required if duration > 1)',
        example: 'Long-term strategic partnership requires extended commitment',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreatePartyDto.prototype, "reasonForExtendedDuration", void 0);
class UpdatePartyDto {
}
exports.UpdatePartyDto = UpdatePartyDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Party name',
        example: 'Updated Ministry of Health',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdatePartyDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Responsibility ID',
        example: 2,
        required: false,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdatePartyDto.prototype, "responsibilityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Objective ID',
        example: 2,
        required: false,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdatePartyDto.prototype, "objectiveId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Goal ID',
        example: 2,
        required: false,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsPositive)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdatePartyDto.prototype, "goalId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Signatory name',
        example: 'Dr. Jane Smith',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdatePartyDto.prototype, "signatory", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Signatory position',
        example: 'Deputy Director General',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdatePartyDto.prototype, "position", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Duration in years',
        example: 2,
        required: false,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(10),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdatePartyDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Reason for extended duration',
        example: 'Updated justification for extended partnership',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdatePartyDto.prototype, "reasonForExtendedDuration", void 0);
class PartyResponseDto {
}
exports.PartyResponseDto = PartyResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Party ID', example: 1 }),
    __metadata("design:type", Number)
], PartyResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Party identifier', example: 'PARTY-001-2024' }),
    __metadata("design:type", String)
], PartyResponseDto.prototype, "partyId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Party name', example: 'Ministry of Health' }),
    __metadata("design:type", String)
], PartyResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Responsibility ID', example: 1 }),
    __metadata("design:type", Number)
], PartyResponseDto.prototype, "responsibilityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Organization ID', example: 1 }),
    __metadata("design:type", Number)
], PartyResponseDto.prototype, "organizationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Objective ID', example: 1 }),
    __metadata("design:type", Number)
], PartyResponseDto.prototype, "objectiveId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Goal ID', example: 1 }),
    __metadata("design:type", Number)
], PartyResponseDto.prototype, "goalId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Signatory name', example: 'Dr. John Doe' }),
    __metadata("design:type", String)
], PartyResponseDto.prototype, "signatory", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Signatory position', example: 'Director General' }),
    __metadata("design:type", String)
], PartyResponseDto.prototype, "position", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Duration in years', example: 1 }),
    __metadata("design:type", Number)
], PartyResponseDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Reason for extended duration', required: false }),
    __metadata("design:type", String)
], PartyResponseDto.prototype, "reasonForExtendedDuration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation date' }),
    __metadata("design:type", Date)
], PartyResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update date' }),
    __metadata("design:type", Date)
], PartyResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Soft delete flag' }),
    __metadata("design:type", Boolean)
], PartyResponseDto.prototype, "deleted", void 0);
class PartyWithRelationsDto extends PartyResponseDto {
}
exports.PartyWithRelationsDto = PartyWithRelationsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Organization information' }),
    __metadata("design:type", Object)
], PartyWithRelationsDto.prototype, "organization", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Responsibility information' }),
    __metadata("design:type", Object)
], PartyWithRelationsDto.prototype, "responsibility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Objective information' }),
    __metadata("design:type", Object)
], PartyWithRelationsDto.prototype, "objective", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Goal information' }),
    __metadata("design:type", Object)
], PartyWithRelationsDto.prototype, "goal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Associated MoU information' }),
    __metadata("design:type", Object)
], PartyWithRelationsDto.prototype, "mou", void 0);
//# sourceMappingURL=create-party.dto.js.map