"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivitiesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const activities_service_1 = require("./activities.service");
const create_activity_dto_1 = require("./dto/create-activity.dto");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
const roles_guard_1 = require("../auth/guard/roles.guard");
const roles_decorator_1 = require("../auth/decorator/roles.decorator");
const dto_1 = require("../auth/dto");
let ActivitiesController = class ActivitiesController {
    constructor(activitiesService) {
        this.activitiesService = activitiesService;
    }
    async create(createActivityDto) {
        return this.activitiesService.create(createActivityDto);
    }
    async findAll(page, limit, projectId, fiscalYear) {
        const pageNum = page ? parseInt(page, 10) : 1;
        const limitNum = limit ? parseInt(limit, 10) : 10;
        const projectIdNum = projectId ? parseInt(projectId, 10) : undefined;
        const fiscalYearNum = fiscalYear ? parseInt(fiscalYear, 10) : undefined;
        return this.activitiesService.findAll(pageNum, limitNum, projectIdNum, fiscalYearNum);
    }
    async getActivitiesByProject(projectId) {
        return this.activitiesService.getActivitiesByProject(projectId);
    }
    async getActivitiesByFiscalYear(fiscalYear) {
        return this.activitiesService.getActivitiesByFiscalYear(fiscalYear);
    }
    async findOne(id) {
        return this.activitiesService.findOne(id);
    }
    async update(id, updateActivityDto) {
        return this.activitiesService.update(id, updateActivityDto);
    }
    async remove(id) {
        return this.activitiesService.remove(id);
    }
};
exports.ActivitiesController = ActivitiesController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new activity' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Activity created successfully',
        type: create_activity_dto_1.ActivityWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Related entity not found' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_activity_dto_1.CreateActivityDto]),
    __metadata("design:returntype", Promise)
], ActivitiesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get all activities with pagination' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10)' }),
    (0, swagger_1.ApiQuery)({ name: 'projectId', required: false, type: Number, description: 'Filter by project ID' }),
    (0, swagger_1.ApiQuery)({ name: 'fiscalYear', required: false, type: Number, description: 'Filter by fiscal year' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of activities',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/ActivityWithRelationsDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('projectId')),
    __param(3, (0, common_1.Query)('fiscalYear')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String]),
    __metadata("design:returntype", Promise)
], ActivitiesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('by-project/:projectId'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get activities by project ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of activities for the project',
        type: [create_activity_dto_1.ActivityWithRelationsDto],
    }),
    __param(0, (0, common_1.Param)('projectId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ActivitiesController.prototype, "getActivitiesByProject", null);
__decorate([
    (0, common_1.Get)('by-fiscal-year/:fiscalYear'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get activities by fiscal year' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of activities for the fiscal year',
        type: [create_activity_dto_1.ActivityWithRelationsDto],
    }),
    __param(0, (0, common_1.Param)('fiscalYear', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ActivitiesController.prototype, "getActivitiesByFiscalYear", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get an activity by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Activity details',
        type: create_activity_dto_1.ActivityWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Activity not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ActivitiesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Update an activity' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Activity updated successfully',
        type: create_activity_dto_1.ActivityWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Activity not found' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, create_activity_dto_1.UpdateActivityDto]),
    __metadata("design:returntype", Promise)
], ActivitiesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Delete an activity (soft delete)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Activity deleted successfully',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Activity not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ActivitiesController.prototype, "remove", null);
exports.ActivitiesController = ActivitiesController = __decorate([
    (0, swagger_1.ApiTags)('Activities'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('activities'),
    __metadata("design:paramtypes", [activities_service_1.ActivitiesService])
], ActivitiesController);
//# sourceMappingURL=activities.controller.js.map