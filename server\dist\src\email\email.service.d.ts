import { MailerService } from "@nestjs-modules/mailer";
import { ConfigService } from "@nestjs/config";
export declare class EmailService {
    private readonly mailerService;
    private configService;
    private readonly logger;
    constructor(mailerService: MailerService, configService: ConfigService);
    private getBaseContext;
    sendWelcomeEmail(user: {
        email: string;
        firstName: string;
        lastName: string;
    }): Promise<void>;
    sendEmailVerificationEmail(user: {
        email: string;
        firstName: string;
        lastName: string;
        verificationToken: string;
        tempPassword?: string;
    }): Promise<void>;
    sendPasswordResetEmail(user: {
        email: string;
        firstName: string;
        lastName: string;
        resetToken: string;
    }): Promise<void>;
    sendInvitationEmail(invitation: {
        email: string;
        invitationToken: string;
        inviterName: string;
        companyName: string;
        companyDescription?: string;
        role: string;
    }): Promise<void>;
    sendHostBookingNotification(booking: {
        hostEmail: string;
        hostName: string;
        propertyName: string;
        bookingId: string;
        checkIn: string;
        checkOut: string;
        guests: number;
        totalPayout: number;
        guestName: string;
        guestReviews?: string;
        reviewCount?: number;
        guestMessage?: string;
    }): Promise<void>;
    sendBookingApprovalNotification(booking: {
        guestEmail: string;
        guestName: string;
        propertyName: string;
        propertyAddress: string;
        propertyImageUrl?: string;
        bookingId: string;
        checkIn: string;
        checkOut: string;
        guests: number;
        totalAmount: number;
        hostName?: string;
        hostMessage?: string;
    }): Promise<void>;
    sendBookingRejectionNotification(booking: {
        guestEmail: string;
        guestName: string;
        propertyName: string;
        bookingId: string;
        checkIn: string;
        checkOut: string;
        guests: number;
        hostMessage?: string;
    }): Promise<void>;
}
