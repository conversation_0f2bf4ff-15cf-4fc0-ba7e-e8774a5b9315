import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsEnum, IsOptional, IsUUID } from 'class-validator';

export enum AddressType {
    HEADQUARTERS = 'HEADQUARTERS',
    RWANDA = 'RWANDA'
}

export class CreateAddressDto {
    @ApiProperty({ enum: AddressType, description: 'Type of address (HEADQUARTERS or RWANDA)' })
    @IsNotEmpty()
    @IsEnum(AddressType)
    addressType: AddressType;

    @ApiProperty({ description: 'Country name' })
    @IsNotEmpty()
    @IsString()
    country: string;

    @ApiProperty({ required: false, description: 'Province/State (optional for headquarters addresses)' })
    @IsOptional()
    @IsString()
    province?: string;

    @ApiProperty({ required: false, description: 'District (optional for headquarters addresses)' })
    @IsOptional()
    @IsString()
    district?: string;

    @ApiProperty({ required: false, description: 'Sector (Rwanda-specific administrative division)' })
    @IsOptional()
    @IsString()
    sector?: string;

    @ApiProperty({ required: false, description: 'Cell (Rwanda-specific administrative division)' })
    @IsOptional()
    @IsString()
    cell?: string;

    @ApiProperty({ required: false, description: 'Village (Rwanda-specific administrative division)' })
    @IsOptional()
    @IsString()
    village?: string;

    @ApiProperty({ description: 'Street address' })
    @IsNotEmpty()
    @IsString()
    street: string;

    @ApiProperty({ required: false, description: 'Avenue' })
    @IsOptional()
    @IsString()
    avenue?: string;

    @ApiProperty({ description: 'P.O. Box' })
    @IsNotEmpty()
    @IsString()
    poBox: string;

    @ApiProperty({ required: false, description: 'Postal code' })
    @IsOptional()
    @IsString()
    postalCode?: string;

    @ApiProperty({ description: 'Organization ID this address belongs to' })
    @IsNotEmpty()
    @IsUUID()
    organizationId: string;
}

export class UpdateAddressDto {
    @ApiProperty({ enum: AddressType, required: false, description: 'Type of address (HEADQUARTERS or RWANDA)' })
    @IsOptional()
    @IsEnum(AddressType)
    addressType?: AddressType;

    @ApiProperty({ required: false, description: 'Country name' })
    @IsOptional()
    @IsString()
    country?: string;

    @ApiProperty({ required: false, description: 'Province/State' })
    @IsOptional()
    @IsString()
    province?: string;

    @ApiProperty({ required: false, description: 'District' })
    @IsOptional()
    @IsString()
    district?: string;

    @ApiProperty({ required: false, description: 'Sector (Rwanda-specific administrative division)' })
    @IsOptional()
    @IsString()
    sector?: string;

    @ApiProperty({ required: false, description: 'Cell (Rwanda-specific administrative division)' })
    @IsOptional()
    @IsString()
    cell?: string;

    @ApiProperty({ required: false, description: 'Village (Rwanda-specific administrative division)' })
    @IsOptional()
    @IsString()
    village?: string;

    @ApiProperty({ required: false, description: 'Street address' })
    @IsOptional()
    @IsString()
    street?: string;

    @ApiProperty({ required: false, description: 'Avenue' })
    @IsOptional()
    @IsString()
    avenue?: string;

    @ApiProperty({ required: false, description: 'P.O. Box' })
    @IsOptional()
    @IsString()
    poBox?: string;

    @ApiProperty({ required: false, description: 'Postal code' })
    @IsOptional()
    @IsString()
    postalCode?: string;
}

export class AddressResponseDto {
    @ApiProperty()
    id: string;

    @ApiProperty({ enum: AddressType })
    addressType: AddressType;

    @ApiProperty()
    country: string;

    @ApiProperty({ required: false })
    province?: string;

    @ApiProperty({ required: false })
    district?: string;

    @ApiProperty({ required: false })
    sector?: string;

    @ApiProperty({ required: false })
    cell?: string;

    @ApiProperty({ required: false })
    village?: string;

    @ApiProperty()
    street: string;

    @ApiProperty({ required: false })
    avenue?: string;

    @ApiProperty()
    poBox: string;

    @ApiProperty({ required: false })
    postalCode?: string;

    @ApiProperty()
    organizationId: string;

    @ApiProperty()
    createdAt: Date;

    @ApiProperty()
    updatedAt: Date;

    @ApiProperty()
    deleted: boolean;
}
