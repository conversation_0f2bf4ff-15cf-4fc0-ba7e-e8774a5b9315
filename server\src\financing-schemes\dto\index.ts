import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class CreateFinancingSchemeDto {
    @ApiProperty({ description: 'The name of the financing scheme' })
    @IsNotEmpty()
    @IsString()
    schemeName: string;

    @ApiProperty({ description: 'Description of the financing scheme', required: false })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'Terms of the financing scheme', required: false })
    @IsOptional()
    @IsString()
    terms?: string;

    @ApiProperty({ description: 'Conditions of the financing scheme', required: false })
    @IsOptional()
    @IsString()
    conditions?: string;
}

export class UpdateFinancingSchemeDto {
    @ApiProperty({ description: 'The name of the financing scheme', required: false })
    @IsOptional()
    @IsString()
    schemeName?: string;

    @ApiProperty({ description: 'Description of the financing scheme', required: false })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'Terms of the financing scheme', required: false })
    @IsOptional()
    @IsString()
    terms?: string;

    @ApiProperty({ description: 'Conditions of the financing scheme', required: false })
    @IsOptional()
    @IsString()
    conditions?: string;
}

export class FinancingSchemeResponseDto {
    @ApiProperty()
    id: number;

    @ApiProperty({ description: 'The name of the financing scheme' })
    schemeName: string;

    @ApiProperty({ description: 'Description of the financing scheme' })
    description?: string;

    @ApiProperty({ description: 'Terms of the financing scheme' })
    terms?: string;

    @ApiProperty({ description: 'Conditions of the financing scheme' })
    conditions?: string;

    @ApiProperty({ description: 'When the financing scheme was created' })
    createdAt: Date;

    @ApiProperty({ description: 'When the financing scheme was last updated' })
    updatedAt: Date;

    @ApiProperty({ description: 'Whether the financing scheme has been soft deleted' })
    deleted: boolean;
}
