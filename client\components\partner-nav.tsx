"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import {
  FileText,
  Plus,
  Home,
  Building,
  User,
  Settings,
  BarChart3,
  PlusCircle,
  Building2,
  UserCircle
} from "lucide-react"

const partnerNavItems = [
  // Overview
  {
    title: "Partner Dashboard",
    subtitle: "Overview & Analytics",
    href: "/dashboard/partner",
    icon: BarChart3,
    color: "blue",
  },
  // MoU Management
  {
    title: "My Applications",
    subtitle: "View & Manage",
    href: "/dashboard/partner/applications",
    icon: FileText,
    color: "green",
    badge: "3",
  },
  {
    title: "New Application",
    subtitle: "Create MoU",
    href: "/dashboard/partner/applications/new",
    icon: PlusCircle,
    color: "purple",
  },
  // User Configuration
  {
    title: "Organization",
    subtitle: "Setup & Config",
    href: "/dashboard/partner/organization",
    icon: Building2,
    color: "orange",
  },
  {
    title: "My Profile",
    subtitle: "Account Settings",
    href: "/dashboard/profile",
    icon: UserCircle,
    color: "gray",
  },
]

export function PartnerNav() {
  const pathname = usePathname()

  const getColorClasses = (color: string, isActive: boolean) => {
    const colors = {
      blue: isActive ? "bg-blue-100" : "bg-blue-50",
      green: isActive ? "bg-green-100" : "bg-green-50",
      purple: isActive ? "bg-purple-100" : "bg-purple-50",
      orange: isActive ? "bg-orange-100" : "bg-orange-50",
      gray: isActive ? "bg-gray-100" : "bg-gray-50",
    }

    const iconColors = {
      blue: isActive ? "text-blue-700" : "text-blue-600",
      green: isActive ? "text-green-700" : "text-green-600",
      purple: isActive ? "text-purple-700" : "text-purple-600",
      orange: isActive ? "text-orange-700" : "text-orange-600",
      gray: isActive ? "text-gray-700" : "text-gray-600",
    }

    return {
      bg: colors[color as keyof typeof colors] || colors.gray,
      icon: iconColors[color as keyof typeof iconColors] || iconColors.gray,
    }
  }

  return (
    <nav className="flex flex-col gap-4">
      {partnerNavItems.map((item) => {
        const Icon = item.icon
        const isActive = pathname === item.href ||
          (item.href !== "/dashboard/partner" && pathname.startsWith(item.href))

        const colorClasses = getColorClasses(item.color, isActive)

        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "group flex items-center gap-4 rounded-xl p-4 transition-all duration-200 hover:shadow-lg",
              isActive
                ? "bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg"
                : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-100 hover:border-gray-200"
            )}
          >
            <div className={cn(
              "flex h-12 w-12 items-center justify-center rounded-xl transition-colors",
              isActive ? "bg-white/20" : colorClasses.bg
            )}>
              <Icon className={cn(
                "h-6 w-6 transition-colors",
                isActive ? "text-white" : colorClasses.icon
              )} />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <span className={cn(
                  "font-bold text-base truncate",
                  isActive ? "text-white" : "text-gray-900"
                )}>
                  {item.title}
                </span>
                {item.badge && (
                  <Badge
                    variant="secondary"
                    className={cn(
                      "text-xs px-2 py-1 font-semibold",
                      isActive
                        ? "bg-white/20 text-white"
                        : "bg-blue-100 text-blue-700"
                    )}
                  >
                    {item.badge}
                  </Badge>
                )}
              </div>
              <p className={cn(
                "text-sm truncate font-medium",
                isActive ? "text-blue-100" : "text-gray-500"
              )}>
                {item.subtitle}
              </p>
            </div>
          </Link>
        )
      })}
    </nav>
  )
}
