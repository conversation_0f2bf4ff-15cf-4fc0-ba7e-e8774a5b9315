"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var HealthCareProvidersService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthCareProvidersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let HealthCareProvidersService = HealthCareProvidersService_1 = class HealthCareProvidersService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(HealthCareProvidersService_1.name);
    }
    async create(createHealthCareProviderDto, userId) {
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can create healthcare providers');
            }
            const existing = await this.prisma.healthCareProvider.findFirst({
                where: {
                    providerName: createHealthCareProviderDto.providerName,
                    deleted: false
                }
            });
            if (existing) {
                throw new common_1.ConflictException('Healthcare provider with this name already exists');
            }
            const provider = await this.prisma.healthCareProvider.create({
                data: {
                    providerName: createHealthCareProviderDto.providerName,
                    description: createHealthCareProviderDto.description,
                    location: createHealthCareProviderDto.location,
                    contactEmail: createHealthCareProviderDto.contactEmail,
                    contactPhone: createHealthCareProviderDto.contactPhone,
                    website: createHealthCareProviderDto.website
                }
            });
            return provider;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to create healthcare provider', error.stack);
            throw new Error('Failed to create healthcare provider');
        }
    }
    async findAll() {
        return this.prisma.healthCareProvider.findMany({
            where: {
                deleted: false
            },
            orderBy: {
                providerName: 'asc'
            }
        });
    }
    async findOne(id) {
        const provider = await this.prisma.healthCareProvider.findFirst({
            where: {
                id,
                deleted: false
            }
        });
        if (!provider) {
            throw new common_1.NotFoundException('Healthcare provider not found');
        }
        return provider;
    }
    async update(id, updateHealthCareProviderDto, userId) {
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can update healthcare providers');
            }
            const existing = await this.prisma.healthCareProvider.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });
            if (!existing) {
                throw new common_1.NotFoundException('Healthcare provider not found');
            }
            if (updateHealthCareProviderDto.providerName &&
                updateHealthCareProviderDto.providerName !== existing.providerName) {
                const nameConflict = await this.prisma.healthCareProvider.findFirst({
                    where: {
                        providerName: updateHealthCareProviderDto.providerName,
                        deleted: false,
                        id: { not: id }
                    }
                });
                if (nameConflict) {
                    throw new common_1.ConflictException('Another healthcare provider with this name already exists');
                }
            }
            const updated = await this.prisma.healthCareProvider.update({
                where: { id },
                data: {
                    providerName: updateHealthCareProviderDto.providerName,
                    description: updateHealthCareProviderDto.description,
                    location: updateHealthCareProviderDto.location,
                    contactEmail: updateHealthCareProviderDto.contactEmail,
                    contactPhone: updateHealthCareProviderDto.contactPhone,
                    website: updateHealthCareProviderDto.website
                }
            });
            return updated;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to update healthcare provider', error.stack);
            throw new Error('Failed to update healthcare provider');
        }
    }
    async remove(id, userId) {
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can delete healthcare providers');
            }
            const existing = await this.prisma.healthCareProvider.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });
            if (!existing) {
                throw new common_1.NotFoundException('Healthcare provider not found');
            }
            await this.prisma.healthCareProvider.update({
                where: { id },
                data: { deleted: true }
            });
            return { success: true };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to delete healthcare provider', error.stack);
            throw new Error('Failed to delete healthcare provider');
        }
    }
};
exports.HealthCareProvidersService = HealthCareProvidersService;
exports.HealthCareProvidersService = HealthCareProvidersService = HealthCareProvidersService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], HealthCareProvidersService);
//# sourceMappingURL=health-care-providers.service.js.map