import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useMouStore } from '@/store/mou-store'
import { mouDetailsSchema, partySchema, projectSchema, activitySchema, documentSchema } from '@/lib/validations/mou'
import { toast } from '@/components/ui/use-toast'
import { z } from 'zod'
import { useToast } from './use-toast'
import { FieldValues } from 'react-hook-form'

// Auto-save functionality
export const useFieldBlurAutoSave = () => {
  const lastSaved = useRef<Date | null>(null)
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const onBlur = useCallback(() => {
    // Only save if it's been at least 1 second since the last save
    if (!lastSaved.current || Date.now() - lastSaved.current.getTime() > 1000) {
      lastSaved.current = new Date()
      toast({
        title: 'Auto-saving...',
        description: 'Your changes are being saved.',
        duration: 1000
      })
    }
  }, [])

  useEffect(() => {
    // Auto-save every 30 seconds
    saveTimeoutRef.current = setInterval(() => {
      if (!lastSaved.current || Date.now() - lastSaved.current.getTime() > 30000) {
        lastSaved.current = new Date()
        toast({
          title: 'Auto-saving...',
          description: 'Your changes are being saved.',
          duration: 1000
        })
      }
    }, 30000)

    return () => {
      if (saveTimeoutRef.current) {
        clearInterval(saveTimeoutRef.current)
      }
    }
  }, [])

  return { onBlur }
}

export function useFormAutoSave<T extends FieldValues>(
  onSave: (data: T) => void,
  delay: number = 1000
) {
  const [saving, setSaving] = useState(false)
  const { toast } = useToast()

  const debouncedSave = useCallback(
    async (data: T) => {
      setSaving(true)
      try {
        await onSave(data)
        toast({
          title: "Changes saved",
          description: "Your changes have been automatically saved.",
          duration: 2000,
        })
      } catch (error) {
        toast({
          title: "Error saving changes",
          description: "There was a problem saving your changes.",
          variant: "destructive",
        })
      } finally {
        setSaving(false)
      }
    },
    [onSave, toast]
  )

  const onFieldBlur = useCallback(
    async (data: T) => {
      await debouncedSave(data)
    },
    [debouncedSave]
  )

  return {
    saving,
    onFieldBlur,
  }
}

export const useStepNavigation = () => {
  const [isValidating, setIsValidating] = useState(false)
  const [canGoNext, setCanGoNext] = useState(false)
  const {
    currentStep,
    setCurrentStep,
    organizationName,
    mouDuration,
    durationReason,
    parties,
    projects,
    activities,
    documents
  } = useMouStore()

  const validateCurrentStep = useCallback(async () => {
    setIsValidating(true)
    try {
      switch (currentStep) {
        case 1:
          await mouDetailsSchema.parseAsync({
            organizationName,
            mouDuration,
            durationReason
          })
          return true

        case 2:
          // Debug logging
          console.log('Validating step 2 - Party Details:', { parties })

          // Simple validation for progression - just check basic required fields
          if (!parties || parties.length === 0) {
            console.log('❌ No parties found')
            throw new Error("At least one party is required")
          }

          // Check basic required fields for each party
          for (let i = 0; i < parties.length; i++) {
            const party = parties[i]
            console.log(`Validating party ${i + 1}:`, party)

            // Basic field validation
            if (!party.name?.trim()) {
              console.log(`❌ Party ${i + 1}: Missing organization name`)
              throw new Error(`Party ${i + 1}: Organization name is required`)
            }
            if (!party.signatoryName?.trim()) {
              console.log(`❌ Party ${i + 1}: Missing signatory name`)
              throw new Error(`Party ${i + 1}: Signatory name is required`)
            }
            if (!party.position?.trim()) {
              console.log(`❌ Party ${i + 1}: Missing position`)
              throw new Error(`Party ${i + 1}: Position is required`)
            }

            console.log(`✅ Party ${i + 1} basic validation passed`)
          }

          console.log('✅ Step 2 validation passed! All parties have basic info.')
          return true

        case 3:
          await z.array(projectSchema)
            .min(1, 'At least one project is required')
            .parseAsync(projects)
          return true

        case 4:
          await z.array(activitySchema).parseAsync(activities)
          // Additional validation: ensure each project has at least one activity
          const projectIds = new Set(projects.map(p => p.id))
          const activityProjectIds = new Set(activities.map(a => a.projectId))
          if (![...projectIds].every(id => activityProjectIds.has(id))) {
            throw new Error('Each project must have at least one activity')
          }
          return true

        case 5:
          // Validate all required documents are uploaded
          const requiredDocuments = ['LongTermObjective', 'StrategicPlan', 'CapacityBuildingPlan', 'FundingSourceMemo']
          for (const docType of requiredDocuments) {
            const doc = documents.find(d => d.type === docType)
            if (!doc?.file) {
              throw new Error(`${docType.replace(/([A-Z])/g, ' $1').trim()} is required`)
            }
            await documentSchema.parseAsync({ file: doc.file })
          }
          return true

        case 6:
          // Review step - all previous validations must pass
          return true

        default:
          return false
      }
    } catch (error) {
      if (error instanceof Error) {
        toast({
          variant: "destructive",
          title: "Validation Error",
          description: error.message
        })
      }
      return false
    } finally {
      setIsValidating(false)
    }
  }, [
    currentStep,
    organizationName,
    mouDuration,
    durationReason,
    parties,
    projects,
    activities,
    documents
  ])

  // Check if we can go to next step (async validation)
  const checkCanGoNext = useCallback(async () => {
    if (currentStep === 6) return false // No next step after review
    return await validateCurrentStep()
  }, [currentStep, validateCurrentStep])

  // Update canGoNext state when dependencies change
  React.useEffect(() => {
    const updateCanGoNext = async () => {
      try {
        const result = await checkCanGoNext()
        console.log('canGoNext updated:', result)
        setCanGoNext(result)
      } catch (error) {
        console.log('canGoNext validation failed:', error)
        setCanGoNext(false)
      }
    }
    updateCanGoNext()
  }, [checkCanGoNext, parties, organizationName, mouDuration, durationReason, projects, activities, documents])

  const goToNextStep = useCallback(async () => {
    if (await validateCurrentStep()) {
      if (currentStep < 6) {
        setCurrentStep(currentStep + 1)
      } else if (currentStep === 6) {
        // Handle form submission
        useMouStore.getState().setSubmitting(true)
        try {
          // In a real implementation, this would make an API call
          await new Promise(resolve => setTimeout(resolve, 1000))
          toast({
            title: "Success!",
            description: "Your MoU application has been submitted successfully.",
          })
        } catch (error) {
          toast({
            variant: "destructive",
            title: "Error",
            description: "Failed to submit MoU application. Please try again.",
          })
        } finally {
          useMouStore.getState().setSubmitting(false)
        }
      }
    }
  }, [currentStep, validateCurrentStep, setCurrentStep])

  const goToPreviousStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }, [currentStep, setCurrentStep])

  return {
    currentStep,
    isValidating,
    canGoNext,
    goToNextStep,
    goToPreviousStep
  }
}

export const useCalculateTotalBudget = (fiscalYearBudgets: { budget: number }[]) => {
  return fiscalYearBudgets.reduce((sum, fyb) => sum + fyb.budget, 0)
}

export const useActivityBudgetValidation = (
  projectId: string,
  fiscalYear: string,
  budgetAllocations: { budget: number }[]
) => {
  const project = useMouStore(
    state => state.projects.find(p => p.id === projectId)
  )
  
  const activities = useMouStore(
    state => state.activities.filter(
      a => a.projectId === projectId && a.fiscalYear === fiscalYear
    )
  )

  if (!project) return { isValid: false, availableBudget: 0 }

  const fiscalYearBudget = project.fiscalYearBudgets.find(
    fyb => fyb.fiscalYear === fiscalYear
  )

  if (!fiscalYearBudget) return { isValid: false, availableBudget: 0 }

  const totalBudgetForFiscalYear = activities.reduce(
    (sum, activity) => 
      sum + activity.budgetAllocations.reduce(
        (activitySum, allocation) => activitySum + allocation.budget,
        0
      ),
    0
  )

  const currentActivityBudget = budgetAllocations.reduce(
    (sum, allocation) => sum + allocation.budget,
    0
  )

  const availableBudget = fiscalYearBudget.budget - totalBudgetForFiscalYear + currentActivityBudget

  return {
    isValid: currentActivityBudget <= availableBudget,
    availableBudget
  }
}
