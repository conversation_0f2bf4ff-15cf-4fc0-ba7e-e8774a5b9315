import { PrismaService } from '../prisma/prisma.service';
import { CreateInputDto, UpdateInputDto, CreateInputSubclassDto, UpdateInputSubclassDto } from './dto/create-input.dto';
export declare class InputsService {
    private prisma;
    constructor(prisma: PrismaService);
    createInput(createInputDto: CreateInputDto): Promise<{
        inputSubclass: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            inputId: number;
            subclassId: number;
            budget: number;
        }[];
        activities: ({
            project: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                organizationId: number;
                description: string | null;
                duration: number;
                budgetTypeId: number;
                fundingUnitId: number;
                fundingSourceId: number;
                mouApplicationId: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            description: string | null;
            projectId: number;
            startDate: Date;
            endDate: Date;
            implementer: string;
            implementerUnit: string;
            fiscalYear: number;
            domainInterventionId: number;
            inputId: number;
        })[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
    }>;
    findAllInputs(page?: number, limit?: number, inputSubclassId?: number): Promise<{
        data: ({
            inputSubclass: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                inputId: number;
                subclassId: number;
                budget: number;
            }[];
            activities: ({
                project: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    name: string;
                    organizationId: number;
                    description: string | null;
                    duration: number;
                    budgetTypeId: number;
                    fundingUnitId: number;
                    fundingSourceId: number;
                    mouApplicationId: number;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                description: string | null;
                projectId: number;
                startDate: Date;
                endDate: Date;
                implementer: string;
                implementerUnit: string;
                fiscalYear: number;
                domainInterventionId: number;
                inputId: number;
            })[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        })[];
        meta: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    findOneInput(id: number): Promise<{
        inputSubclass: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            inputId: number;
            subclassId: number;
            budget: number;
        }[];
        activities: ({
            domainIntervention: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                description: string | null;
                userId: number | null;
                parentId: number | null;
                domainName: string;
            };
            project: {
                mouApplication: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    userId: number | null;
                    applicationKey: string;
                    mouId: number;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                organizationId: number;
                description: string | null;
                duration: number;
                budgetTypeId: number;
                fundingUnitId: number;
                fundingSourceId: number;
                mouApplicationId: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            description: string | null;
            projectId: number;
            startDate: Date;
            endDate: Date;
            implementer: string;
            implementerUnit: string;
            fiscalYear: number;
            domainInterventionId: number;
            inputId: number;
        })[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
    }>;
    updateInput(id: number, updateInputDto: UpdateInputDto): Promise<{
        inputSubclass: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            inputId: number;
            subclassId: number;
            budget: number;
        }[];
        activities: ({
            project: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                organizationId: number;
                description: string | null;
                duration: number;
                budgetTypeId: number;
                fundingUnitId: number;
                fundingSourceId: number;
                mouApplicationId: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            description: string | null;
            projectId: number;
            startDate: Date;
            endDate: Date;
            implementer: string;
            implementerUnit: string;
            fiscalYear: number;
            domainInterventionId: number;
            inputId: number;
        })[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
    }>;
    removeInput(id: number): Promise<{
        message: string;
    }>;
    createInputSubclass(createInputSubclassDto: CreateInputSubclassDto): Promise<{
        input: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        inputId: number;
        subclassId: number;
        budget: number;
    }>;
    findAllInputSubclasses(): Promise<({
        input: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        inputId: number;
        subclassId: number;
        budget: number;
    })[]>;
    findOneInputSubclass(id: number): Promise<{
        input: {
            activities: ({
                project: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    name: string;
                    organizationId: number;
                    description: string | null;
                    duration: number;
                    budgetTypeId: number;
                    fundingUnitId: number;
                    fundingSourceId: number;
                    mouApplicationId: number;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                description: string | null;
                projectId: number;
                startDate: Date;
                endDate: Date;
                implementer: string;
                implementerUnit: string;
                fiscalYear: number;
                domainInterventionId: number;
                inputId: number;
            })[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        inputId: number;
        subclassId: number;
        budget: number;
    }>;
    updateInputSubclass(id: number, updateInputSubclassDto: UpdateInputSubclassDto): Promise<{
        input: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        inputId: number;
        subclassId: number;
        budget: number;
    }>;
    removeInputSubclass(id: number): Promise<{
        message: string;
    }>;
    getInputsBySubclass(inputSubclassId: number): Promise<({
        inputSubclass: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            inputId: number;
            subclassId: number;
            budget: number;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
    })[]>;
}
