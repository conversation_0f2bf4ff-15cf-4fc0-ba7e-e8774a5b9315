import { Injectable, ConflictException, NotFoundException, ForbiddenException, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateInputCategoryDto, UpdateInputCategoryDto } from './dto';

@Injectable()
export class InputCategoriesService {
    private readonly logger = new Logger(InputCategoriesService.name);

    constructor(private prisma: PrismaService) {}

    async create(createInputCategoryDto: CreateInputCategoryDto, userId: string) {
        try {
            // Check if user has admin role
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            if (user.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can create input categories');
            }

            // If parentId is provided, verify parent exists
            if (createInputCategoryDto.parentId) {
                const parent = await this.prisma.inputCategory.findFirst({
                    where: {
                        id: createInputCategoryDto.parentId,
                        deleted: false
                    }
                });

                if (!parent) {
                    throw new BadRequestException('Parent input category not found');
                }
            }

            // Check if category with same name exists at the same level
            const existing = await this.prisma.inputCategory.findFirst({
                where: {
                    categoryName: createInputCategoryDto.categoryName,
                    parentId: createInputCategoryDto.parentId,
                    deleted: false
                }
            });

            if (existing) {
                throw new ConflictException('Input category with this name already exists at this level');
            }

            // Create the input category
            const category = await this.prisma.inputCategory.create({
                data: {
                    categoryName: createInputCategoryDto.categoryName,
                    description: createInputCategoryDto.description,
                    parentId: createInputCategoryDto.parentId
                },
                include: {
                    parent: true,
                    children: true
                }
            });

            return category;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || 
                error instanceof ForbiddenException || error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to create input category', error.stack);
            throw new Error('Failed to create input category');
        }
    }

    async findAll() {
        return this.prisma.inputCategory.findMany({
            where: {
                deleted: false
            },
            include: {
                parent: true,
                children: true
            },
            orderBy: {
                categoryName: 'asc'
            }
        });
    }

    async findTree() {
        // First get all categories
        const categories = await this.prisma.inputCategory.findMany({
            where: {
                deleted: false
            },
            orderBy: {
                categoryName: 'asc'
            }
        });

        // Build tree structure
        const buildTree = (parentId: number | null = null): any[] => {
            return categories
                .filter(c => c.parentId === parentId)
                .map(c => ({
                    ...c,
                    children: buildTree(c.id)
                }));
        };

        return buildTree(null);
    }

    async findOne(id: number) {
        const category = await this.prisma.inputCategory.findFirst({
            where: {
                id,
                deleted: false
            },
            include: {
                parent: true,
                children: true
            }
        });

        if (!category) {
            throw new NotFoundException('Input category not found');
        }

        return category;
    }

    async update(id: number, updateInputCategoryDto: UpdateInputCategoryDto, userId: string) {
        try {
            // Check if user has admin role
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            if (user.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can update input categories');
            }

            // Check if category exists
            const existing = await this.prisma.inputCategory.findFirst({
                where: {
                    id,
                    deleted: false
                },
                include: {
                    children: true
                }
            });

            if (!existing) {
                throw new NotFoundException('Input category not found');
            }

            // If parentId is being changed
            if (updateInputCategoryDto.parentId !== undefined) {
                // Cannot set parent to self
                if (updateInputCategoryDto.parentId === id) {
                    throw new BadRequestException('Cannot set category as its own parent');
                }

                // Cannot set parent to one of its descendants
                const isDescendant = async (childId: number, potentialParentId: number): Promise<boolean> => {
                    const child = await this.prisma.inputCategory.findUnique({
                        where: { id: childId },
                        include: { children: true }
                    });
                    
                    if (!child) return false;
                    if (child.id === potentialParentId) return true;
                    
                    for (const descendant of child.children) {
                        if (await isDescendant(descendant.id, potentialParentId)) {
                            return true;
                        }
                    }
                    return false;
                };

                if (updateInputCategoryDto.parentId && 
                    await isDescendant(updateInputCategoryDto.parentId, id)) {
                    throw new BadRequestException('Cannot set a descendant as parent');
                }

                // Verify new parent exists if specified
                if (updateInputCategoryDto.parentId) {
                    const parent = await this.prisma.inputCategory.findFirst({
                        where: {
                            id: updateInputCategoryDto.parentId,
                            deleted: false
                        }
                    });

                    if (!parent) {
                        throw new BadRequestException('Parent input category not found');
                    }
                }
            }

            // Check for name conflicts at the same level
            if (updateInputCategoryDto.categoryName && 
                updateInputCategoryDto.categoryName !== existing.categoryName) {
                const nameConflict = await this.prisma.inputCategory.findFirst({
                    where: {
                        categoryName: updateInputCategoryDto.categoryName,
                        parentId: updateInputCategoryDto.parentId ?? existing.parentId,
                        deleted: false,
                        id: { not: id }
                    }
                });

                if (nameConflict) {
                    throw new ConflictException('Another input category with this name exists at this level');
                }
            }

            // Update the input category
            const updated = await this.prisma.inputCategory.update({
                where: { id },
                data: {
                    categoryName: updateInputCategoryDto.categoryName,
                    description: updateInputCategoryDto.description,
                    parentId: updateInputCategoryDto.parentId
                },
                include: {
                    parent: true,
                    children: true
                }
            });

            return updated;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || 
                error instanceof ForbiddenException || error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to update input category', error.stack);
            throw new Error('Failed to update input category');
        }
    }

    async remove(id: number, userId: string) {
        try {
            // Check if user has admin role
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            if (user.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can delete input categories');
            }

            // Check if category exists
            const existing = await this.prisma.inputCategory.findFirst({
                where: {
                    id,
                    deleted: false
                },
                include: {
                    children: {
                        where: {
                            deleted: false
                        }
                    }
                }
            });

            if (!existing) {
                throw new NotFoundException('Input category not found');
            }

            // Check if category has children
            if (existing.children.length > 0) {
                throw new ConflictException('Cannot delete input category that has child categories');
            }

            // Check if category is being used in inputs
            // Note: Add this check when Input model relationship is added
            // const inUse = await this.prisma.input.findFirst({
            //     where: {
            //         categoryId: id,
            //         deleted: false
            //     }
            // });
            // if (inUse) {
            //     throw new ConflictException('Cannot delete input category that is being used');
            // }

            // Soft delete the input category
            await this.prisma.inputCategory.update({
                where: { id },
                data: { deleted: true }
            });

            return { success: true };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || 
                error instanceof ForbiddenException || error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to delete input category', error.stack);
            throw new Error('Failed to delete input category');
        }
    }
}
