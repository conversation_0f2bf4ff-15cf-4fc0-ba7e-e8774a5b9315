import { PrismaService } from '../prisma/prisma.service';
import { CreateUserDto, UpdateUserDto } from './dto';
import { EmailService } from '../email/email.service';
export declare class UsersService {
    private prisma;
    private emailService;
    private readonly logger;
    private readonly SALT_ROUNDS;
    constructor(prisma: PrismaService, emailService: EmailService);
    findAll(currentUserId: string): Promise<{
        id: number;
        firstName: string;
        lastName: string;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        emailVerified: boolean;
        organizationId: number;
        organization: {
            id: number;
            organizationName: string;
        };
        createdAt: Date;
        updatedAt: Date;
    }[]>;
    findOne(id: string, currentUserId: string): Promise<{
        id: number;
        firstName: string;
        lastName: string;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        emailVerified: boolean;
        organizationId: number;
        organization: {
            id: number;
            organizationName: string;
        };
        createdAt: Date;
        updatedAt: Date;
    }>;
    create(createUserDto: CreateUserDto, currentUserId: string): Promise<{
        id: number;
        firstName: string;
        lastName: string;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        emailVerified: boolean;
        organizationId: number;
        createdAt: Date;
        updatedAt: Date;
        tempPassword: string;
    }>;
    update(id: string, updateUserDto: UpdateUserDto, currentUserId: string): Promise<{
        id: number;
        firstName: string;
        lastName: string;
        email: string;
        role: import("@prisma/client").$Enums.UserRole;
        emailVerified: boolean;
        organizationId: number;
        organization: {
            id: number;
            organizationName: string;
        };
        createdAt: Date;
        updatedAt: Date;
    }>;
    remove(id: string, currentUserId: string): Promise<{
        message: string;
    }>;
}
