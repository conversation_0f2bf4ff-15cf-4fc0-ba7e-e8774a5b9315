"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MouApplicationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const mou_applications_service_1 = require("./mou-applications.service");
const create_mou_application_dto_1 = require("./dto/create-mou-application.dto");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
const roles_guard_1 = require("../auth/guard/roles.guard");
const roles_decorator_1 = require("../auth/decorator/roles.decorator");
const dto_1 = require("../auth/dto");
let MouApplicationsController = class MouApplicationsController {
    constructor(mouApplicationsService) {
        this.mouApplicationsService = mouApplicationsService;
    }
    async create(createMouApplicationDto) {
        return this.mouApplicationsService.create(createMouApplicationDto);
    }
    async findAll(page, limit, userId, req) {
        const pageNum = page ? parseInt(page, 10) : 1;
        const limitNum = limit ? parseInt(limit, 10) : 10;
        const userIdNum = userId ? parseInt(userId, 10) : undefined;
        const finalUserId = req.user?.role === dto_1.UserRole.PARTNER ? req.user.id : userIdNum;
        return this.mouApplicationsService.findAll(pageNum, limitNum, finalUserId);
    }
    async getStats(userId, req) {
        const userIdNum = userId ? parseInt(userId, 10) : undefined;
        const finalUserId = req.user?.role === dto_1.UserRole.PARTNER ? req.user.id : userIdNum;
        return this.mouApplicationsService.getApplicationStats(finalUserId);
    }
    async findOne(id) {
        return this.mouApplicationsService.findOne(id);
    }
    async findByKey(applicationKey) {
        return this.mouApplicationsService.findByApplicationKey(applicationKey);
    }
    async update(id, updateMouApplicationDto) {
        return this.mouApplicationsService.update(id, updateMouApplicationDto);
    }
    async remove(id) {
        return this.mouApplicationsService.remove(id);
    }
};
exports.MouApplicationsController = MouApplicationsController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new MoU application' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'MoU application created successfully',
        type: create_mou_application_dto_1.MouApplicationWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'MoU or User not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Application key already exists' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_mou_application_dto_1.CreateMouApplicationDto]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get all MoU applications with pagination' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10)' }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: false, type: Number, description: 'Filter by user ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of MoU applications',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/MouApplicationWithRelationsDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('userId')),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Object]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get MoU application statistics' }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: false, type: Number, description: 'Filter by user ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Application statistics',
        schema: {
            type: 'object',
            properties: {
                total: { type: 'number' },
                pending: { type: 'number' },
                approved: { type: 'number' },
                rejected: { type: 'number' },
            },
        },
    }),
    __param(0, (0, common_1.Query)('userId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get a MoU application by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'MoU application details',
        type: create_mou_application_dto_1.MouApplicationWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'MoU application not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('by-key/:applicationKey'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get a MoU application by application key' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'MoU application details',
        type: create_mou_application_dto_1.MouApplicationWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'MoU application not found' }),
    __param(0, (0, common_1.Param)('applicationKey')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "findByKey", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Update a MoU application' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'MoU application updated successfully',
        type: create_mou_application_dto_1.MouApplicationWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'MoU application not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, create_mou_application_dto_1.UpdateMouApplicationDto]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a MoU application (soft delete)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'MoU application deleted successfully',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'MoU application not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "remove", null);
exports.MouApplicationsController = MouApplicationsController = __decorate([
    (0, swagger_1.ApiTags)('MoU Applications'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('mou-applications'),
    __metadata("design:paramtypes", [mou_applications_service_1.MouApplicationsService])
], MouApplicationsController);
//# sourceMappingURL=mou-applications.controller.js.map