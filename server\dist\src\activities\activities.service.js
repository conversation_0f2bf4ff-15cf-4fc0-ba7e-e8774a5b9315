"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivitiesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let ActivitiesService = class ActivitiesService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createActivityDto) {
        try {
            await this.validateRelationships(createActivityDto);
            const startDate = new Date(createActivityDto.startDate);
            const endDate = new Date(createActivityDto.endDate);
            if (startDate >= endDate) {
                throw new common_1.BadRequestException('Start date must be before end date');
            }
            const activity = await this.prisma.activity.create({
                data: {
                    ...createActivityDto,
                    startDate,
                    endDate,
                },
                include: {
                    project: {
                        include: {
                            mouApplication: true,
                        },
                    },
                    domainIntervention: {
                        include: {
                            parent: true,
                        },
                    },
                    input: {
                        include: {
                            inputSubclass: true,
                        },
                    },
                },
            });
            return activity;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2003') {
                    throw new common_1.BadRequestException('Invalid foreign key reference');
                }
            }
            throw error;
        }
    }
    async findAll(page = 1, limit = 10, projectId, fiscalYear) {
        const skip = (page - 1) * limit;
        const where = {
            deleted: false,
            ...(projectId && { projectId }),
            ...(fiscalYear && { fiscalYear }),
        };
        const [activities, total] = await Promise.all([
            this.prisma.activity.findMany({
                where,
                skip,
                take: limit,
                include: {
                    project: {
                        include: {
                            mouApplication: true,
                        },
                    },
                    domainIntervention: {
                        include: {
                            parent: true,
                        },
                    },
                    input: {
                        include: {
                            inputSubclass: true,
                        },
                    },
                },
                orderBy: {
                    startDate: 'desc',
                },
            }),
            this.prisma.activity.count({ where }),
        ]);
        return {
            data: activities,
            meta: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id) {
        const activity = await this.prisma.activity.findFirst({
            where: {
                id,
                deleted: false,
            },
            include: {
                project: {
                    include: {
                        mouApplication: {
                            include: {
                                mou: {
                                    include: {
                                        party: {
                                            include: {
                                                organization: true,
                                            },
                                        },
                                    },
                                },
                            },
                        },
                        budgetType: true,
                        fundingSource: true,
                        fundingUnit: true,
                    },
                },
                domainIntervention: {
                    include: {
                        parent: true,
                        children: true,
                    },
                },
                input: {
                    include: {
                        inputSubclass: true,
                    },
                },
            },
        });
        if (!activity) {
            throw new common_1.NotFoundException(`Activity with ID ${id} not found`);
        }
        return activity;
    }
    async update(id, updateActivityDto) {
        const existingActivity = await this.prisma.activity.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingActivity) {
            throw new common_1.NotFoundException(`Activity with ID ${id} not found`);
        }
        if (Object.keys(updateActivityDto).some(key => ['projectId', 'domainInterventionId', 'inputId'].includes(key))) {
            await this.validateRelationships({
                ...existingActivity,
                ...updateActivityDto,
            });
        }
        const startDate = updateActivityDto.startDate ? new Date(updateActivityDto.startDate) : existingActivity.startDate;
        const endDate = updateActivityDto.endDate ? new Date(updateActivityDto.endDate) : existingActivity.endDate;
        if (startDate >= endDate) {
            throw new common_1.BadRequestException('Start date must be before end date');
        }
        try {
            const updatedActivity = await this.prisma.activity.update({
                where: { id },
                data: {
                    ...updateActivityDto,
                    ...(updateActivityDto.startDate && { startDate }),
                    ...(updateActivityDto.endDate && { endDate }),
                },
                include: {
                    project: {
                        include: {
                            mouApplication: true,
                        },
                    },
                    domainIntervention: {
                        include: {
                            parent: true,
                        },
                    },
                    input: {
                        include: {
                            inputSubclass: true,
                        },
                    },
                },
            });
            return updatedActivity;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2003') {
                    throw new common_1.BadRequestException('Invalid foreign key reference');
                }
            }
            throw error;
        }
    }
    async remove(id) {
        const existingActivity = await this.prisma.activity.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingActivity) {
            throw new common_1.NotFoundException(`Activity with ID ${id} not found`);
        }
        await this.prisma.activity.update({
            where: { id },
            data: { deleted: true },
        });
        return { message: 'Activity deleted successfully' };
    }
    async getActivitiesByProject(projectId) {
        const activities = await this.prisma.activity.findMany({
            where: {
                projectId,
                deleted: false,
            },
            include: {
                domainIntervention: {
                    include: {
                        parent: true,
                    },
                },
                input: {
                    include: {
                        inputSubclass: true,
                    },
                },
            },
            orderBy: {
                startDate: 'asc',
            },
        });
        return activities;
    }
    async getActivitiesByFiscalYear(fiscalYear) {
        const activities = await this.prisma.activity.findMany({
            where: {
                fiscalYear,
                deleted: false,
            },
            include: {
                project: {
                    include: {
                        mouApplication: true,
                        organization: true,
                    },
                },
                domainIntervention: true,
                input: {
                    include: {
                        inputSubclass: true,
                    },
                },
            },
            orderBy: {
                startDate: 'asc',
            },
        });
        return activities;
    }
    async validateRelationships(dto) {
        const [project, domainIntervention, input] = await Promise.all([
            this.prisma.project.findUnique({ where: { id: dto.projectId } }),
            this.prisma.domainIntervention.findUnique({ where: { id: dto.domainInterventionId } }),
            this.prisma.input.findUnique({ where: { id: dto.inputId } }),
        ]);
        if (!project) {
            throw new common_1.NotFoundException(`Project with ID ${dto.projectId} not found`);
        }
        if (!domainIntervention) {
            throw new common_1.NotFoundException(`Domain intervention with ID ${dto.domainInterventionId} not found`);
        }
        if (!input) {
            throw new common_1.NotFoundException(`Input with ID ${dto.inputId} not found`);
        }
    }
};
exports.ActivitiesService = ActivitiesService;
exports.ActivitiesService = ActivitiesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ActivitiesService);
//# sourceMappingURL=activities.service.js.map