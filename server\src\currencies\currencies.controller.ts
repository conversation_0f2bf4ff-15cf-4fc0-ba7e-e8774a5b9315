import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { CurrenciesService } from './currencies.service';
import { CreateCurrencyDto, UpdateCurrencyDto, CurrencyResponseDto } from './dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { Public } from '../auth/decorator/public.decorator';

@ApiTags('currencies')
@Controller('currencies')
export class CurrenciesController {
  constructor(private readonly currenciesService: CurrenciesService) {}

  @Post()
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new currency (Admin only)' })
  @ApiResponse({ status: 201, description: 'Currency created successfully', type: CurrencyResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 409, description: 'Currency with code already exists' })
  async create(@Body() createCurrencyDto: CreateCurrencyDto, @Request() req: any) {
    return this.currenciesService.create(createCurrencyDto, req.user.sub);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all currencies (Public access)' })
  @ApiResponse({ status: 200, description: 'List of currencies', type: [CurrencyResponseDto] })
  async findAll() {
    return this.currenciesService.findAll();
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get currency by ID (Public access)' })
  @ApiResponse({ status: 200, description: 'Currency details', type: CurrencyResponseDto })
  @ApiResponse({ status: 404, description: 'Currency not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.currenciesService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update currency (Admin only)' })
  @ApiResponse({ status: 200, description: 'Currency updated successfully', type: CurrencyResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Currency not found' })
  @ApiResponse({ status: 409, description: 'Currency with code already exists' })
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateCurrencyDto: UpdateCurrencyDto, @Request() req: any) {
    return this.currenciesService.update(id, updateCurrencyDto, req.user.sub);
  }

  @Delete(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete currency (Admin only)' })
  @ApiResponse({ status: 200, description: 'Currency deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Currency not found' })
  @ApiResponse({ status: 409, description: 'Cannot delete currency that is being used' })
  async remove(@Param('id', ParseIntPipe) id: number, @Request() req: any) {
    return this.currenciesService.remove(id, req.user.sub);
  }
}
