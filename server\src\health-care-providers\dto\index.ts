import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsEmail, IsUrl } from 'class-validator';

export class CreateHealthCareProviderDto {
    @ApiProperty({ description: 'The name of the healthcare provider' })
    @IsNotEmpty()
    @IsString()
    providerName: string;

    @ApiProperty({ description: 'Description of the healthcare provider', required: false })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'Location of the healthcare provider', required: false })
    @IsOptional()
    @IsString()
    location?: string;

    @ApiProperty({ description: 'Contact email for the healthcare provider', required: false })
    @IsOptional()
    @IsEmail()
    contactEmail?: string;

    @ApiProperty({ description: 'Contact phone number for the healthcare provider', required: false })
    @IsOptional()
    @IsString()
    contactPhone?: string;

    @ApiProperty({ description: 'Website URL for the healthcare provider', required: false })
    @IsOptional()
    @IsUrl()
    website?: string;
}

export class UpdateHealthCareProviderDto {
    @ApiProperty({ description: 'The name of the healthcare provider', required: false })
    @IsOptional()
    @IsString()
    providerName?: string;

    @ApiProperty({ description: 'Description of the healthcare provider', required: false })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'Location of the healthcare provider', required: false })
    @IsOptional()
    @IsString()
    location?: string;

    @ApiProperty({ description: 'Contact email for the healthcare provider', required: false })
    @IsOptional()
    @IsEmail()
    contactEmail?: string;

    @ApiProperty({ description: 'Contact phone number for the healthcare provider', required: false })
    @IsOptional()
    @IsString()
    contactPhone?: string;

    @ApiProperty({ description: 'Website URL for the healthcare provider', required: false })
    @IsOptional()
    @IsUrl()
    website?: string;
}

export class HealthCareProviderResponseDto {
    @ApiProperty()
    id: number;

    @ApiProperty({ description: 'The name of the healthcare provider' })
    providerName: string;

    @ApiProperty({ description: 'Description of the healthcare provider' })
    description?: string;

    @ApiProperty({ description: 'Location of the healthcare provider' })
    location?: string;

    @ApiProperty({ description: 'Contact email for the healthcare provider' })
    contactEmail?: string;

    @ApiProperty({ description: 'Contact phone number for the healthcare provider' })
    contactPhone?: string;

    @ApiProperty({ description: 'Website URL for the healthcare provider' })
    website?: string;

    @ApiProperty({ description: 'When the healthcare provider was created' })
    createdAt: Date;

    @ApiProperty({ description: 'When the healthcare provider was last updated' })
    updatedAt: Date;

    @ApiProperty({ description: 'Whether the healthcare provider has been soft deleted' })
    deleted: boolean;
}
