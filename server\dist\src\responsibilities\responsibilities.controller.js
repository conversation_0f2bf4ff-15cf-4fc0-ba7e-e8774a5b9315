"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponsibilitiesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const responsibilities_service_1 = require("./responsibilities.service");
const create_responsibility_dto_1 = require("./dto/create-responsibility.dto");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
const roles_guard_1 = require("../auth/guard/roles.guard");
const roles_decorator_1 = require("../auth/decorator/roles.decorator");
const dto_1 = require("../auth/dto");
let ResponsibilitiesController = class ResponsibilitiesController {
    constructor(responsibilitiesService) {
        this.responsibilitiesService = responsibilitiesService;
    }
    async create(createResponsibilityDto) {
        return this.responsibilitiesService.create(createResponsibilityDto);
    }
    async findAll() {
        return this.responsibilitiesService.findAll();
    }
    async findOne(id) {
        return this.responsibilitiesService.findOne(id);
    }
    async update(id, updateResponsibilityDto) {
        return this.responsibilitiesService.update(id, updateResponsibilityDto);
    }
    async remove(id) {
        return this.responsibilitiesService.remove(id);
    }
};
exports.ResponsibilitiesController = ResponsibilitiesController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new responsibility' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Responsibility created successfully',
        type: create_responsibility_dto_1.ResponsibilityResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Responsibility name already exists' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_responsibility_dto_1.CreateResponsibilityDto]),
    __metadata("design:returntype", Promise)
], ResponsibilitiesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get all responsibilities' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of responsibilities',
        type: [create_responsibility_dto_1.ResponsibilityResponseDto],
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ResponsibilitiesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get a responsibility by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Responsibility details',
        type: create_responsibility_dto_1.ResponsibilityResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Responsibility not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ResponsibilitiesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Update a responsibility' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Responsibility updated successfully',
        type: create_responsibility_dto_1.ResponsibilityResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Responsibility not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Responsibility name already exists' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, create_responsibility_dto_1.UpdateResponsibilityDto]),
    __metadata("design:returntype", Promise)
], ResponsibilitiesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a responsibility (soft delete)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Responsibility deleted successfully',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Responsibility not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Responsibility is being used by parties' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ResponsibilitiesController.prototype, "remove", null);
exports.ResponsibilitiesController = ResponsibilitiesController = __decorate([
    (0, swagger_1.ApiTags)('Responsibilities'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('responsibilities'),
    __metadata("design:paramtypes", [responsibilities_service_1.ResponsibilitiesService])
], ResponsibilitiesController);
//# sourceMappingURL=responsibilities.controller.js.map