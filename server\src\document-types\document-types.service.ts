import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateDocumentTypeDto, UpdateDocumentTypeDto } from './dto/create-document-type.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class DocumentTypesService {
  constructor(private prisma: PrismaService) {}

  async create(createDocumentTypeDto: CreateDocumentTypeDto) {
    try {
      const documentType = await this.prisma.documentType.create({
        data: createDocumentTypeDto,
      });

      return documentType;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Document type name must be unique');
        }
      }
      throw error;
    }
  }

  async findAll() {
    const documentTypes = await this.prisma.documentType.findMany({
      where: {
        deleted: false,
      },
      orderBy: {
        typeName: 'asc',
      },
    });

    return documentTypes;
  }

  async findOne(id: number) {
    const documentType = await this.prisma.documentType.findFirst({
      where: {
        id,
        deleted: false,
      },
      include: {
        documents: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!documentType) {
      throw new NotFoundException(`Document type with ID ${id} not found`);
    }

    return documentType;
  }

  async update(id: number, updateDocumentTypeDto: UpdateDocumentTypeDto) {
    // Check if document type exists
    const existingDocumentType = await this.prisma.documentType.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingDocumentType) {
      throw new NotFoundException(`Document type with ID ${id} not found`);
    }

    try {
      const updatedDocumentType = await this.prisma.documentType.update({
        where: { id },
        data: updateDocumentTypeDto,
      });

      return updatedDocumentType;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Document type name must be unique');
        }
      }
      throw error;
    }
  }

  async remove(id: number) {
    // Check if document type exists
    const existingDocumentType = await this.prisma.documentType.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingDocumentType) {
      throw new NotFoundException(`Document type with ID ${id} not found`);
    }

    // Check if document type is being used by documents
    const documentsUsingType = await this.prisma.document.findMany({
      where: {
        documentTypeId: id,
        deleted: false,
      },
    });

    if (documentsUsingType.length > 0) {
      throw new ConflictException('Cannot delete document type that is being used by documents');
    }

    // Soft delete
    await this.prisma.documentType.update({
      where: { id },
      data: { deleted: true },
    });

    return { message: 'Document type deleted successfully' };
  }
}
