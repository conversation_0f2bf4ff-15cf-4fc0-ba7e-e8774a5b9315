{"version": 3, "file": "documents.service.js", "sourceRoot": "", "sources": ["../../../src/documents/documents.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAuG;AACvG,6DAAyD;AAEzD,2CAAwC;AACxC,yBAAyB;AACzB,6BAA6B;AAGtB,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAC3B,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAE7C,KAAK,CAAC,MAAM,CAAC,iBAAoC;QAC/C,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;YAEpD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACjD,IAAI,EAAE,iBAAiB;gBACvB,OAAO,EAAE;oBACP,YAAY,EAAE,IAAI;oBAClB,YAAY,EAAE,IAAI;oBAClB,OAAO,EAAE;wBACP,OAAO,EAAE;4BACP,cAAc,EAAE,IAAI;yBACrB;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,eAAM,CAAC,6BAA6B,EAAE,CAAC;gBAC1D,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBAC3B,MAAM,IAAI,4BAAmB,CAAC,+BAA+B,CAAC,CAAC;gBACjE,CAAC;YACH,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,OAAe,CAAC,EAAE,QAAgB,EAAE,EAAE,cAAuB,EAAE,cAAuB;QAClG,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAEhC,MAAM,KAAK,GAA8B;YACvC,OAAO,EAAE,KAAK;YACd,GAAG,CAAC,cAAc,IAAI,EAAE,cAAc,EAAE,CAAC;YACzC,GAAG,CAAC,cAAc,IAAI,EAAE,cAAc,EAAE,CAAC;SAC1C,CAAC;QAEF,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC3C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBAC5B,KAAK;gBACL,IAAI;gBACJ,IAAI,EAAE,KAAK;gBACX,OAAO,EAAE;oBACP,YAAY,EAAE,IAAI;oBAClB,YAAY,EAAE,IAAI;oBAClB,OAAO,EAAE;wBACP,OAAO,EAAE;4BACP,cAAc,EAAE,IAAI;yBACrB;qBACF;iBACF;gBACD,OAAO,EAAE;oBACP,SAAS,EAAE,MAAM;iBAClB;aACF,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC;SACtC,CAAC,CAAC;QAEH,OAAO;YACL,IAAI,EAAE,SAAS;YACf,IAAI,EAAE;gBACJ,KAAK;gBACL,IAAI;gBACJ,KAAK;gBACL,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;aACrC;SACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YACpD,KAAK,EAAE;gBACL,EAAE;gBACF,OAAO,EAAE,KAAK;aACf;YACD,OAAO,EAAE;gBACP,YAAY,EAAE;oBACZ,OAAO,EAAE;wBACP,gBAAgB,EAAE,IAAI;wBACtB,SAAS,EAAE,IAAI;qBAChB;iBACF;gBACD,YAAY,EAAE,IAAI;gBAClB,OAAO,EAAE;oBACP,OAAO,EAAE;wBACP,cAAc,EAAE;4BACd,OAAO,EAAE;gCACP,GAAG,EAAE;oCACH,OAAO,EAAE;wCACP,KAAK,EAAE,IAAI;qCACZ;iCACF;6BACF;yBACF;wBACD,UAAU,EAAE,IAAI;qBACjB;iBACF;aACF;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAC;QAClE,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,iBAAoC;QAE3D,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC5D,KAAK,EAAE;gBACL,EAAE;gBACF,OAAO,EAAE,KAAK;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAC;QAClE,CAAC;QAGD,IAAI,iBAAiB,CAAC,cAAc,EAAE,CAAC;YACrC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;gBAC7D,KAAK,EAAE,EAAE,EAAE,EAAE,iBAAiB,CAAC,cAAc,EAAE;aAChD,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,iBAAiB,CAAC,cAAc,YAAY,CAAC,CAAC;YACrG,CAAC;QACH,CAAC;QAED,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACxD,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,IAAI,EAAE,iBAAiB;gBACvB,OAAO,EAAE;oBACP,YAAY,EAAE,IAAI;oBAClB,YAAY,EAAE,IAAI;oBAClB,OAAO,EAAE;wBACP,OAAO,EAAE;4BACP,cAAc,EAAE,IAAI;yBACrB;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,eAAM,CAAC,6BAA6B,EAAE,CAAC;gBAC1D,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBAC3B,MAAM,IAAI,4BAAmB,CAAC,+BAA+B,CAAC,CAAC;gBACjE,CAAC;YACH,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QAErB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC5D,KAAK,EAAE;gBACL,EAAE;gBACF,OAAO,EAAE,KAAK;aACf;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAC;QAClE,CAAC;QAGD,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YAC/D,KAAK,EAAE;gBACL,SAAS,EAAE;oBACT,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;iBACjB;aACF;SACF,CAAC,CAAC;QAEH,IAAI,oBAAoB,EAAE,CAAC;YACzB,MAAM,IAAI,0BAAiB,CAAC,wDAAwD,CAAC,CAAC;QACxF,CAAC;QAGD,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAChC,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,IAAI,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;SACxB,CAAC,CAAC;QAEH,OAAO,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,cAAsB;QACrD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACpD,KAAK,EAAE;gBACL,cAAc;gBACd,OAAO,EAAE,KAAK;aACf;YACD,OAAO,EAAE;gBACP,YAAY,EAAE,IAAI;gBAClB,OAAO,EAAE;oBACP,OAAO,EAAE;wBACP,cAAc,EAAE,IAAI;qBACrB;iBACF;aACF;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM;aAClB;SACF,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,cAAsB;QAC7C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACpD,KAAK,EAAE;gBACL,cAAc;gBACd,OAAO,EAAE,KAAK;aACf;YACD,OAAO,EAAE;gBACP,YAAY,EAAE,IAAI;gBAClB,OAAO,EAAE;oBACP,OAAO,EAAE;wBACP,cAAc,EAAE,IAAI;qBACrB;iBACF;aACF;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,MAAM;aAClB;SACF,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAyB,EAAE,iBAAoC;QAC9E,IAAI,CAAC;YAEH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,4BAAmB,CAAC,kBAAkB,CAAC,CAAC;YACpD,CAAC;YAGD,MAAM,gBAAgB,GAAG;gBACvB,iBAAiB;gBACjB,oBAAoB;gBACpB,yEAAyE;gBACzE,0BAA0B;gBAC1B,mEAAmE;gBACnE,YAAY;gBACZ,WAAW;gBACX,WAAW;aACZ,CAAC;YAEF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC9C,MAAM,IAAI,4BAAmB,CAAC,wFAAwF,CAAC,CAAC;YAC1H,CAAC;YAGD,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;YACjC,IAAI,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE,CAAC;gBACxB,MAAM,IAAI,4BAAmB,CAAC,8BAA8B,CAAC,CAAC;YAChE,CAAC;YAGD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YAGtD,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACtD,MAAM,QAAQ,GAAG,OAAO,QAAQ,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,aAAa,EAAE,CAAC;YACpE,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;YACpE,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAGjD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC/B,EAAE,CAAC,SAAS,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAChD,CAAC;YAGD,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAIxC,OAAO;gBACL,QAAQ;gBACR,IAAI,EAAE;oBACJ,QAAQ;oBACR,YAAY,EAAE,IAAI,CAAC,YAAY;oBAC/B,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;oBACvB,IAAI,EAAE,sBAAsB,QAAQ,EAAE;oBACtC,UAAU,EAAE,IAAI,IAAI,EAAE;iBACvB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,EAAU;QAC3B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAIxC,MAAM,IAAI,0BAAiB,CAAC,iDAAiD,CAAC,CAAC;IACjF,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,GAAsB;QACxD,MAAM,CAAC,YAAY,EAAE,YAAY,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACrD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;YAC1E,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC;SAC3E,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,0BAAiB,CAAC,wBAAwB,GAAG,CAAC,cAAc,YAAY,CAAC,CAAC;QACtF,CAAC;QACD,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,0BAAiB,CAAC,yBAAyB,GAAG,CAAC,cAAc,YAAY,CAAC,CAAC;QACvF,CAAC;IACH,CAAC;CACF,CAAA;AArUY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;qCAEiB,8BAAa;GAD9B,gBAAgB,CAqU5B"}