"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  FileText, 
  ArrowLeft,
  Edit,
  Send,
  Clock,
  CheckCircle,
  XCircle,
  FileEdit,
  Building,
  Calendar,
  DollarSign,
  User,
  MessageSquare
} from "lucide-react"
import Link from "next/link"
import { mouApplicationService, MouApplication } from "@/lib/services/mou-application.service"
import { Loader2 } from "lucide-react"
import { format } from "date-fns"

const getStatusIcon = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'draft':
      return <FileEdit className="h-4 w-4" />
    case 'pending':
      return <Clock className="h-4 w-4" />
    case 'approved':
      return <CheckCircle className="h-4 w-4" />
    case 'rejected':
      return <XCircle className="h-4 w-4" />
    default:
      return <FileText className="h-4 w-4" />
  }
}

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'draft':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    case 'pending':
      return 'bg-amber-100 text-amber-800 border-amber-200'
    case 'approved':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'rejected':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

export default function ViewApplicationPage() {
  const { user } = useAuth()
  const params = useParams()
  const router = useRouter()
  const applicationId = params.id as string

  const [application, setApplication] = useState<MouApplication | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [submitting, setSubmitting] = useState(false)

  useEffect(() => {
    if (applicationId) {
      loadApplication()
    }
  }, [applicationId])

  const loadApplication = async () => {
    try {
      setLoading(true)
      const data = await mouApplicationService.getApplication(applicationId)
      setApplication(data)
    } catch (err: any) {
      console.error("Failed to load application:", err)
      setError("Failed to load application")
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async () => {
    if (!application) return

    if (confirm(`Are you sure you want to submit application ${application.mouApplicationId} for review?`)) {
      try {
        setSubmitting(true)
        await mouApplicationService.submitApplication(application.id)
        setSuccess("Application submitted for review successfully")
        await loadApplication()
      } catch (err: any) {
        setError(err.response?.data?.message || "Failed to submit application")
      } finally {
        setSubmitting(false)
      }
    }
  }

  const getApplicationStatus = (app: MouApplication) => {
    if (!app.approvalSteps || app.approvalSteps.length === 0) {
      return 'draft'
    }
    
    const latestStep = app.approvalSteps.sort((a, b) => b.stepNumber - a.stepNumber)[0]
    return latestStep.status.toLowerCase()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-cyan-600" />
      </div>
    )
  }

  if (error && !application) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button asChild>
          <Link href="/dashboard/partner/applications">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Applications
          </Link>
        </Button>
      </div>
    )
  }

  if (!application) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertDescription>Application not found</AlertDescription>
        </Alert>
        <Button asChild>
          <Link href="/dashboard/partner/applications">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Applications
          </Link>
        </Button>
      </div>
    )
  }

  const status = getApplicationStatus(application)
  const isDraft = status === 'draft'

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button asChild variant="ghost" size="sm">
              <Link href="/dashboard/partner/applications">
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Applications
              </Link>
            </Button>
          </div>
          <h2 className="text-3xl font-bold tracking-tight text-cyan-900">
            Application {application.mouApplicationId}
          </h2>
          <p className="text-muted-foreground">View and manage your MoU application</p>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge className={getStatusColor(status)}>
            <div className="flex items-center gap-1">
              {getStatusIcon(status)}
              <span className="capitalize">{status}</span>
            </div>
          </Badge>
          
          {isDraft && (
            <>
              <Button asChild variant="outline">
                <Link href={`/dashboard/partner/applications/${application.id}/edit`}>
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Link>
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={submitting}
                className="bg-cyan-600 hover:bg-cyan-700"
              >
                {submitting ? (
                  <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                ) : (
                  <Send className="h-4 w-4 mr-1" />
                )}
                Submit for Review
              </Button>
            </>
          )}
        </div>
      </div>

      {success && (
        <Alert className="bg-green-50 text-green-700 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Application Details */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-cyan-600" />
              Application Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Application ID</label>
              <p className="font-medium">{application.mouApplicationId}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-muted-foreground">MoU Party</label>
              <p className="font-medium">{application.mou?.party?.partyName || 'N/A'}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-muted-foreground">Created</label>
              <p className="font-medium">{format(new Date(application.createdAt), 'PPP')}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
              <p className="font-medium">{format(new Date(application.updatedAt), 'PPP')}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5 text-cyan-600" />
              Organization Responsibility
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm leading-relaxed">
              {application.responsibility || 'No responsibility description provided.'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Projects */}
      {application.projects && application.projects.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-cyan-600" />
              Projects ({application.projects.length})
            </CardTitle>
            <CardDescription>
              Projects to be implemented under this MoU
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {application.projects.map((project, index) => (
              <div key={project.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold">{project.projectName}</h4>
                  <span className="text-sm text-muted-foreground">Project {index + 1}</span>
                </div>
                
                {project.projectDescription && (
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {project.projectDescription}
                  </p>
                )}
                
                <div className="grid gap-4 md:grid-cols-3 text-sm">
                  {project.startDate && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>Start: {format(new Date(project.startDate), 'MMM dd, yyyy')}</span>
                    </div>
                  )}
                  
                  {project.endDate && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>End: {format(new Date(project.endDate), 'MMM dd, yyyy')}</span>
                    </div>
                  )}
                  
                  {project.totalBudget && (
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <span>Budget: {project.totalBudget.toLocaleString()} {project.currency || 'USD'}</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Approval Steps */}
      {application.approvalSteps && application.approvalSteps.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5 text-cyan-600" />
              Review Process
            </CardTitle>
            <CardDescription>
              Application review steps and feedback
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {application.approvalSteps
              .sort((a, b) => a.stepNumber - b.stepNumber)
              .map((step) => (
                <div key={step.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Step {step.stepNumber}</span>
                      <Badge className={getStatusColor(step.status)}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(step.status)}
                          <span className="capitalize">{step.status.replace('_', ' ')}</span>
                        </div>
                      </Badge>
                    </div>
                    <span className="text-sm text-muted-foreground">{step.reviewerRole}</span>
                  </div>
                  
                  {step.reviewer && (
                    <div className="flex items-center gap-2 text-sm">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span>Reviewed by: {step.reviewer.firstName} {step.reviewer.lastName}</span>
                    </div>
                  )}
                  
                  {step.reviewedAt && (
                    <div className="flex items-center gap-2 text-sm">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>Reviewed on: {format(new Date(step.reviewedAt), 'PPP')}</span>
                    </div>
                  )}
                  
                  {step.comments && (
                    <div className="bg-gray-50 rounded p-3">
                      <label className="text-sm font-medium text-muted-foreground">Comments:</label>
                      <p className="text-sm mt-1">{step.comments}</p>
                    </div>
                  )}
                </div>
              ))}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
