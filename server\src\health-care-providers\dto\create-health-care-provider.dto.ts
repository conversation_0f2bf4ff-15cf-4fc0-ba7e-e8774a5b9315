import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsEmail, IsPhoneNumber } from 'class-validator';

export class CreateHealthCareProviderDto {
  @ApiProperty({
    description: 'Name of the health care provider',
    example: 'Rwanda Biomedical Center',
  })
  @IsString()
  @IsNotEmpty()
  providerName: string;

  @ApiProperty({
    description: 'Description of the health care provider',
    example: 'National reference laboratory and biomedical research institution',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Location of the health care provider',
    example: 'Kigali, Rwanda',
    required: false,
  })
  @IsString()
  @IsOptional()
  location?: string;

  @ApiProperty({
    description: 'Contact email of the health care provider',
    example: '<EMAIL>',
    required: false,
  })
  @IsEmail()
  @IsOptional()
  contactEmail?: string;

  @ApiProperty({
    description: 'Contact phone number of the health care provider',
    example: '+250788123456',
    required: false,
  })
  @IsString()
  @IsOptional()
  contactPhone?: string;

  @ApiProperty({
    description: 'Website URL of the health care provider',
    example: 'https://rbc.gov.rw',
    required: false,
  })
  @IsString()
  @IsOptional()
  website?: string;
}
