import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { PartiesService } from './parties.service';
import { CreatePartyDto, UpdatePartyDto, PartyResponseDto, PartyWithRelationsDto } from './dto/create-party.dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { RolesGuard } from '../auth/guard/roles.guard';
import { Roles } from '../auth/decorator/roles.decorator';
import { UserRole } from '../auth/dto';

@ApiTags('Parties')
@ApiBearerAuth()
@UseGuards(JwtGuard, RolesGuard)
@Controller('parties')
export class PartiesController {
  constructor(private readonly partiesService: PartiesService) {}

  @Post()
  @Roles(UserRole.PARTNER, UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new party' })
  @ApiResponse({
    status: 201,
    description: 'Party created successfully',
    type: PartyWithRelationsDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Related entity not found' })
  @ApiResponse({ status: 409, description: 'Party ID already exists' })
  async create(@Body() createPartyDto: CreatePartyDto) {
    return this.partiesService.create(createPartyDto);
  }

  @Get()
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get all parties with pagination' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10)' })
  @ApiQuery({ name: 'organizationId', required: false, type: Number, description: 'Filter by organization ID' })
  @ApiResponse({
    status: 200,
    description: 'List of parties',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/PartyWithRelationsDto' },
        },
        meta: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            page: { type: 'number' },
            limit: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
      },
    },
  })
  async findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('organizationId') organizationId?: string,
  ) {
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 10;
    const orgIdNum = organizationId ? parseInt(organizationId, 10) : undefined;

    return this.partiesService.findAll(pageNum, limitNum, orgIdNum);
  }

  @Get('by-organization/:organizationId')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get parties by organization ID' })
  @ApiResponse({
    status: 200,
    description: 'List of parties for the organization',
    type: [PartyWithRelationsDto],
  })
  async getPartiesByOrganization(@Param('organizationId', ParseIntPipe) organizationId: number) {
    return this.partiesService.getPartiesByOrganization(organizationId);
  }

  @Get('by-party-id/:partyId')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get a party by party ID' })
  @ApiResponse({
    status: 200,
    description: 'Party details',
    type: PartyWithRelationsDto,
  })
  @ApiResponse({ status: 404, description: 'Party not found' })
  async findByPartyId(@Param('partyId') partyId: string) {
    return this.partiesService.findByPartyId(partyId);
  }

  @Get(':id')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get a party by ID' })
  @ApiResponse({
    status: 200,
    description: 'Party details',
    type: PartyWithRelationsDto,
  })
  @ApiResponse({ status: 404, description: 'Party not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.partiesService.findOne(id);
  }

  @Patch(':id')
  @Roles(UserRole.PARTNER, UserRole.ADMIN)
  @ApiOperation({ summary: 'Update a party' })
  @ApiResponse({
    status: 200,
    description: 'Party updated successfully',
    type: PartyWithRelationsDto,
  })
  @ApiResponse({ status: 404, description: 'Party not found' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePartyDto: UpdatePartyDto,
  ) {
    return this.partiesService.update(id, updatePartyDto);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete a party (soft delete)' })
  @ApiResponse({
    status: 200,
    description: 'Party deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Party not found' })
  @ApiResponse({ status: 409, description: 'Party has an associated MoU' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.partiesService.remove(id);
  }
}
