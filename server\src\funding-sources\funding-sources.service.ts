import { Injectable, NotFoundException, ConflictException, ForbiddenException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateFundingSourceDto, UpdateFundingSourceDto } from './dto';

@Injectable()
export class FundingSourcesService {
    private readonly logger = new Logger(FundingSourcesService.name);

    constructor(private prisma: PrismaService) {}

    async create(createFundingSourceDto: CreateFundingSourceDto, userId: string) {
        try {
            // Check if user has admin role
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            if (user.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can create funding sources');
            }

            // Check if funding source already exists
            const existing = await this.prisma.fundingSource.findFirst({
                where: {
                    sourceName: createFundingSourceDto.sourceName,
                    deleted: false
                }
            });

            if (existing) {
                throw new ConflictException('Funding source with this name already exists');
            }

            // Create the funding source
            const fundingSource = await this.prisma.fundingSource.create({
                data: {
                    sourceName: createFundingSourceDto.sourceName
                }
            });

            return fundingSource;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to create funding source', error.stack);
            throw new Error('Failed to create funding source');
        }
    }

    async findAll() {
        return this.prisma.fundingSource.findMany({
            where: {
                deleted: false
            },
            orderBy: {
                sourceName: 'asc'
            }
        });
    }

    async findOne(id: number) {
        const fundingSource = await this.prisma.fundingSource.findFirst({
            where: {
                id,
                deleted: false
            }
        });

        if (!fundingSource) {
            throw new NotFoundException('Funding source not found');
        }

        return fundingSource;
    }

    async update(id: number, updateFundingSourceDto: UpdateFundingSourceDto, userId: string) {
        try {
            // Check if user has admin role
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            if (user.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can update funding sources');
            }

            // Check if funding source exists
            const existing = await this.prisma.fundingSource.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });

            if (!existing) {
                throw new NotFoundException('Funding source not found');
            }

            // Check if name is being changed and if so, if it conflicts
            if (updateFundingSourceDto.sourceName && updateFundingSourceDto.sourceName !== existing.sourceName) {
                const nameConflict = await this.prisma.fundingSource.findFirst({
                    where: {
                        sourceName: updateFundingSourceDto.sourceName,
                        deleted: false,
                        id: { not: id }
                    }
                });

                if (nameConflict) {
                    throw new ConflictException('Another funding source with this name already exists');
                }
            }

            // Update the funding source
            const updated = await this.prisma.fundingSource.update({
                where: { id },
                data: {
                    sourceName: updateFundingSourceDto.sourceName
                }
            });

            return updated;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to update funding source', error.stack);
            throw new Error('Failed to update funding source');
        }
    }

    async remove(id: number, userId: string) {
        try {
            // Check if user has admin role
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            if (user.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can delete funding sources');
            }

            // Check if funding source exists
            const existing = await this.prisma.fundingSource.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });

            if (!existing) {
                throw new NotFoundException('Funding source not found');
            }

            // Check if funding source is being used
            const inUse = await this.prisma.project.findFirst({
                where: {
                    fundingSourceId: id,
                    deleted: false
                }
            });

            if (inUse) {
                throw new ConflictException('Cannot delete funding source that is being used by projects');
            }

            // Soft delete the funding source
            await this.prisma.fundingSource.update({
                where: { id },
                data: { deleted: true }
            });

            return { success: true };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to delete funding source', error.stack);
            throw new Error('Failed to delete funding source');
        }
    }
}
