import { Module } from '@nestjs/common';
import { HealthCareProvidersService } from './health-care-providers.service';
import { HealthCareProvidersController } from './health-care-providers.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [HealthCareProvidersController],
  providers: [HealthCareProvidersService],
  exports: [HealthCareProvidersService],
})
export class HealthCareProvidersModule {}
