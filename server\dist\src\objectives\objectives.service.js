"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObjectivesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let ObjectivesService = class ObjectivesService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createObjectiveDto) {
        try {
            const objective = await this.prisma.objective.create({
                data: createObjectiveDto,
            });
            return objective;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException('Objective name must be unique');
                }
            }
            throw error;
        }
    }
    async findAll() {
        const objectives = await this.prisma.objective.findMany({
            where: {
                deleted: false,
            },
            orderBy: {
                name: 'asc',
            },
        });
        return objectives;
    }
    async findOne(id) {
        const objective = await this.prisma.objective.findFirst({
            where: {
                id,
                deleted: false,
            },
            include: {
                parties: {
                    include: {
                        organization: true,
                    },
                },
            },
        });
        if (!objective) {
            throw new common_1.NotFoundException(`Objective with ID ${id} not found`);
        }
        return objective;
    }
    async update(id, updateObjectiveDto) {
        const existingObjective = await this.prisma.objective.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingObjective) {
            throw new common_1.NotFoundException(`Objective with ID ${id} not found`);
        }
        try {
            const updatedObjective = await this.prisma.objective.update({
                where: { id },
                data: updateObjectiveDto,
            });
            return updatedObjective;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException('Objective name must be unique');
                }
            }
            throw error;
        }
    }
    async remove(id) {
        const existingObjective = await this.prisma.objective.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingObjective) {
            throw new common_1.NotFoundException(`Objective with ID ${id} not found`);
        }
        const partiesUsingObjective = await this.prisma.party.findMany({
            where: {
                objectiveId: id,
                deleted: false,
            },
        });
        if (partiesUsingObjective.length > 0) {
            throw new common_1.ConflictException('Cannot delete objective that is being used by parties');
        }
        await this.prisma.objective.update({
            where: { id },
            data: { deleted: true },
        });
        return { message: 'Objective deleted successfully' };
    }
};
exports.ObjectivesService = ObjectivesService;
exports.ObjectivesService = ObjectivesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ObjectivesService);
//# sourceMappingURL=objectives.service.js.map