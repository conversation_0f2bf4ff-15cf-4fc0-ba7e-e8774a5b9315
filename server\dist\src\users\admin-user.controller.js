"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminUserController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const admin_user_service_1 = require("./admin-user.service");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const client_1 = require("@prisma/client");
const user_interface_1 = require("./interfaces/user.interface");
const admin_user_dto_1 = require("./dto/admin-user.dto");
let AdminUserController = class AdminUserController {
    constructor(adminUserService) {
        this.adminUserService = adminUserService;
    }
    async createUser(createUserDto) {
        return this.adminUserService.createUser(createUserDto);
    }
    async findAll(filters) {
        return this.adminUserService.findAllUsers(filters);
    }
    async findOne(id) {
        return this.adminUserService.findOne(id);
    }
    async updateUser(id, updateUserDto) {
        return this.adminUserService.updateUser(id, updateUserDto);
    }
    async assignRoles(id, assignRolesDto) {
        return this.adminUserService.assignRoles(id, assignRolesDto);
    }
    async getUserActivity(id, activityDto) {
        activityDto.userId = id;
        return this.adminUserService.getUserActivity(activityDto);
    }
    async bulkUpdateUsers(bulkActionDto) {
        return this.adminUserService.bulkUpdateUsers(bulkActionDto);
    }
    async resetUserPassword(id, newPassword) {
        return this.adminUserService.resetUserPassword(id, newPassword);
    }
    async deactivateUser(id) {
        return this.adminUserService.deactivateUser(id);
    }
    async reactivateUser(id) {
        return this.adminUserService.reactivateUser(id);
    }
    async getUserRoleStatistics() {
        return {};
    }
    async getActivityStatistics() {
        return {};
    }
    async getDepartments() {
        return {};
    }
    async getOrganizations() {
        return {};
    }
    async importUsers(usersData) {
        return {};
    }
    async exportUsers(filters) {
        return {};
    }
    async assignOrganization(id, organizationId) {
        return {};
    }
    async assignDepartment(id, department) {
        return {};
    }
    async getAuditLog(filters) {
        return { data: [], meta: { total: 0, page: 1, limit: 10 } };
    }
    async notifyUsers(notification) {
        return {};
    }
    async getPermissions() {
        return {};
    }
    async updateUserPermissions(id, permissions) {
        return { message: 'Permissions updated successfully' };
    }
};
exports.AdminUserController = AdminUserController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create new user' }),
    (0, swagger_1.ApiResponse)({ status: 201, type: user_interface_1.UserResponse }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [admin_user_dto_1.CreateUserDto]),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "createUser", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [admin_user_dto_1.FilterUsersDto]),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, admin_user_dto_1.UpdateUserDto]),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "updateUser", null);
__decorate([
    (0, common_1.Patch)(':id/roles'),
    (0, swagger_1.ApiOperation)({ summary: 'Assign roles to user' }),
    (0, swagger_1.ApiResponse)({ status: 200, type: user_interface_1.UserResponse }),
    (0, roles_guard_1.Roles)(client_1.UserRole.ADMIN),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, admin_user_dto_1.AssignRolesDto]),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "assignRoles", null);
__decorate([
    (0, common_1.Get)(':id/activity'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, admin_user_dto_1.UserActivityDto]),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "getUserActivity", null);
__decorate([
    (0, common_1.Post)('bulk-update'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [admin_user_dto_1.BulkUserActionDto]),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "bulkUpdateUsers", null);
__decorate([
    (0, common_1.Post)(':id/reset-password'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)('newPassword')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "resetUserPassword", null);
__decorate([
    (0, common_1.Patch)(':id/deactivate'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "deactivateUser", null);
__decorate([
    (0, common_1.Patch)(':id/reactivate'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "reactivateUser", null);
__decorate([
    (0, common_1.Get)('statistics/roles'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "getUserRoleStatistics", null);
__decorate([
    (0, common_1.Get)('statistics/activity'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "getActivityStatistics", null);
__decorate([
    (0, common_1.Get)('departments'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "getDepartments", null);
__decorate([
    (0, common_1.Get)('organizations'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "getOrganizations", null);
__decorate([
    (0, common_1.Post)('import'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "importUsers", null);
__decorate([
    (0, common_1.Get)('export'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [admin_user_dto_1.FilterUsersDto]),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "exportUsers", null);
__decorate([
    (0, common_1.Post)(':id/assign-organization'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)('organizationId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "assignOrganization", null);
__decorate([
    (0, common_1.Post)(':id/assign-department'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)('department')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "assignDepartment", null);
__decorate([
    (0, common_1.Get)('audit-log'),
    (0, swagger_1.ApiOperation)({ summary: 'Get user management audit log' }),
    (0, swagger_1.ApiResponse)({ status: 200, type: user_interface_1.AuditLogResponse }),
    (0, roles_guard_1.Roles)(client_1.UserRole.ADMIN),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "getAuditLog", null);
__decorate([
    (0, common_1.Post)('notify'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "notifyUsers", null);
__decorate([
    (0, common_1.Get)('permissions'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "getPermissions", null);
__decorate([
    (0, common_1.Post)(':id/permissions'),
    (0, swagger_1.ApiOperation)({ summary: 'Update user permissions' }),
    (0, swagger_1.ApiResponse)({ status: 200, type: user_interface_1.UserResponse }),
    (0, roles_guard_1.Roles)(client_1.UserRole.ADMIN),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], AdminUserController.prototype, "updateUserPermissions", null);
exports.AdminUserController = AdminUserController = __decorate([
    (0, common_1.Controller)('admin/users'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard, roles_guard_1.RolesGuard),
    (0, roles_guard_1.Roles)(client_1.UserRole.ADMIN),
    (0, swagger_1.ApiTags)('User Administration'),
    __metadata("design:paramtypes", [admin_user_service_1.AdminUserService])
], AdminUserController);
//# sourceMappingURL=admin-user.controller.js.map