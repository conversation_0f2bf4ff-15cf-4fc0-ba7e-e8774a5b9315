import api from "../api"

export interface Organization {
  id: string
  organizationName: string
  organizationRegistrationNumber: string
  organizationPhoneNumber: string
  organizationEmail: string
  organizationWebsite?: string
  homeCountryRepresentative: string
  rwandaRepresentative: string
  organizationRgbNumber: string
  organizationTypeId: number
  organizationType?: OrganizationType
  addresses: Array<{
    id: string
    addressType: "HEADQUARTERS" | "RWANDA"
    country: string
    province?: string
    district?: string
    sector?: string
    cell?: string
    village?: string
    street: string
    avenue?: string
    poBox: string
    postalCode?: string
  }>
  createdAt: string
  updatedAt: string
}

export interface OrganizationType {
  id: number
  typeName: string
  createAt: string
  updatedAt: string
}

export interface CreateOrganizationRequest {
  organizationName: string
  organizationRegistrationNumber: string
  organizationPhoneNumber: string
  organizationEmail: string
  organizationWebsite?: string
  homeCountryRepresentative: string
  rwandaRepresentative: string
  organizationRgbNumber: string
  organizationTypeId: number
  addresses: Array<{
    addressType: "HEADQUARTERS" | "RWANDA"
    country: string
    province?: string
    district?: string
    sector?: string
    cell?: string
    village?: string
    street: string
    avenue?: string
    poBox: string
    postalCode?: string
  }>
}

export const organizationService = {
  async getOrganizations(): Promise<Organization[]> {
    const response = await api.get("/organizations")
    return response.data
  },

  async getOrganization(id: string): Promise<Organization> {
    const response = await api.get(`/organizations/${id}`)
    return response.data
  },

  async createOrganization(data: CreateOrganizationRequest): Promise<Organization> {
    const response = await api.post("/organizations", data)
    return response.data
  },

  async updateOrganization(id: string, data: Partial<CreateOrganizationRequest>): Promise<Organization> {
    const response = await api.patch(`/organizations/${id}`, data)
    return response.data
  },

  async deleteOrganization(id: string): Promise<void> {
    await api.delete(`/organizations/${id}`)
  },

  async getOrganizationTypes(): Promise<OrganizationType[]> {
    const response = await api.get("/organization-types")
    return response.data
  },

  async createOrganizationType(typeName: string): Promise<OrganizationType> {
    const response = await api.post("/organization-types", { typeName })
    return response.data
  },

  async updateOrganizationType(id: number, typeName: string): Promise<OrganizationType> {
    const response = await api.patch(`/organization-types/${id}`, { typeName })
    return response.data
  },

  async deleteOrganizationType(id: number): Promise<void> {
    await api.delete(`/organization-types/${id}`)
  },
}
