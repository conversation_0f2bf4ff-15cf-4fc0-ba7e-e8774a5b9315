"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoalsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let GoalsService = class GoalsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createGoalDto) {
        try {
            const goal = await this.prisma.goal.create({
                data: createGoalDto,
            });
            return goal;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException('Goal name must be unique');
                }
            }
            throw error;
        }
    }
    async findAll() {
        const goals = await this.prisma.goal.findMany({
            where: {
                deleted: false,
            },
            orderBy: {
                name: 'asc',
            },
        });
        return goals;
    }
    async findOne(id) {
        const goal = await this.prisma.goal.findFirst({
            where: {
                id,
                deleted: false,
            },
            include: {
                parties: {
                    include: {
                        organization: true,
                    },
                },
            },
        });
        if (!goal) {
            throw new common_1.NotFoundException(`Goal with ID ${id} not found`);
        }
        return goal;
    }
    async update(id, updateGoalDto) {
        const existingGoal = await this.prisma.goal.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingGoal) {
            throw new common_1.NotFoundException(`Goal with ID ${id} not found`);
        }
        try {
            const updatedGoal = await this.prisma.goal.update({
                where: { id },
                data: updateGoalDto,
            });
            return updatedGoal;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException('Goal name must be unique');
                }
            }
            throw error;
        }
    }
    async remove(id) {
        const existingGoal = await this.prisma.goal.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingGoal) {
            throw new common_1.NotFoundException(`Goal with ID ${id} not found`);
        }
        const partiesUsingGoal = await this.prisma.party.findMany({
            where: {
                goalId: id,
                deleted: false,
            },
        });
        if (partiesUsingGoal.length > 0) {
            throw new common_1.ConflictException('Cannot delete goal that is being used by parties');
        }
        await this.prisma.goal.update({
            where: { id },
            data: { deleted: true },
        });
        return { message: 'Goal deleted successfully' };
    }
};
exports.GoalsService = GoalsService;
exports.GoalsService = GoalsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], GoalsService);
//# sourceMappingURL=goals.service.js.map