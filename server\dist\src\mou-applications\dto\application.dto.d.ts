import { ApplicationStatus, ModificationSource } from '@prisma/client';
declare enum ReviewDecisionType {
    APPROVE = "APPROVE",
    REJECT = "REJECT",
    REQUEST_MODIFICATIONS = "REQUEST_MODIFICATIONS"
}
export declare class CreateApplicationDto {
    title: string;
    partnerId: number;
    tags?: string;
    priority?: number;
}
export declare class UpdateApplicationDto {
    title?: string;
    status?: ApplicationStatus;
    currentPhase?: string;
    phaseDueDate?: Date;
    tags?: string;
    priority?: number;
}
export declare class AssignTechnicalExpertDto {
    expertId: number;
    dueDate?: Date;
    priority?: number;
    specialInstructions?: string;
}
export declare class ReviewDecisionDto {
    decision: ReviewDecisionType;
    comments: string;
    attachments?: string;
    checklist?: string;
    riskAssessment?: string;
}
export declare class ModificationRequestDto {
    source: ModificationSource;
    details: string;
    priority?: number;
    dueDate?: Date;
}
export declare class FilterApplicationsDto {
    status?: ApplicationStatus[];
    search?: string;
    partnerId?: number;
    startDate?: Date;
    endDate?: Date;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export declare class ApplicationVersionDto {
    version: number;
    previousVersionId?: number;
}
export {};
