import { Injectable, NotFoundException, ConflictException, ForbiddenException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateOrganizationTypeDto, UpdateOrganizationTypeDto } from './dto';

@Injectable()
export class OrganizationTypesService {
    private readonly logger = new Logger(OrganizationTypesService.name);

    constructor(private prisma: PrismaService) {}

    async findAll() {
        try {
            const organizationTypes = await this.prisma.organizationType.findMany({
                where: { deleted: false },
                orderBy: { createdAt: 'desc' }
            });

            return organizationTypes;
        } catch (error) {
            this.logger.error('Failed to fetch organization types', error.stack);
            throw new Error('Failed to fetch organization types');
        }
    }

    async findOne(id: number) {
        try {
            const organizationType = await this.prisma.organizationType.findUnique({
                where: { id, deleted: false }
            });

            if (!organizationType) {
                throw new NotFoundException('Organization type not found');
            }

            return organizationType;
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            this.logger.error(`Failed to fetch organization type with ID ${id}`, error.stack);
            throw new Error('Failed to fetch organization type');
        }
    }

    async create(createOrganizationTypeDto: CreateOrganizationTypeDto, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can create organization types
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can create organization types');
            }

            // Check if organization type with same name already exists
            const existingType = await this.prisma.organizationType.findFirst({
                where: { 
                    typeName: createOrganizationTypeDto.typeName,
                    deleted: false 
                }
            });

            if (existingType) {
                throw new ConflictException('Organization type with this name already exists');
            }

            const organizationType = await this.prisma.organizationType.create({
                data: {
                    typeName: createOrganizationTypeDto.typeName
                }
            });

            return organizationType;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to create organization type', error.stack);
            throw new Error('Failed to create organization type');
        }
    }

    async update(id: number, updateOrganizationTypeDto: UpdateOrganizationTypeDto, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can update organization types
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can update organization types');
            }

            // Check if organization type exists
            const existingType = await this.prisma.organizationType.findUnique({
                where: { id, deleted: false }
            });

            if (!existingType) {
                throw new NotFoundException('Organization type not found');
            }

            // Check if new name conflicts with existing types
            if (updateOrganizationTypeDto.typeName && updateOrganizationTypeDto.typeName !== existingType.typeName) {
                const nameConflict = await this.prisma.organizationType.findFirst({
                    where: { 
                        typeName: updateOrganizationTypeDto.typeName,
                        deleted: false,
                        id: { not: id }
                    }
                });

                if (nameConflict) {
                    throw new ConflictException('Organization type with this name already exists');
                }
            }

            const organizationType = await this.prisma.organizationType.update({
                where: { id },
                data: updateOrganizationTypeDto
            });

            return organizationType;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to update organization type with ID ${id}`, error.stack);
            throw new Error('Failed to update organization type');
        }
    }

    async remove(id: number, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can delete organization types
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can delete organization types');
            }

            // Check if organization type exists
            const existingType = await this.prisma.organizationType.findUnique({
                where: { id, deleted: false }
            });

            if (!existingType) {
                throw new NotFoundException('Organization type not found');
            }

            // Check if organization type is being used by any organizations
            const organizationsUsingType = await this.prisma.organization.findFirst({
                where: { 
                    organizationTypeId: id,
                    deleted: false 
                }
            });

            if (organizationsUsingType) {
                throw new ConflictException('Cannot delete organization type that is being used by organizations');
            }

            // Soft delete the organization type
            await this.prisma.organizationType.update({
                where: { id },
                data: { deleted: true }
            });

            return { message: 'Organization type deleted successfully' };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to delete organization type with ID ${id}`, error.stack);
            throw new Error('Failed to delete organization type');
        }
    }
}
