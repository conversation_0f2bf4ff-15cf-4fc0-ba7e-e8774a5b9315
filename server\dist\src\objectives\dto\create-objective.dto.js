"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObjectiveResponseDto = exports.UpdateObjectiveDto = exports.CreateObjectiveDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateObjectiveDto {
}
exports.CreateObjectiveDto = CreateObjectiveDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Objective name',
        example: 'Improve Primary Healthcare Access',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateObjectiveDto.prototype, "name", void 0);
class UpdateObjectiveDto {
}
exports.UpdateObjectiveDto = UpdateObjectiveDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Objective name',
        example: 'Enhanced Primary Healthcare Access',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UpdateObjectiveDto.prototype, "name", void 0);
class ObjectiveResponseDto {
}
exports.ObjectiveResponseDto = ObjectiveResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Objective ID', example: 1 }),
    __metadata("design:type", Number)
], ObjectiveResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Objective name', example: 'Improve Primary Healthcare Access' }),
    __metadata("design:type", String)
], ObjectiveResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation date' }),
    __metadata("design:type", Date)
], ObjectiveResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update date' }),
    __metadata("design:type", Date)
], ObjectiveResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Soft delete flag' }),
    __metadata("design:type", Boolean)
], ObjectiveResponseDto.prototype, "deleted", void 0);
//# sourceMappingURL=create-objective.dto.js.map