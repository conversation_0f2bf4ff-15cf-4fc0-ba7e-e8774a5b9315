import { PrismaService } from '../prisma/prisma.service';
import { CreateFundingSourceDto, UpdateFundingSourceDto } from './dto';
export declare class FundingSourcesService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    create(createFundingSourceDto: CreateFundingSourceDto, userId: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        sourceName: string;
    }>;
    findAll(): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        sourceName: string;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        sourceName: string;
    }>;
    update(id: number, updateFundingSourceDto: UpdateFundingSourceDto, userId: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        sourceName: string;
    }>;
    remove(id: number, userId: string): Promise<{
        success: boolean;
    }>;
}
