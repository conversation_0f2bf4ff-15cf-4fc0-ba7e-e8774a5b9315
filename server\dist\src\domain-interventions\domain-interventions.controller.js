"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DomainInterventionsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const domain_interventions_service_1 = require("./domain-interventions.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
const public_decorator_1 = require("../auth/decorator/public.decorator");
let DomainInterventionsController = class DomainInterventionsController {
    constructor(domainInterventionsService) {
        this.domainInterventionsService = domainInterventionsService;
    }
    async create(createDomainInterventionDto, req) {
        return this.domainInterventionsService.create(createDomainInterventionDto, req.user.sub);
    }
    async findAll() {
        return this.domainInterventionsService.findAll();
    }
    async findTree() {
        return this.domainInterventionsService.findTree();
    }
    async findOne(id) {
        return this.domainInterventionsService.findOne(id);
    }
    async update(id, updateDomainInterventionDto, req) {
        return this.domainInterventionsService.update(id, updateDomainInterventionDto, req.user.sub);
    }
    async remove(id, req) {
        return this.domainInterventionsService.remove(id, req.user.sub);
    }
};
exports.DomainInterventionsController = DomainInterventionsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new domain intervention (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Domain intervention created successfully', type: dto_1.DomainInterventionResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - Invalid parent domain' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateDomainInterventionDto, Object]),
    __metadata("design:returntype", Promise)
], DomainInterventionsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all domain interventions with hierarchy' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns list of domain interventions', type: [dto_1.DomainInterventionResponseDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DomainInterventionsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('tree'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get domain interventions as tree structure (root domains with sub-domains)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns tree structure of domain interventions', type: [dto_1.DomainInterventionResponseDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DomainInterventionsController.prototype, "findTree", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get domain intervention by ID with hierarchy' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns domain intervention details', type: dto_1.DomainInterventionResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Domain intervention not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DomainInterventionsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update domain intervention (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Domain intervention updated successfully', type: dto_1.DomainInterventionResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - Invalid parent domain or circular reference' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Domain intervention not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateDomainInterventionDto, Object]),
    __metadata("design:returntype", Promise)
], DomainInterventionsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Delete domain intervention (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Domain intervention deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - Domain has sub-domains' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Domain intervention not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], DomainInterventionsController.prototype, "remove", null);
exports.DomainInterventionsController = DomainInterventionsController = __decorate([
    (0, swagger_1.ApiTags)('domain-interventions'),
    (0, common_1.Controller)('domain-interventions'),
    __metadata("design:paramtypes", [domain_interventions_service_1.DomainInterventionsService])
], DomainInterventionsController);
//# sourceMappingURL=domain-interventions.controller.js.map