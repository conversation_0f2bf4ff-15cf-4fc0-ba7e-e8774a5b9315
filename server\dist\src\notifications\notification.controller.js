"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationController = void 0;
const common_1 = require("@nestjs/common");
const websockets_1 = require("@nestjs/websockets");
const socket_io_1 = require("socket.io");
const notification_service_1 = require("./notification.service");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
let NotificationController = class NotificationController {
    constructor(notificationService) {
        this.notificationService = notificationService;
        this.userSockets = new Map();
    }
    async handleConnection(client) {
        const token = client.handshake.auth.token;
        if (!token) {
            client.disconnect();
            return;
        }
        try {
            const userId = this.getUserIdFromToken(token);
            const userSockets = this.userSockets.get(userId) || [];
            userSockets.push(client.id);
            this.userSockets.set(userId, userSockets);
            client.join(`user-${userId}`);
            const unreadCount = await this.notificationService.getUnreadCount(userId);
            client.emit('unreadCount', unreadCount);
        }
        catch (error) {
            client.disconnect();
        }
    }
    handleDisconnect(client) {
        for (const [userId, sockets] of this.userSockets.entries()) {
            const index = sockets.indexOf(client.id);
            if (index !== -1) {
                sockets.splice(index, 1);
                if (sockets.length === 0) {
                    this.userSockets.delete(userId);
                }
                else {
                    this.userSockets.set(userId, sockets);
                }
                break;
            }
        }
    }
    async getUserNotifications(req, skip = 0, take = 10, includeRead = false) {
        return this.notificationService.getUserNotifications(req.user.id, {
            skip,
            take,
            includeRead
        });
    }
    async getUnreadCount(req) {
        return {
            count: await this.notificationService.getUnreadCount(req.user.id)
        };
    }
    async markAsRead(id, req) {
        await this.notificationService.markNotificationAsRead(id, req.user.id);
        const unreadCount = await this.notificationService.getUnreadCount(req.user.id);
        this.server.to(`user-${req.user.id}`).emit('unreadCount', unreadCount);
        return { success: true };
    }
    async markAllRead(req) {
        const notifications = await this.notificationService.getUserNotifications(req.user.id, {
            includeRead: false
        });
        for (const notification of notifications) {
            await this.notificationService.markNotificationAsRead(notification.id, req.user.id);
        }
        this.server.to(`user-${req.user.id}`).emit('unreadCount', 0);
        return { success: true };
    }
    handleSubscribe(client) {
        const userId = this.getUserIdFromSocket(client);
        if (userId) {
            client.join(`user-${userId}`);
        }
    }
    handleUnsubscribe(client) {
        const userId = this.getUserIdFromSocket(client);
        if (userId) {
            client.leave(`user-${userId}`);
        }
    }
    async sendNotificationToUser(userId, notification) {
        const storedNotification = await this.notificationService.createNotification({
            userId,
            title: notification.title,
            message: notification.message,
            type: notification.type,
            applicationId: notification.applicationId,
        });
        this.server.to(`user-${userId}`).emit('notification', storedNotification);
        const unreadCount = await this.notificationService.getUnreadCount(userId);
        this.server.to(`user-${userId}`).emit('unreadCount', unreadCount);
    }
    async sendNotificationToRole(role, notification) {
        const users = await this.getUsersByRole(role);
        for (const user of users) {
            await this.sendNotificationToUser(user.id, notification);
        }
    }
    getUserIdFromToken(token) {
        return 0;
    }
    getUserIdFromSocket(client) {
        return 0;
    }
    async getUsersByRole(role) {
        return [];
    }
};
exports.NotificationController = NotificationController;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], NotificationController.prototype, "server", void 0);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('skip', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Query)('take', common_1.ParseIntPipe)),
    __param(3, (0, common_1.Query)('includeRead')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number, Boolean]),
    __metadata("design:returntype", Promise)
], NotificationController.prototype, "getUserNotifications", null);
__decorate([
    (0, common_1.Get)('unread-count'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], NotificationController.prototype, "getUnreadCount", null);
__decorate([
    (0, common_1.Post)(':id/mark-read'),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], NotificationController.prototype, "markAsRead", null);
__decorate([
    (0, common_1.Post)('mark-all-read'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], NotificationController.prototype, "markAllRead", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('subscribe'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket]),
    __metadata("design:returntype", void 0)
], NotificationController.prototype, "handleSubscribe", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('unsubscribe'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket]),
    __metadata("design:returntype", void 0)
], NotificationController.prototype, "handleUnsubscribe", null);
exports.NotificationController = NotificationController = __decorate([
    (0, websockets_1.WebSocketGateway)({
        namespace: '/notifications',
        cors: {
            origin: process.env.CLIENT_URL,
            credentials: true
        }
    }),
    (0, common_1.Controller)('notifications'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [notification_service_1.NotificationService])
], NotificationController);
//# sourceMappingURL=notification.controller.js.map