"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponsibilitiesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let ResponsibilitiesService = class ResponsibilitiesService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createResponsibilityDto) {
        try {
            const responsibility = await this.prisma.responsibility.create({
                data: createResponsibilityDto,
            });
            return responsibility;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException('Responsibility name must be unique');
                }
            }
            throw error;
        }
    }
    async findAll() {
        const responsibilities = await this.prisma.responsibility.findMany({
            where: {
                deleted: false,
            },
            orderBy: {
                name: 'asc',
            },
        });
        return responsibilities;
    }
    async findOne(id) {
        const responsibility = await this.prisma.responsibility.findFirst({
            where: {
                id,
                deleted: false,
            },
            include: {
                parties: {
                    include: {
                        organization: true,
                    },
                },
            },
        });
        if (!responsibility) {
            throw new common_1.NotFoundException(`Responsibility with ID ${id} not found`);
        }
        return responsibility;
    }
    async update(id, updateResponsibilityDto) {
        const existingResponsibility = await this.prisma.responsibility.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingResponsibility) {
            throw new common_1.NotFoundException(`Responsibility with ID ${id} not found`);
        }
        try {
            const updatedResponsibility = await this.prisma.responsibility.update({
                where: { id },
                data: updateResponsibilityDto,
            });
            return updatedResponsibility;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException('Responsibility name must be unique');
                }
            }
            throw error;
        }
    }
    async remove(id) {
        const existingResponsibility = await this.prisma.responsibility.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingResponsibility) {
            throw new common_1.NotFoundException(`Responsibility with ID ${id} not found`);
        }
        const partiesUsingResponsibility = await this.prisma.party.findMany({
            where: {
                responsibilityId: id,
                deleted: false,
            },
        });
        if (partiesUsingResponsibility.length > 0) {
            throw new common_1.ConflictException('Cannot delete responsibility that is being used by parties');
        }
        await this.prisma.responsibility.update({
            where: { id },
            data: { deleted: true },
        });
        return { message: 'Responsibility deleted successfully' };
    }
};
exports.ResponsibilitiesService = ResponsibilitiesService;
exports.ResponsibilitiesService = ResponsibilitiesService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ResponsibilitiesService);
//# sourceMappingURL=responsibilities.service.js.map