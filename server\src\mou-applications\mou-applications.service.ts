import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateMouApplicationDto, UpdateMouApplicationDto } from './dto/create-mou-application.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class MouApplicationsService {
  constructor(private prisma: PrismaService) {}

  async create(createMouApplicationDto: CreateMouApplicationDto) {
    try {
      // Check if MoU exists
      const mou = await this.prisma.mou.findUnique({
        where: { id: createMouApplicationDto.mouId },
      });

      if (!mou) {
        throw new NotFoundException(`MoU with ID ${createMouApplicationDto.mouId} not found`);
      }

      // Check if application key is unique
      const existingApplication = await this.prisma.mouApplication.findUnique({
        where: { applicationKey: createMouApplicationDto.applicationKey },
      });

      if (existingApplication) {
        throw new ConflictException(`Application with key ${createMouApplicationDto.applicationKey} already exists`);
      }

      // Check if user exists (if provided)
      if (createMouApplicationDto.userId) {
        const user = await this.prisma.user.findUnique({
          where: { id: createMouApplicationDto.userId },
        });

        if (!user) {
          throw new NotFoundException(`User with ID ${createMouApplicationDto.userId} not found`);
        }
      }

      const mouApplication = await this.prisma.mouApplication.create({
        data: createMouApplicationDto,
        include: {
          mou: {
            include: {
              party: {
                include: {
                  organization: true,
                },
              },
            },
          },
          user: {
            include: {
              organization: true,
            },
          },
          projects: true,
          approvalSteps: {
            include: {
              reviewer: true,
            },
          },
        },
      });

      return mouApplication;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Application key must be unique');
        }
      }
      throw error;
    }
  }

  async findAll(page: number = 1, limit: number = 10, userId?: number) {
    const skip = (page - 1) * limit;
    
    const where: Prisma.MouApplicationWhereInput = {
      deleted: false,
      ...(userId && { userId }),
    };

    const [applications, total] = await Promise.all([
      this.prisma.mouApplication.findMany({
        where,
        skip,
        take: limit,
        include: {
          mou: {
            include: {
              party: {
                include: {
                  organization: true,
                },
              },
            },
          },
          user: {
            include: {
              organization: true,
            },
          },
          projects: true,
          approvalSteps: {
            include: {
              reviewer: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.mouApplication.count({ where }),
    ]);

    return {
      data: applications,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number) {
    const mouApplication = await this.prisma.mouApplication.findFirst({
      where: {
        id,
        deleted: false,
      },
      include: {
        mou: {
          include: {
            party: {
              include: {
                organization: true,
              },
            },
          },
        },
        user: {
          include: {
            organization: true,
          },
        },
        projects: {
          include: {
            activities: true,
            budgetType: true,
            fundingSource: true,
            fundingUnit: true,
          },
        },
        approvalSteps: {
          include: {
            reviewer: true,
            project: true,
          },
        },
      },
    });

    if (!mouApplication) {
      throw new NotFoundException(`MoU Application with ID ${id} not found`);
    }

    return mouApplication;
  }

  async findByApplicationKey(applicationKey: string) {
    const mouApplication = await this.prisma.mouApplication.findFirst({
      where: {
        applicationKey,
        deleted: false,
      },
      include: {
        mou: {
          include: {
            party: {
              include: {
                organization: true,
              },
            },
          },
        },
        user: {
          include: {
            organization: true,
          },
        },
        projects: {
          include: {
            activities: true,
          },
        },
        approvalSteps: {
          include: {
            reviewer: true,
          },
        },
      },
    });

    if (!mouApplication) {
      throw new NotFoundException(`MoU Application with key ${applicationKey} not found`);
    }

    return mouApplication;
  }

  async update(id: number, updateMouApplicationDto: UpdateMouApplicationDto) {
    // Check if application exists
    const existingApplication = await this.prisma.mouApplication.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingApplication) {
      throw new NotFoundException(`MoU Application with ID ${id} not found`);
    }

    // Check if user exists (if provided)
    if (updateMouApplicationDto.userId) {
      const user = await this.prisma.user.findUnique({
        where: { id: updateMouApplicationDto.userId },
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${updateMouApplicationDto.userId} not found`);
      }
    }

    const updatedApplication = await this.prisma.mouApplication.update({
      where: { id },
      data: updateMouApplicationDto,
      include: {
        mou: {
          include: {
            party: {
              include: {
                organization: true,
              },
            },
          },
        },
        user: {
          include: {
            organization: true,
          },
        },
        projects: true,
        approvalSteps: {
          include: {
            reviewer: true,
          },
        },
      },
    });

    return updatedApplication;
  }

  async remove(id: number) {
    // Check if application exists
    const existingApplication = await this.prisma.mouApplication.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingApplication) {
      throw new NotFoundException(`MoU Application with ID ${id} not found`);
    }

    // Soft delete
    await this.prisma.mouApplication.update({
      where: { id },
      data: { deleted: true },
    });

    return { message: 'MoU Application deleted successfully' };
  }

  async getApplicationStats(userId?: number) {
    const where: Prisma.MouApplicationWhereInput = {
      deleted: false,
      ...(userId && { userId }),
    };

    const [total, pending, approved, rejected] = await Promise.all([
      this.prisma.mouApplication.count({ where }),
      this.prisma.mouApplication.count({
        where: {
          ...where,
          approvalSteps: {
            some: {
              status: 'PENDING',
            },
          },
        },
      }),
      this.prisma.mouApplication.count({
        where: {
          ...where,
          approvalSteps: {
            every: {
              status: 'APPROVED',
            },
          },
        },
      }),
      this.prisma.mouApplication.count({
        where: {
          ...where,
          approvalSteps: {
            some: {
              status: 'REJECTED',
            },
          },
        },
      }),
    ]);

    return {
      total,
      pending,
      approved,
      rejected,
    };
  }
}
