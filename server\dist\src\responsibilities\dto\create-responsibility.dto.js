"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponsibilityResponseDto = exports.UpdateResponsibilityDto = exports.CreateResponsibilityDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateResponsibilityDto {
}
exports.CreateResponsibilityDto = CreateResponsibilityDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Responsibility name',
        example: 'Healthcare Service Delivery',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateResponsibilityDto.prototype, "name", void 0);
class UpdateResponsibilityDto {
}
exports.UpdateResponsibilityDto = UpdateResponsibilityDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Responsibility name',
        example: 'Updated Healthcare Service Delivery',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UpdateResponsibilityDto.prototype, "name", void 0);
class ResponsibilityResponseDto {
}
exports.ResponsibilityResponseDto = ResponsibilityResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Responsibility ID', example: 1 }),
    __metadata("design:type", Number)
], ResponsibilityResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Responsibility name', example: 'Healthcare Service Delivery' }),
    __metadata("design:type", String)
], ResponsibilityResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation date' }),
    __metadata("design:type", Date)
], ResponsibilityResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update date' }),
    __metadata("design:type", Date)
], ResponsibilityResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Soft delete flag' }),
    __metadata("design:type", Boolean)
], ResponsibilityResponseDto.prototype, "deleted", void 0);
//# sourceMappingURL=create-responsibility.dto.js.map