"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FundingUnitsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const funding_units_service_1 = require("./funding-units.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
const public_decorator_1 = require("../auth/decorator/public.decorator");
let FundingUnitsController = class FundingUnitsController {
    constructor(fundingUnitsService) {
        this.fundingUnitsService = fundingUnitsService;
    }
    async create(createFundingUnitDto, req) {
        return this.fundingUnitsService.create(createFundingUnitDto, req.user.sub);
    }
    async findAll() {
        return this.fundingUnitsService.findAll();
    }
    async findOne(id) {
        return this.fundingUnitsService.findOne(id);
    }
    async update(id, updateFundingUnitDto, req) {
        return this.fundingUnitsService.update(id, updateFundingUnitDto, req.user.sub);
    }
    async remove(id, req) {
        return this.fundingUnitsService.remove(id, req.user.sub);
    }
};
exports.FundingUnitsController = FundingUnitsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new funding unit (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Funding unit created successfully', type: dto_1.FundingUnitResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Funding unit with name already exists' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateFundingUnitDto, Object]),
    __metadata("design:returntype", Promise)
], FundingUnitsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all funding units (Public access)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of funding units', type: [dto_1.FundingUnitResponseDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FundingUnitsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get funding unit by ID (Public access)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Funding unit details', type: dto_1.FundingUnitResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Funding unit not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], FundingUnitsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update funding unit (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Funding unit updated successfully', type: dto_1.FundingUnitResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Funding unit not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Funding unit with name already exists' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateFundingUnitDto, Object]),
    __metadata("design:returntype", Promise)
], FundingUnitsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Delete funding unit (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Funding unit deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Funding unit not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Cannot delete funding unit that is being used' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], FundingUnitsController.prototype, "remove", null);
exports.FundingUnitsController = FundingUnitsController = __decorate([
    (0, swagger_1.ApiTags)('funding-units'),
    (0, common_1.Controller)('funding-units'),
    __metadata("design:paramtypes", [funding_units_service_1.FundingUnitsService])
], FundingUnitsController);
//# sourceMappingURL=funding-units.controller.js.map