import { useCallback } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useFieldArray, useForm } from 'react-hook-form'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useFieldBlurAutoSave } from '@/hooks/use-mou-form'
import { useMouStore } from '@/store/mou-store'
import { activitySchema } from '@/lib/validations/mou'
import {
  mockDomains,
  mockInputCategories,
  mockProvinces,
  mockCentralLevels,
  mockFiscalYears
} from '@/data/mock-data'
import { CalendarIcon, ChevronsUpDown, Trash2 } from 'lucide-react'
import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { cn } from '@/lib/utils'
import { format } from 'date-fns'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Checkbox } from '@/components/ui/checkbox'

type FormData = {
  activities: z.infer<typeof activitySchema>[]
}

const formSchema = z.object({
  activities: z.array(activitySchema)
})

export function ActivitiesStep() {
  const { onBlur } = useFieldBlurAutoSave()
  const projects = useMouStore(state => state.projects)
  const activities = useMouStore(state => state.activities)
  const addActivity = useMouStore(state => state.addActivity)
  const removeActivity = useMouStore(state => state.removeActivity)
  const updateActivity = useMouStore(state => state.updateActivity)

  const {
    register,
    control,
    setValue,
    watch,
    formState: { errors }
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      activities
    }
  })

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'activities'
  })

  const handleAddActivity = useCallback((projectId: string) => {
    const newActivity = {
      projectId,
      name: '',
      implementor: '',
      implementingUnit: '',
      fiscalYear: '',
      startDate: '',
      endDate: '',
      domain: '',
      subDomain: '',
      subDomainFunction: '',
      subFunction: '',
      inputCategory: '',
      activityInput: '',
      geographicLevel: 'Provinces' as const,
      budgetAllocations: []
    }
    append(newActivity)
    addActivity(newActivity)
  }, [append, addActivity])

  const handleRemoveActivity = useCallback((index: number, id?: string) => {
    remove(index)
    if (id) {
      removeActivity(id)
    }
  }, [remove, removeActivity])

  const watchActivities = watch('activities')

  const handleActivityChange = useCallback((index: number, field: string, value: any) => {
    const activity = activities[index]
    if (activity) {
      updateActivity(activity.id, { [field]: value })
    }
  }, [activities, updateActivity])

  return (
    <div className="space-y-6">
      {projects.length === 0 ? (
        <Alert>
          <AlertDescription>
            Please add at least one project before adding activities.
          </AlertDescription>
        </Alert>
      ) : (
        <Accordion type="single" collapsible className="space-y-4">
          {projects.map((project) => {
            const projectActivities = fields.filter(
              (field, index) => watchActivities[index]?.projectId === project.id
            )

            return (
              <AccordionItem key={project.id} value={project.id}>
                <AccordionTrigger className="text-lg font-semibold">
                  {project.name}
                </AccordionTrigger>
                <AccordionContent className="pt-4 space-y-6">
                  {projectActivities.map((field, activityIndex) => {
                    const globalIndex = fields.findIndex(f => f.id === field.id)
                    const currentDomain = mockDomains.find(
                      d => d.id === watchActivities[globalIndex]?.domain
                    )
                    const currentSubDomain = currentDomain?.subDomains.find(
                      sd => sd.id === watchActivities[globalIndex]?.subDomain
                    )
                    const currentFunction = currentSubDomain?.functions.find(
                      f => f.id === watchActivities[globalIndex]?.subDomainFunction
                    )
                    const currentInputCategory = mockInputCategories.find(
                      ic => ic.id === watchActivities[globalIndex]?.inputCategory
                    )

                    return (
                      <Card key={field.id} className="p-6 relative">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute right-4 top-4"
                          onClick={() => handleRemoveActivity(globalIndex, field.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>

                        <div className="grid grid-cols-2 gap-6">
                          <div className="space-y-2">
                            <Label htmlFor={`activities.${globalIndex}.name`}>Activity Name</Label>
                            <Input
                              {...register(`activities.${globalIndex}.name`)}
                              onBlur={(e) => {
                                onBlur()
                                handleActivityChange(globalIndex, 'name', e.target.value)
                              }}
                            />
                            {errors.activities?.[globalIndex]?.name && (
                              <p className="text-sm text-red-500">
                                {errors.activities[globalIndex]?.name?.message}
                              </p>
                            )}
                          </div>

                          <div className="space-y-2">
                            <Label>Fiscal Year</Label>
                            <Select
                              value={watchActivities[globalIndex]?.fiscalYear}
                              onValueChange={(value) => {
                                setValue(`activities.${globalIndex}.fiscalYear`, value)
                                handleActivityChange(globalIndex, 'fiscalYear', value)
                                onBlur()
                              }}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select fiscal year" />
                              </SelectTrigger>
                              <SelectContent>
                                {mockFiscalYears.slice(0, 2).map((fy) => (
                                  <SelectItem key={fy.id} value={fy.id}>
                                    {fy.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label>Start Date</Label>
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    "w-full justify-start text-left font-normal",
                                    !watchActivities[globalIndex]?.startDate && "text-muted-foreground"
                                  )}
                                >
                                  <CalendarIcon className="mr-2 h-4 w-4" />
                                  {watchActivities[globalIndex]?.startDate ? (
                                    format(new Date(watchActivities[globalIndex].startDate), "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="start">
                                <Calendar
                                  mode="single"
                                  selected={watchActivities[globalIndex]?.startDate ? new Date(watchActivities[globalIndex].startDate) : undefined}
                                  onSelect={(date) => {
                                    if (date) {
                                      const value = date.toISOString()
                                      setValue(`activities.${globalIndex}.startDate`, value)
                                      handleActivityChange(globalIndex, 'startDate', value)
                                      onBlur()
                                    }
                                  }}
                                  initialFocus
                                />
                              </PopoverContent>
                            </Popover>
                          </div>

                          <div className="space-y-2">
                            <Label>End Date</Label>
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    "w-full justify-start text-left font-normal",
                                    !watchActivities[globalIndex]?.endDate && "text-muted-foreground"
                                  )}
                                >
                                  <CalendarIcon className="mr-2 h-4 w-4" />
                                  {watchActivities[globalIndex]?.endDate ? (
                                    format(new Date(watchActivities[globalIndex].endDate), "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="start">
                                <Calendar
                                  mode="single"
                                  selected={watchActivities[globalIndex]?.endDate ? new Date(watchActivities[globalIndex].endDate) : undefined}
                                  onSelect={(date) => {
                                    if (date) {
                                      const value = date.toISOString()
                                      setValue(`activities.${globalIndex}.endDate`, value)
                                      handleActivityChange(globalIndex, 'endDate', value)
                                      onBlur()
                                    }
                                  }}
                                  initialFocus
                                />
                              </PopoverContent>
                            </Popover>
                          </div>

                          <div className="space-y-2">
                            <Label>Implementor</Label>
                            <Input
                              {...register(`activities.${globalIndex}.implementor`)}
                              onBlur={(e) => {
                                onBlur()
                                handleActivityChange(globalIndex, 'implementor', e.target.value)
                              }}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label>Implementing Unit</Label>
                            <Input
                              {...register(`activities.${globalIndex}.implementingUnit`)}
                              onBlur={(e) => {
                                onBlur()
                                handleActivityChange(globalIndex, 'implementingUnit', e.target.value)
                              }}
                            />
                          </div>

                          <div className="col-span-2">
                            <Card className="p-4">
                              <h4 className="font-semibold mb-4">Domain of Intervention</h4>
                              <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                  <Label>Domain</Label>
                                  <Select
                                    value={watchActivities[globalIndex]?.domain}
                                    onValueChange={(value) => {
                                      setValue(`activities.${globalIndex}.domain`, value)
                                      setValue(`activities.${globalIndex}.subDomain`, '')
                                      setValue(`activities.${globalIndex}.subDomainFunction`, '')
                                      setValue(`activities.${globalIndex}.subFunction`, '')
                                      handleActivityChange(globalIndex, 'domain', value)
                                      onBlur()
                                    }}
                                  >
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select domain" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {mockDomains.map((domain) => (
                                        <SelectItem key={domain.id} value={domain.id}>
                                          {domain.name}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>

                                <div className="space-y-2">
                                  <Label>Sub Domain</Label>
                                  <Select
                                    value={watchActivities[globalIndex]?.subDomain}
                                    onValueChange={(value) => {
                                      setValue(`activities.${globalIndex}.subDomain`, value)
                                      setValue(`activities.${globalIndex}.subDomainFunction`, '')
                                      setValue(`activities.${globalIndex}.subFunction`, '')
                                      handleActivityChange(globalIndex, 'subDomain', value)
                                      onBlur()
                                    }}
                                    disabled={!currentDomain}
                                  >
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select sub domain" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {currentDomain?.subDomains.map((subDomain) => (
                                        <SelectItem key={subDomain.id} value={subDomain.id}>
                                          {subDomain.name}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>

                                <div className="space-y-2">
                                  <Label>Function</Label>
                                  <Select
                                    value={watchActivities[globalIndex]?.subDomainFunction}
                                    onValueChange={(value) => {
                                      setValue(`activities.${globalIndex}.subDomainFunction`, value)
                                      setValue(`activities.${globalIndex}.subFunction`, '')
                                      handleActivityChange(globalIndex, 'subDomainFunction', value)
                                      onBlur()
                                    }}
                                    disabled={!currentSubDomain}
                                  >
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select function" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {currentSubDomain?.functions.map((func) => (
                                        <SelectItem key={func.id} value={func.id}>
                                          {func.name}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>

                                <div className="space-y-2">
                                  <Label>Sub Function</Label>
                                  <Select
                                    value={watchActivities[globalIndex]?.subFunction}
                                    onValueChange={(value) => {
                                      setValue(`activities.${globalIndex}.subFunction`, value)
                                      handleActivityChange(globalIndex, 'subFunction', value)
                                      onBlur()
                                    }}
                                    disabled={!currentFunction}
                                  >
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select sub function" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {currentFunction?.subFunctions.map((subFunc) => (
                                        <SelectItem key={subFunc.id} value={subFunc.id}>
                                          {subFunc.name}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                              </div>
                            </Card>
                          </div>

                          <div className="col-span-2">
                            <Card className="p-4">
                              <h4 className="font-semibold mb-4">Budget Allocation</h4>
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                  <div className="space-y-2">
                                    <Label>Input Category</Label>
                                    <Select
                                      value={watchActivities[globalIndex]?.inputCategory}
                                      onValueChange={(value) => {
                                        setValue(`activities.${globalIndex}.inputCategory`, value)
                                        setValue(`activities.${globalIndex}.activityInput`, '')
                                        handleActivityChange(globalIndex, 'inputCategory', value)
                                        onBlur()
                                      }}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select input category" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {mockInputCategories.map((category) => (
                                          <SelectItem key={category.id} value={category.id}>
                                            {category.name}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </div>

                                  <div className="space-y-2">
                                    <Label>Activity Input</Label>
                                    <Select
                                      value={watchActivities[globalIndex]?.activityInput}
                                      onValueChange={(value) => {
                                        setValue(`activities.${globalIndex}.activityInput`, value)
                                        handleActivityChange(globalIndex, 'activityInput', value)
                                        onBlur()
                                      }}
                                      disabled={!currentInputCategory}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select activity input" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {currentInputCategory?.activities.map((activity) => (
                                          <SelectItem key={activity.id} value={activity.id}>
                                            {activity.name}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </div>
                                </div>

                                <div className="space-y-4">
                                  <div className="flex items-center space-x-4">
                                    <Label>Geographic Level</Label>
                                    <div className="flex items-center space-x-4">
                                      <div className="flex items-center space-x-2">
                                        <input
                                          type="radio"
                                          id={`provinces-${globalIndex}`}
                                          value="Provinces"
                                          checked={watchActivities[globalIndex]?.geographicLevel === 'Provinces'}
                                          onChange={(e) => {
                                            setValue(`activities.${globalIndex}.geographicLevel`, 'Provinces')
                                            setValue(`activities.${globalIndex}.budgetAllocations`, [])
                                            handleActivityChange(globalIndex, 'geographicLevel', 'Provinces')
                                            onBlur()
                                          }}
                                        />
                                        <Label htmlFor={`provinces-${globalIndex}`}>Provinces</Label>
                                      </div>
                                      <div className="flex items-center space-x-2">
                                        <input
                                          type="radio"
                                          id={`central-${globalIndex}`}
                                          value="Central"
                                          checked={watchActivities[globalIndex]?.geographicLevel === 'Central'}
                                          onChange={(e) => {
                                            setValue(`activities.${globalIndex}.geographicLevel`, 'Central')
                                            setValue(`activities.${globalIndex}.budgetAllocations`, [])
                                            handleActivityChange(globalIndex, 'geographicLevel', 'Central')
                                            onBlur()
                                          }}
                                        />
                                        <Label htmlFor={`central-${globalIndex}`}>Central</Label>
                                      </div>
                                    </div>
                                  </div>

                                  {watchActivities[globalIndex]?.geographicLevel === 'Provinces' ? (
                                    <div className="space-y-4">
                                      {mockProvinces.map((province) => (
                                        <div key={province.id} className="space-y-2">
                                          <Label>{province.name}</Label>
                                          <div className="pl-4 space-y-2">
                                            {province.districts.map((district) => {
                                              const allocation = watchActivities[globalIndex]?.budgetAllocations.find(
                                                (ba) => ba.location === district.id
                                              )
                                              return (
                                                <div key={district.id} className="flex items-center space-x-4">
                                                  <Checkbox
                                                    checked={!!allocation}
                                                    onCheckedChange={(checked) => {
                                                      const allocations = [...(watchActivities[globalIndex]?.budgetAllocations || [])]
                                                      if (checked) {
                                                        allocations.push({
                                                          location: district.id,
                                                          budget: 0
                                                        })
                                                      } else {
                                                        const index = allocations.findIndex(
                                                          (a) => a.location === district.id
                                                        )
                                                        if (index !== -1) {
                                                          allocations.splice(index, 1)
                                                        }
                                                      }
                                                      setValue(
                                                        `activities.${globalIndex}.budgetAllocations`,
                                                        allocations
                                                      )
                                                      handleActivityChange(
                                                        globalIndex,
                                                        'budgetAllocations',
                                                        allocations
                                                      )
                                                      onBlur()
                                                    }}
                                                  />
                                                  <Label className="flex-1">{district.name}</Label>
                                                  {allocation && (
                                                    <Input
                                                      type="number"
                                                      min="0"
                                                      className="w-32"
                                                      value={allocation.budget}
                                                      onChange={(e) => {
                                                        const value = parseFloat(e.target.value) || 0
                                                        const allocations = [...watchActivities[globalIndex].budgetAllocations]
                                                        const index = allocations.findIndex(
                                                          (a) => a.location === district.id
                                                        )
                                                        if (index !== -1) {
                                                          allocations[index].budget = value
                                                          setValue(
                                                            `activities.${globalIndex}.budgetAllocations`,
                                                            allocations
                                                          )
                                                          handleActivityChange(
                                                            globalIndex,
                                                            'budgetAllocations',
                                                            allocations
                                                          )
                                                          onBlur()
                                                        }
                                                      }}
                                                    />
                                                  )}
                                                </div>
                                              )
                                            })}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  ) : (
                                    <div className="space-y-4">
                                      {mockCentralLevels.map((level) => {
                                        const allocation = watchActivities[globalIndex]?.budgetAllocations.find(
                                          (ba) => ba.location === level.id
                                        )
                                        return (
                                          <div key={level.id} className="flex items-center space-x-4">
                                            <Checkbox
                                              checked={!!allocation}
                                              onCheckedChange={(checked) => {
                                                const allocations = [...(watchActivities[globalIndex]?.budgetAllocations || [])]
                                                if (checked) {
                                                  allocations.push({
                                                    location: level.id,
                                                    budget: 0
                                                  })
                                                } else {
                                                  const index = allocations.findIndex(
                                                    (a) => a.location === level.id
                                                  )
                                                  if (index !== -1) {
                                                    allocations.splice(index, 1)
                                                  }
                                                }
                                                setValue(
                                                  `activities.${globalIndex}.budgetAllocations`,
                                                  allocations
                                                )
                                                handleActivityChange(
                                                  globalIndex,
                                                  'budgetAllocations',
                                                  allocations
                                                )
                                                onBlur()
                                              }}
                                            />
                                            <Label className="flex-1">{level.name}</Label>
                                            {allocation && (
                                              <Input
                                                type="number"
                                                min="0"
                                                className="w-32"
                                                value={allocation.budget}
                                                onChange={(e) => {
                                                  const value = parseFloat(e.target.value) || 0
                                                  const allocations = [...watchActivities[globalIndex].budgetAllocations]
                                                  const index = allocations.findIndex(
                                                    (a) => a.location === level.id
                                                  )
                                                  if (index !== -1) {
                                                    allocations[index].budget = value
                                                    setValue(
                                                      `activities.${globalIndex}.budgetAllocations`,
                                                      allocations
                                                    )
                                                    handleActivityChange(
                                                      globalIndex,
                                                      'budgetAllocations',
                                                      allocations
                                                    )
                                                    onBlur()
                                                  }
                                                }}
                                              />
                                            )}
                                          </div>
                                        )
                                      })}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </Card>
                          </div>
                        </div>
                      </Card>
                    )
                  })}

                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={() => handleAddActivity(project.id)}
                  >
                    Add Activity
                  </Button>
                </AccordionContent>
              </AccordionItem>
            )
          })}
        </Accordion>
      )}
    </div>
  )
}
