import { PrismaService } from '../prisma/prisma.service';
import { CreateDomainInterventionDto, UpdateDomainInterventionDto } from './dto';
export declare class DomainInterventionsService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    create(createDomainInterventionDto: CreateDomainInterventionDto, userId: string): Promise<{
        parent: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            userId: number | null;
            parentId: number | null;
            domainName: string;
        };
        children: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            userId: number | null;
            parentId: number | null;
            domainName: string;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        userId: number | null;
        parentId: number | null;
        domainName: string;
    }>;
    findAll(): Promise<({
        parent: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            userId: number | null;
            parentId: number | null;
            domainName: string;
        };
        children: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            userId: number | null;
            parentId: number | null;
            domainName: string;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        userId: number | null;
        parentId: number | null;
        domainName: string;
    })[]>;
    findTree(): Promise<any[]>;
    findOne(id: number): Promise<{
        parent: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            userId: number | null;
            parentId: number | null;
            domainName: string;
        };
        children: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            userId: number | null;
            parentId: number | null;
            domainName: string;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        userId: number | null;
        parentId: number | null;
        domainName: string;
    }>;
    update(id: number, updateDomainInterventionDto: UpdateDomainInterventionDto, userId: string): Promise<{
        parent: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            userId: number | null;
            parentId: number | null;
            domainName: string;
        };
        children: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            userId: number | null;
            parentId: number | null;
            domainName: string;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        userId: number | null;
        parentId: number | null;
        domainName: string;
    }>;
    remove(id: number, userId: string): Promise<{
        success: boolean;
    }>;
}
