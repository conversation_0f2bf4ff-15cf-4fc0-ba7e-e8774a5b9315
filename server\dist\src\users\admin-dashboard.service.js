"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminDashboardService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let AdminDashboardService = class AdminDashboardService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getUserStatistics() {
        const [totalUsers, activeUsers, inactiveUsers, recentlyActive, roleDistribution, departmentDistribution] = await Promise.all([
            this.prisma.user.count(),
            this.prisma.user.count({
                where: { isActive: true }
            }),
            this.prisma.user.count({
                where: { isActive: false }
            }),
            this.prisma.user.count({
                where: {
                    verifiedAt: {
                        gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
                    }
                }
            }),
            this.getRoleDistribution(),
            this.getDepartmentDistribution()
        ]);
        return {
            totalUsers,
            activeUsers,
            inactiveUsers,
            recentlyActive,
            roleDistribution,
            departmentDistribution
        };
    }
    async getActivityMetrics(startDate, endDate) {
        const dateFilter = this.getDateFilter(startDate, endDate);
        const [loginActivity, userActions, reviewActivity, approvalActivity] = await Promise.all([
            this.getLoginActivity(dateFilter),
            this.getUserActions(dateFilter),
            this.getReviewActivity(dateFilter),
            this.getApprovalActivity(dateFilter)
        ]);
        return {
            loginActivity,
            userActions,
            reviewActivity,
            approvalActivity
        };
    }
    async getSystemHealth() {
        const [activeApplications, pendingReviews, pendingApprovals, recentErrors] = await Promise.all([
            this.prisma.mouApplication.count({
                where: {
                    approvalSteps: {
                        some: {
                            status: 'PENDING'
                        }
                    }
                }
            }),
            this.prisma.mouApplication.count({
                where: {
                    approvalSteps: {
                        some: {
                            status: 'PENDING'
                        }
                    }
                }
            }),
            this.prisma.mouApplication.count({
                where: {
                    approvalSteps: {
                        some: {
                            status: 'APPROVED'
                        }
                    }
                }
            }),
            this.prisma.activityLog.findMany({
                where: {
                    importance: 'ERROR',
                    createdAt: {
                        gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
                    }
                },
                orderBy: {
                    createdAt: 'desc'
                },
                take: 10
            })
        ]);
        return {
            activeApplications,
            pendingReviews,
            pendingApprovals,
            recentErrors,
            systemStatus: this.getSystemStatus()
        };
    }
    async getPerformanceMetrics() {
        return {
            averageReviewTime: await this.calculateAverageReviewTime(),
            averageApprovalTime: await this.calculateAverageApprovalTime(),
            applicationThroughput: await this.calculateApplicationThroughput(),
            userProductivity: await this.calculateUserProductivity()
        };
    }
    async getRoleDistribution() {
        const roles = await this.prisma.user.groupBy({
            by: ['role'],
            _count: {
                role: true
            }
        });
        return {
            roles: roles
        };
    }
    async getDepartmentDistribution() {
        return this.prisma.user.groupBy({
            by: ['organizationId'],
            _count: {
                organizationId: true
            }
        });
    }
    async getLoginActivity(dateFilter) {
        return this.prisma.activityLog.groupBy({
            by: ['createdAt'],
            where: {
                action: 'LOGIN',
                ...dateFilter
            },
            _count: {
                action: true
            }
        });
    }
    async getUserActions(dateFilter) {
        return this.prisma.activityLog.groupBy({
            by: ['action'],
            where: dateFilter,
            _count: {
                action: true
            }
        });
    }
    async getReviewActivity(dateFilter) {
        return this.prisma.approvalStep.groupBy({
            by: ['status'],
            where: {
                createdAt: dateFilter.createdAt
            },
            _count: {
                status: true
            }
        });
    }
    async getApprovalActivity(dateFilter) {
        return this.prisma.approvalStep.groupBy({
            by: ['status'],
            where: {
                createdAt: dateFilter.createdAt,
                status: 'APPROVED'
            },
            _count: {
                status: true
            }
        });
    }
    async calculateAverageReviewTime() {
        const approvalSteps = await this.prisma.approvalStep.findMany({
            include: {
                mouApplication: true
            }
        });
        let totalTime = 0;
        let count = 0;
        approvalSteps.forEach(step => {
            if (step.mouApplication.createdAt && step.createdAt) {
                const submissionDate = step.mouApplication.createdAt;
                const reviewDate = step.createdAt;
                totalTime += reviewDate.getTime() - submissionDate.getTime();
                count++;
            }
        });
        return count > 0 ? totalTime / count / (1000 * 60 * 60) : 0;
    }
    async calculateAverageApprovalTime() {
        const approvals = await this.prisma.approvalStep.findMany({
            where: {
                status: 'APPROVED'
            },
            include: {
                mouApplication: true
            }
        });
        let totalTime = 0;
        let count = 0;
        approvals.forEach(approval => {
            if (approval.mouApplication.createdAt && approval.createdAt) {
                const submissionDate = approval.mouApplication.createdAt;
                const approvalDate = approval.createdAt;
                totalTime += approvalDate.getTime() - submissionDate.getTime();
                count++;
            }
        });
        return count > 0 ? totalTime / count / (1000 * 60 * 60) : 0;
    }
    async calculateApplicationThroughput() {
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const applications = await this.prisma.mouApplication.findMany({
            where: {
                updatedAt: {
                    gte: thirtyDaysAgo
                }
            },
            include: {
                approvalSteps: true
            }
        });
        const statusCounts = {};
        applications.forEach(app => {
            const latestStep = app.approvalSteps[app.approvalSteps.length - 1];
            const status = latestStep?.status || 'PENDING';
            statusCounts[status] = (statusCounts[status] || 0) + 1;
        });
        return Object.entries(statusCounts).map(([status, count]) => ({
            status,
            _count: { status: count }
        }));
    }
    async calculateUserProductivity() {
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        return [];
    }
    getDateFilter(startDate, endDate) {
        const filter = {};
        if (startDate || endDate) {
            filter.createdAt = {};
            if (startDate)
                filter.createdAt.gte = startDate;
            if (endDate)
                filter.createdAt.lte = endDate;
        }
        return filter;
    }
    getSystemStatus() {
        return {
            status: 'HEALTHY',
            lastCheck: new Date(),
            components: {
                database: 'OPERATIONAL',
                email: 'OPERATIONAL',
                storage: 'OPERATIONAL',
                notifications: 'OPERATIONAL'
            }
        };
    }
};
exports.AdminDashboardService = AdminDashboardService;
exports.AdminDashboardService = AdminDashboardService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], AdminDashboardService);
//# sourceMappingURL=admin-dashboard.service.js.map