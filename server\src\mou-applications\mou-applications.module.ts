import { Module } from '@nestjs/common';
import { MouApplicationsService } from './mou-applications.service';
import { MouApplicationsController } from './mou-applications.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [PrismaModule],
  controllers: [MouApplicationsController],
  providers: [MouApplicationsService],
  exports: [MouApplicationsService],
})
export class MouApplicationsModule {}
