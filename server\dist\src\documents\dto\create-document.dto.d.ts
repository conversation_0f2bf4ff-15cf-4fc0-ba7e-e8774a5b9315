export declare class CreateDocumentDto {
    name: string;
    description?: string;
    organizationId: number;
    documentTypeId: number;
}
export declare class UpdateDocumentDto {
    name?: string;
    description?: string;
    documentTypeId?: number;
}
export declare class DocumentResponseDto {
    id: number;
    name: string;
    description?: string;
    organizationId: number;
    documentTypeId: number;
    createdAt: Date;
    updatedAt: Date;
    deleted: boolean;
}
export declare class DocumentWithRelationsDto extends DocumentResponseDto {
    organization?: {
        id: number;
        organizationName: string;
        organizationEmail: string;
    };
    documentType?: {
        id: number;
        typeName: string;
    };
    project?: {
        id: number;
        name: string;
        description?: string;
        mouApplication: {
            id: number;
            applicationKey: string;
        };
    };
}
export declare class DocumentUploadDto {
    file: Express.Multer.File;
    name: string;
    description?: string;
    organizationId: number;
    documentTypeId: number;
}
export declare class DocumentFileResponseDto {
    fileName: string;
    originalName: string;
    size: number;
    mimeType: string;
    path: string;
    uploadedAt: Date;
}
