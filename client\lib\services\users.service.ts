import api from "../api"

export interface User {
  id: string
  firstName: string
  lastName: string
  email: string
  role: string
  emailVerified: boolean
  organizationId?: string
  organization?: {
    id: string
    organizationName: string
  }
  createdAt: string
  updatedAt: string
}

export interface CreateUserRequest {
  firstName: string
  lastName: string
  email: string
  role: string
  organizationId?: string
}

export interface UpdateUserRequest {
  firstName?: string
  lastName?: string
  email?: string
  role?: string
  organizationId?: string
}

export const usersService = {
  async getUsers(): Promise<User[]> {
    const response = await api.get("/users")
    return response.data
  },

  async getUser(id: string): Promise<User> {
    const response = await api.get(`/users/${id}`)
    return response.data
  },

  async createUser(data: CreateUserRequest): Promise<User> {
    const response = await api.post("/users", data)
    return response.data
  },

  async updateUser(id: string, data: UpdateUserRequest): Promise<User> {
    const response = await api.patch(`/users/${id}`, data)
    return response.data
  },

  async deleteUser(id: string): Promise<void> {
    await api.delete(`/users/${id}`)
  },
}
