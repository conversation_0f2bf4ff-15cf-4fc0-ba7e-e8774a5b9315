import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Res,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery, ApiConsumes } from '@nestjs/swagger';
import { Response } from 'express';
import { DocumentsService } from './documents.service';
import { 
  CreateDocumentDto, 
  UpdateDocumentDto, 
  DocumentResponseDto, 
  DocumentWithRelationsDto,
  DocumentUploadDto,
  DocumentFileResponseDto 
} from './dto/create-document.dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { RolesGuard } from '../auth/guard/roles.guard';
import { Roles } from '../auth/decorator/roles.decorator';
import { UserRole } from '../auth/dto';

@ApiTags('Documents')
@ApiBearerAuth()
@UseGuards(JwtGuard, RolesGuard)
@Controller('documents')
export class DocumentsController {
  constructor(private readonly documentsService: DocumentsService) {}

  @Post()
  @Roles(UserRole.PARTNER, UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new document record' })
  @ApiResponse({
    status: 201,
    description: 'Document created successfully',
    type: DocumentWithRelationsDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Related entity not found' })
  async create(@Body() createDocumentDto: CreateDocumentDto) {
    return this.documentsService.create(createDocumentDto);
  }

  @Post('upload')
  @Roles(UserRole.PARTNER, UserRole.ADMIN)
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload a document file' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: 201,
    description: 'Document uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        document: { $ref: '#/components/schemas/DocumentWithRelationsDto' },
        file: { $ref: '#/components/schemas/DocumentFileResponseDto' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request or invalid file' })
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDto: DocumentUploadDto,
  ) {
    const createDocumentDto: CreateDocumentDto = {
      name: uploadDto.name,
      description: uploadDto.description,
      organizationId: parseInt(uploadDto.organizationId.toString(), 10),
      documentTypeId: parseInt(uploadDto.documentTypeId.toString(), 10),
    };

    return this.documentsService.uploadFile(file, createDocumentDto);
  }

  @Get()
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get all documents with pagination' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10)' })
  @ApiQuery({ name: 'organizationId', required: false, type: Number, description: 'Filter by organization ID' })
  @ApiQuery({ name: 'documentTypeId', required: false, type: Number, description: 'Filter by document type ID' })
  @ApiResponse({
    status: 200,
    description: 'List of documents',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/DocumentWithRelationsDto' },
        },
        meta: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            page: { type: 'number' },
            limit: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
      },
    },
  })
  async findAll(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('organizationId') organizationId?: string,
    @Query('documentTypeId') documentTypeId?: string,
  ) {
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 10;
    const orgIdNum = organizationId ? parseInt(organizationId, 10) : undefined;
    const docTypeIdNum = documentTypeId ? parseInt(documentTypeId, 10) : undefined;

    return this.documentsService.findAll(pageNum, limitNum, orgIdNum, docTypeIdNum);
  }

  @Get('by-organization/:organizationId')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get documents by organization ID' })
  @ApiResponse({
    status: 200,
    description: 'List of documents for the organization',
    type: [DocumentWithRelationsDto],
  })
  async getDocumentsByOrganization(@Param('organizationId', ParseIntPipe) organizationId: number) {
    return this.documentsService.getDocumentsByOrganization(organizationId);
  }

  @Get('by-type/:documentTypeId')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get documents by document type ID' })
  @ApiResponse({
    status: 200,
    description: 'List of documents of the specified type',
    type: [DocumentWithRelationsDto],
  })
  async getDocumentsByType(@Param('documentTypeId', ParseIntPipe) documentTypeId: number) {
    return this.documentsService.getDocumentsByType(documentTypeId);
  }

  @Get(':id')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get a document by ID' })
  @ApiResponse({
    status: 200,
    description: 'Document details',
    type: DocumentWithRelationsDto,
  })
  @ApiResponse({ status: 404, description: 'Document not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.documentsService.findOne(id);
  }

  @Get(':id/download')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Download a document file' })
  @ApiResponse({
    status: 200,
    description: 'Document file',
    content: {
      'application/octet-stream': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Document not found' })
  async downloadFile(@Param('id', ParseIntPipe) id: number, @Res() res: Response) {
    return this.documentsService.downloadFile(id);
  }

  @Patch(':id')
  @Roles(UserRole.PARTNER, UserRole.ADMIN)
  @ApiOperation({ summary: 'Update a document' })
  @ApiResponse({
    status: 200,
    description: 'Document updated successfully',
    type: DocumentWithRelationsDto,
  })
  @ApiResponse({ status: 404, description: 'Document not found' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDocumentDto: UpdateDocumentDto,
  ) {
    return this.documentsService.update(id, updateDocumentDto);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete a document (soft delete)' })
  @ApiResponse({
    status: 200,
    description: 'Document deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Document not found' })
  @ApiResponse({ status: 409, description: 'Document is being used by a project' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.documentsService.remove(id);
  }
}
