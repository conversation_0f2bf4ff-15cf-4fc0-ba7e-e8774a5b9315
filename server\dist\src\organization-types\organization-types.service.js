"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OrganizationTypesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationTypesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let OrganizationTypesService = OrganizationTypesService_1 = class OrganizationTypesService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(OrganizationTypesService_1.name);
    }
    async findAll() {
        try {
            const organizationTypes = await this.prisma.organizationType.findMany({
                where: { deleted: false },
                orderBy: { createdAt: 'desc' }
            });
            return organizationTypes;
        }
        catch (error) {
            this.logger.error('Failed to fetch organization types', error.stack);
            throw new Error('Failed to fetch organization types');
        }
    }
    async findOne(id) {
        try {
            const organizationType = await this.prisma.organizationType.findUnique({
                where: { id, deleted: false }
            });
            if (!organizationType) {
                throw new common_1.NotFoundException('Organization type not found');
            }
            return organizationType;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Failed to fetch organization type with ID ${id}`, error.stack);
            throw new Error('Failed to fetch organization type');
        }
    }
    async create(createOrganizationTypeDto, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can create organization types');
            }
            const existingType = await this.prisma.organizationType.findFirst({
                where: {
                    typeName: createOrganizationTypeDto.typeName,
                    deleted: false
                }
            });
            if (existingType) {
                throw new common_1.ConflictException('Organization type with this name already exists');
            }
            const organizationType = await this.prisma.organizationType.create({
                data: {
                    typeName: createOrganizationTypeDto.typeName
                }
            });
            return organizationType;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to create organization type', error.stack);
            throw new Error('Failed to create organization type');
        }
    }
    async update(id, updateOrganizationTypeDto, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can update organization types');
            }
            const existingType = await this.prisma.organizationType.findUnique({
                where: { id, deleted: false }
            });
            if (!existingType) {
                throw new common_1.NotFoundException('Organization type not found');
            }
            if (updateOrganizationTypeDto.typeName && updateOrganizationTypeDto.typeName !== existingType.typeName) {
                const nameConflict = await this.prisma.organizationType.findFirst({
                    where: {
                        typeName: updateOrganizationTypeDto.typeName,
                        deleted: false,
                        id: { not: id }
                    }
                });
                if (nameConflict) {
                    throw new common_1.ConflictException('Organization type with this name already exists');
                }
            }
            const organizationType = await this.prisma.organizationType.update({
                where: { id },
                data: updateOrganizationTypeDto
            });
            return organizationType;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to update organization type with ID ${id}`, error.stack);
            throw new Error('Failed to update organization type');
        }
    }
    async remove(id, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can delete organization types');
            }
            const existingType = await this.prisma.organizationType.findUnique({
                where: { id, deleted: false }
            });
            if (!existingType) {
                throw new common_1.NotFoundException('Organization type not found');
            }
            const organizationsUsingType = await this.prisma.organization.findFirst({
                where: {
                    organizationTypeId: id,
                    deleted: false
                }
            });
            if (organizationsUsingType) {
                throw new common_1.ConflictException('Cannot delete organization type that is being used by organizations');
            }
            await this.prisma.organizationType.update({
                where: { id },
                data: { deleted: true }
            });
            return { message: 'Organization type deleted successfully' };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to delete organization type with ID ${id}`, error.stack);
            throw new Error('Failed to delete organization type');
        }
    }
};
exports.OrganizationTypesService = OrganizationTypesService;
exports.OrganizationTypesService = OrganizationTypesService = OrganizationTypesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], OrganizationTypesService);
//# sourceMappingURL=organization-types.service.js.map