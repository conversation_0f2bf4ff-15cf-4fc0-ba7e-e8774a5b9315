import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { FundingSourcesService } from './funding-sources.service';
import { CreateFundingSourceDto, UpdateFundingSourceDto, FundingSourceResponseDto } from './dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { Public } from '../auth/decorator/public.decorator';

@ApiTags('funding-sources')
@Controller('funding-sources')
export class FundingSourcesController {
  constructor(private readonly fundingSourcesService: FundingSourcesService) {}

  @Post()
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new funding source (Admin only)' })
  @ApiResponse({ status: 201, description: 'Funding source created successfully', type: FundingSourceResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 409, description: 'Funding source with name already exists' })
  async create(@Body() createFundingSourceDto: CreateFundingSourceDto, @Request() req: any) {
    return this.fundingSourcesService.create(createFundingSourceDto, req.user.sub);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all funding sources (Public access)' })
  @ApiResponse({ status: 200, description: 'List of funding sources', type: [FundingSourceResponseDto] })
  async findAll() {
    return this.fundingSourcesService.findAll();
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get funding source by ID (Public access)' })
  @ApiResponse({ status: 200, description: 'Funding source details', type: FundingSourceResponseDto })
  @ApiResponse({ status: 404, description: 'Funding source not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.fundingSourcesService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update funding source (Admin only)' })
  @ApiResponse({ status: 200, description: 'Funding source updated successfully', type: FundingSourceResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Funding source not found' })
  @ApiResponse({ status: 409, description: 'Funding source with name already exists' })
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateFundingSourceDto: UpdateFundingSourceDto, @Request() req: any) {
    return this.fundingSourcesService.update(id, updateFundingSourceDto, req.user.sub);
  }

  @Delete(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete funding source (Admin only)' })
  @ApiResponse({ status: 200, description: 'Funding source deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Funding source not found' })
  @ApiResponse({ status: 409, description: 'Cannot delete funding source that is being used' })
  async remove(@Param('id', ParseIntPipe) id: number, @Request() req: any) {
    return this.fundingSourcesService.remove(id, req.user.sub);
  }
}
