import { PrismaService } from '../prisma/prisma.service';
import { CreateOrganizationTypeDto, UpdateOrganizationTypeDto } from './dto';
export declare class OrganizationTypesService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    findAll(): Promise<{
        id: number;
        typeName: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        typeName: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    create(createOrganizationTypeDto: CreateOrganizationTypeDto, currentUserId: string): Promise<{
        id: number;
        typeName: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    update(id: number, updateOrganizationTypeDto: UpdateOrganizationTypeDto, currentUserId: string): Promise<{
        id: number;
        typeName: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    remove(id: number, currentUserId: string): Promise<{
        message: string;
    }>;
}
