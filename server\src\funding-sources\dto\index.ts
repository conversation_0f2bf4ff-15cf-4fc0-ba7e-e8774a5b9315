import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsBoolean } from 'class-validator';

export class CreateFundingSourceDto {
    @ApiProperty({ description: 'The name of the funding source' })
    @IsNotEmpty()
    @IsString()
    sourceName: string;
}

export class UpdateFundingSourceDto {
    @ApiProperty({ description: 'The name of the funding source', required: false })
    @IsOptional()
    @IsString()
    sourceName?: string;
}

export class FundingSourceResponseDto {
    @ApiProperty()
    id: number;

    @ApiProperty({ description: 'The name of the funding source' })
    sourceName: string;

    @ApiProperty({ description: 'When the funding source was created' })
    createdAt: Date;

    @ApiProperty({ description: 'When the funding source was last updated' })
    updatedAt: Date;

    @ApiProperty({ description: 'Whether the funding source has been soft deleted' })
    deleted: boolean;
}
