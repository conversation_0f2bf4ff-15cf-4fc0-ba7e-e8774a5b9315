export declare class CreateProjectDto {
    name: string;
    description?: string;
    duration: number;
    budgetTypeId: number;
    fundingUnitId: number;
    fundingSourceId: number;
    organizationId: number;
    projectDocumentId: number;
    mouApplicationId: number;
}
export declare class UpdateProjectDto {
    name?: string;
    description?: string;
    duration?: number;
    budgetTypeId?: number;
    fundingUnitId?: number;
    fundingSourceId?: number;
    projectDocumentId?: number;
}
export declare class ProjectResponseDto {
    id: number;
    name: string;
    description?: string;
    duration: number;
    budgetTypeId: number;
    fundingUnitId: number;
    fundingSourceId: number;
    organizationId: number;
    projectDocumentId: number;
    mouApplicationId: number;
    createdAt: Date;
    updatedAt: Date;
    deleted: boolean;
}
export declare class ProjectWithRelationsDto extends ProjectResponseDto {
    budgetType?: {
        id: number;
        typeName: string;
    };
    fundingUnit?: {
        id: number;
        unitName: string;
    };
    fundingSource?: {
        id: number;
        sourceName: string;
    };
    organization?: {
        id: number;
        organizationName: string;
    };
    projectDocument?: {
        id: number;
        name: string;
        description?: string;
    };
    mouApplication?: {
        id: number;
        applicationKey: string;
    };
    activities?: Array<{
        id: number;
        name: string;
        description?: string;
        startDate: Date;
        endDate: Date;
        implementer: string;
        implementerUnit: string;
        fiscalYear: number;
    }>;
    approvalSteps?: Array<{
        id: number;
        status: string;
        comment?: string;
        role: string;
        reviewer: {
            id: number;
            firstName: string;
            lastName: string;
        };
    }>;
}
