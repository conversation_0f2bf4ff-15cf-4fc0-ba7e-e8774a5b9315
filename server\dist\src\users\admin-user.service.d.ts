import { PrismaService } from '../prisma/prisma.service';
import { User, ActivityLog } from '@prisma/client';
import { CreateUserDto, UpdateUserDto, AssignRolesDto, FilterUsersDto, UserActivityDto, BulkUserActionDto } from './dto/admin-user.dto';
export declare class AdminUserService {
    private prisma;
    constructor(prisma: PrismaService);
    createUser(dto: CreateUserDto): Promise<Omit<User, 'password'>>;
    findAllUsers(filters: FilterUsersDto): Promise<{
        data: Array<Omit<User, 'password'>>;
        meta: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    findOne(id: number): Promise<Omit<User, 'password'>>;
    updateUser(id: number, dto: UpdateUserDto): Promise<Omit<User, 'password'>>;
    assignRoles(id: number, dto: AssignRolesDto): Promise<Omit<User, 'password'>>;
    getUserActivity(dto: UserActivityDto): Promise<{
        data: Array<ActivityLog & {
            user: Pick<User, 'firstName' | 'lastName' | 'email'>;
        }>;
        meta: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    bulkUpdateUsers(dto: BulkUserActionDto): Promise<Array<Omit<User, 'password'>>>;
    resetUserPassword(id: number, newPassword: string): Promise<{
        message: string;
    }>;
    deactivateUser(id: number): Promise<Omit<User, 'password'>>;
    reactivateUser(id: number): Promise<Omit<User, 'password'>>;
}
