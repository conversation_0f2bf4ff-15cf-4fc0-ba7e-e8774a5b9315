"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PartiesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const parties_service_1 = require("./parties.service");
const create_party_dto_1 = require("./dto/create-party.dto");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
const roles_guard_1 = require("../auth/guard/roles.guard");
const roles_decorator_1 = require("../auth/decorator/roles.decorator");
const dto_1 = require("../auth/dto");
let PartiesController = class PartiesController {
    constructor(partiesService) {
        this.partiesService = partiesService;
    }
    async create(createPartyDto) {
        return this.partiesService.create(createPartyDto);
    }
    async findAll(page, limit, organizationId) {
        const pageNum = page ? parseInt(page, 10) : 1;
        const limitNum = limit ? parseInt(limit, 10) : 10;
        const orgIdNum = organizationId ? parseInt(organizationId, 10) : undefined;
        return this.partiesService.findAll(pageNum, limitNum, orgIdNum);
    }
    async getPartiesByOrganization(organizationId) {
        return this.partiesService.getPartiesByOrganization(organizationId);
    }
    async findByPartyId(partyId) {
        return this.partiesService.findByPartyId(partyId);
    }
    async findOne(id) {
        return this.partiesService.findOne(id);
    }
    async update(id, updatePartyDto) {
        return this.partiesService.update(id, updatePartyDto);
    }
    async remove(id) {
        return this.partiesService.remove(id);
    }
};
exports.PartiesController = PartiesController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new party' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Party created successfully',
        type: create_party_dto_1.PartyWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Related entity not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Party ID already exists' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_party_dto_1.CreatePartyDto]),
    __metadata("design:returntype", Promise)
], PartiesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get all parties with pagination' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10)' }),
    (0, swagger_1.ApiQuery)({ name: 'organizationId', required: false, type: Number, description: 'Filter by organization ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of parties',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/PartyWithRelationsDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('organizationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], PartiesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('by-organization/:organizationId'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get parties by organization ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of parties for the organization',
        type: [create_party_dto_1.PartyWithRelationsDto],
    }),
    __param(0, (0, common_1.Param)('organizationId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PartiesController.prototype, "getPartiesByOrganization", null);
__decorate([
    (0, common_1.Get)('by-party-id/:partyId'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get a party by party ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Party details',
        type: create_party_dto_1.PartyWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Party not found' }),
    __param(0, (0, common_1.Param)('partyId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PartiesController.prototype, "findByPartyId", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get a party by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Party details',
        type: create_party_dto_1.PartyWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Party not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PartiesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Update a party' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Party updated successfully',
        type: create_party_dto_1.PartyWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Party not found' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, create_party_dto_1.UpdatePartyDto]),
    __metadata("design:returntype", Promise)
], PartiesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a party (soft delete)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Party deleted successfully',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Party not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Party has an associated MoU' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], PartiesController.prototype, "remove", null);
exports.PartiesController = PartiesController = __decorate([
    (0, swagger_1.ApiTags)('Parties'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('parties'),
    __metadata("design:paramtypes", [parties_service_1.PartiesService])
], PartiesController);
//# sourceMappingURL=parties.controller.js.map