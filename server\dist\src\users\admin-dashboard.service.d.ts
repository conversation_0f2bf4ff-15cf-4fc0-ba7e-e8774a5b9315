import { PrismaService } from '../prisma/prisma.service';
export declare class AdminDashboardService {
    private prisma;
    constructor(prisma: PrismaService);
    getUserStatistics(): Promise<{
        totalUsers: number;
        activeUsers: number;
        inactiveUsers: number;
        recentlyActive: number;
        roleDistribution: {
            roles: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.UserGroupByOutputType, "role"[]> & {
                _count: {
                    role: number;
                };
            })[];
        };
        departmentDistribution: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.UserGroupByOutputType, "organizationId"[]> & {
            _count: {
                organizationId: number;
            };
        })[];
    }>;
    getActivityMetrics(startDate?: Date, endDate?: Date): Promise<{
        loginActivity: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.ActivityLogGroupByOutputType, "createdAt"[]> & {
            _count: {
                action: number;
            };
        })[];
        userActions: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.ActivityLogGroupByOutputType, "action"[]> & {
            _count: {
                action: number;
            };
        })[];
        reviewActivity: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.ApprovalStepGroupByOutputType, "status"[]> & {
            _count: {
                status: number;
            };
        })[];
        approvalActivity: (import("@prisma/client").Prisma.PickEnumerable<import("@prisma/client").Prisma.ApprovalStepGroupByOutputType, "status"[]> & {
            _count: {
                status: number;
            };
        })[];
    }>;
    getSystemHealth(): Promise<{
        activeApplications: number;
        pendingReviews: number;
        pendingApprovals: number;
        recentErrors: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            mouApplicationId: number | null;
            details: import("@prisma/client/runtime/library").JsonValue;
            action: string;
            category: string;
            importance: string;
            ipAddress: string | null;
            userAgent: string | null;
        }[];
        systemStatus: {
            status: string;
            lastCheck: Date;
            components: {
                database: string;
                email: string;
                storage: string;
                notifications: string;
            };
        };
    }>;
    getPerformanceMetrics(): Promise<{
        averageReviewTime: number;
        averageApprovalTime: number;
        applicationThroughput: {
            status: string;
            _count: {
                status: number;
            };
        }[];
        userProductivity: any[];
    }>;
    private getRoleDistribution;
    private getDepartmentDistribution;
    private getLoginActivity;
    private getUserActions;
    private getReviewActivity;
    private getApprovalActivity;
    private calculateAverageReviewTime;
    private calculateAverageApprovalTime;
    private calculateApplicationThroughput;
    private calculateUserProductivity;
    private getDateFilter;
    private getSystemStatus;
}
