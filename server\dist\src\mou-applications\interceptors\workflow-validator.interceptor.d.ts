import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { PrismaService } from '../../prisma/prisma.service';
export declare class WorkflowValidatorInterceptor implements NestInterceptor {
    private prisma;
    constructor(prisma: PrismaService);
    intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>>;
    private extractApplicationId;
    private determineAction;
    private validateWorkflowTransition;
    private validateReviewTransition;
    private validateExpertAssignment;
    private validateModificationRequest;
    private validateApprovalTransition;
    private validateStatusUpdate;
    private getNextApproverRole;
}
