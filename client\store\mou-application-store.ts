import { create } from "zustand";
import { persist } from "zustand/middleware";
import {
  MouApplicationData,
  Party,
  Project,
  Activity,
  Document,
  ApplicationStatus,
  DocumentType
} from "@/types/mou-application";
import { demoOrganization } from "@/data/mock-data";

interface MouApplicationStore {
  data: MouApplicationData;
  lastSaved?: Date;
  isSubmitting: boolean;

  // Navigation
  setCurrentStep: (step: number) => void;

  // MoU Details
  updateMouDetails: (duration: number, reason?: string) => void;
  setOrganizationName: (name: string) => void;

  // Parties
  addParty: (party: Omit<Party, "id">) => void;
  removeParty: (partyId: string) => void;
  updateParty: (partyId: string, party: Partial<Party>) => void;

  // Projects
  addProject: (project: Omit<Project, "id" | "totalBudget">) => void;
  removeProject: (projectId: string) => void;
  updateProject: (projectId: string, project: Partial<Project>) => void;

  // Activities
  addActivity: (activity: Omit<Activity, "id">) => void;
  removeActivity: (activityId: string) => void;
  updateActivity: (activityId: string, activity: Partial<Activity>) => void;

  // Documents
  uploadDocument: (documentType: DocumentType, file: File) => void;
  removeDocument: (documentType: DocumentType) => void;
  updateDocumentProgress: (documentType: DocumentType, progress: number) => void;

  // Auto-save
  setLastSaved: (date: Date) => void;

  // Submission
  setSubmitting: (isSubmitting: boolean) => void;
  setStatus: (status: ApplicationStatus) => void;

  // Utility
  resetStore: () => void;
  getCompletedSteps: () => number[];
  getProgressPercentage: () => number;
}

const initialState: MouApplicationData = {
  id: crypto.randomUUID(),
  organizationId: demoOrganization.id,
  organizationName: demoOrganization.name,
  mouDuration: 1,
  parties: [],
  projects: [],
  activities: [],
  documents: [
    { id: crypto.randomUUID(), type: "MEMO_OBJECTIVE", file: null, uploaded: false },
    { id: crypto.randomUUID(), type: "STRATEGIC_PLAN", file: null, uploaded: false },
    { id: crypto.randomUUID(), type: "CAPACITY_BUILDING", file: null, uploaded: false },
    { id: crypto.randomUUID(), type: "MEMO_FUNDS", file: null, uploaded: false },
  ],
  currentStep: 1,
  status: "DRAFT",
};

export const useMouApplicationStore = create<MouApplicationStore>()(
  persist(
    (set, get) => ({
      data: initialState,
      lastSaved: undefined,
      isSubmitting: false,

      // Navigation
      setCurrentStep: (step) =>
        set((state) => ({ data: { ...state.data, currentStep: step } })),

      // MoU Details
      updateMouDetails: (duration, reason) =>
        set((state) => ({
          data: {
            ...state.data,
            mouDuration: duration,
            extendedDurationReason: reason,
          },
        })),

      setOrganizationName: (name) =>
        set((state) => ({
          data: { ...state.data, organizationName: name },
        })),

      // Parties
      addParty: (party) =>
        set((state) => ({
          data: {
            ...state.data,
            parties: [...state.data.parties, { ...party, id: crypto.randomUUID() }],
          },
        })),

      removeParty: (partyId) =>
        set((state) => ({
          data: {
            ...state.data,
            parties: state.data.parties.filter((p) => p.id !== partyId),
          },
        })),

      updateParty: (partyId, party) =>
        set((state) => ({
          data: {
            ...state.data,
            parties: state.data.parties.map((p) =>
              p.id === partyId ? { ...p, ...party } : p
            ),
          },
        })),

      // Projects
      addProject: (project) => {
        const totalBudget = project.fiscalYearBudgets.reduce(
          (sum: number, fy) => sum + fy.budget,
          0
        );
        set((state) => ({
          data: {
            ...state.data,
            projects: [
              ...state.data.projects,
              { ...project, id: crypto.randomUUID(), totalBudget },
            ],
          },
        }));
      },
      
      removeProject: (projectId) =>
        set((state) => ({
          data: {
            ...state.data,
            projects: state.data.projects.filter((p) => p.id !== projectId),
            activities: state.data.activities.filter(
              (a) => a.projectId !== projectId
            ),
          },
        })),
      
      updateProject: (projectId, project) => {
        set((state) => {
          const updatedProjects = state.data.projects.map((p) => {
            if (p.id === projectId) {
              const updatedProject = { ...p, ...project };
              // Recalculate total budget if fiscal year budgets were updated
              if (project.fiscalYearBudgets) {
                updatedProject.totalBudget = project.fiscalYearBudgets.reduce(
                  (sum: number, fy) => sum + fy.budget,
                  0
                );
              }
              return updatedProject;
            }
            return p;
          });
          return {
            data: {
              ...state.data,
              projects: updatedProjects,
            },
          };
        });
      },

      // Activities
      addActivity: (activity) =>
        set((state) => ({
          data: {
            ...state.data,
            activities: [...state.data.activities, { ...activity, id: crypto.randomUUID() }],
          },
        })),

      removeActivity: (activityId) =>
        set((state) => ({
          data: {
            ...state.data,
            activities: state.data.activities.filter((a) => a.id !== activityId),
          },
        })),

      updateActivity: (activityId, activity) =>
        set((state) => ({
          data: {
            ...state.data,
            activities: state.data.activities.map((a) =>
              a.id === activityId ? { ...a, ...activity } : a
            ),
          },
        })),

      // Documents
      uploadDocument: (documentType: DocumentType, file: File) =>
        set((state) => ({
          data: {
            ...state.data,
            documents: state.data.documents.map((d) =>
              d.type === documentType
                ? { ...d, file, uploaded: true }
                : d
            ),
          },
        })),

      removeDocument: (documentType: DocumentType) =>
        set((state) => ({
          data: {
            ...state.data,
            documents: state.data.documents.map((d) =>
              d.type === documentType
                ? { ...d, file: null, uploaded: false }
                : d
            ),
          },
        })),

      updateDocumentProgress: (documentType: DocumentType, progress: number) =>
        set((state) => ({
          data: {
            ...state.data,
            documents: state.data.documents.map((d) =>
              d.type === documentType
                ? { ...d, uploadProgress: progress }
                : d
            ),
          },
        })),

      // Auto-save
      setLastSaved: (date: Date) =>
        set({ lastSaved: date }),

      // Submission
      setSubmitting: (isSubmitting: boolean) =>
        set({ isSubmitting }),

      setStatus: (status: ApplicationStatus) =>
        set((state) => ({
          data: { ...state.data, status },
        })),

      // Utility
      getCompletedSteps: () => {
        const state = get();
        const completed: number[] = [];

        // Step 1: MoU Details
        if (state.data.mouDuration > 0) completed.push(1);

        // Step 2: Parties
        if (state.data.parties.length >= 2) completed.push(2);

        // Step 3: Projects
        if (state.data.projects.length > 0) completed.push(3);

        // Step 4: Activities
        if (state.data.activities.length > 0) completed.push(4);

        // Step 5: Documents
        const uploadedDocs = state.data.documents.filter(d => d.uploaded);
        if (uploadedDocs.length === 4) completed.push(5);

        // Step 6: Review (always accessible if previous steps are complete)
        if (completed.length === 5) completed.push(6);

        return completed;
      },

      getProgressPercentage: () => {
        const completed = get().getCompletedSteps();
        return Math.round((completed.length / 6) * 100);
      },

      resetStore: () => set({
        data: initialState,
        lastSaved: undefined,
        isSubmitting: false
      }),
    }),
    {
      name: "mou-application-store",
    }
  )
);
