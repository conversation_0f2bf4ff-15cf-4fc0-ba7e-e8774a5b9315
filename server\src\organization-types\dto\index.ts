import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class CreateOrganizationTypeDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    typeName: string;
}

export class UpdateOrganizationTypeDto {
    @ApiProperty({ required: false })
    @IsOptional()
    @IsString()
    typeName?: string;
}

export class OrganizationTypeResponseDto {
    @ApiProperty()
    id: number;

    @ApiProperty()
    typeName: string;

    @ApiProperty()
    createAt: Date;

    @ApiProperty()
    updatedAt: Date;

    @ApiProperty()
    deleted: boolean;
}
