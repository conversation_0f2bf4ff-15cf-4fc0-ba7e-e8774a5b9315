import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export type Party = {
  id: string
  name: string
  signatoryName: string
  position: string
  responsibilities: string
}

export type ProjectGoal = {
  id: string
  description: string
  isOverallGoal: boolean
}

export type FiscalYearBudget = {
  id: string
  fiscalYear: string
  budget: number
}

export type Project = {
  id: string
  name: string
  fundingSource: string
  fundingUnit: string
  budgetType: string
  currency: string
  startDate: string
  endDate: string
  fiscalYearBudgets: FiscalYearBudget[]
  goals: ProjectGoal[]
  totalBudget: number
}

export type Activity = {
  id: string
  projectId: string
  name: string
  implementor: string
  implementingUnit: string
  fiscalYear: string
  startDate: string
  endDate: string
  domain: string
  subDomain: string
  subDomainFunction: string
  subFunction: string
  inputCategory: string
  activityInput: string
  geographicLevel: 'Provinces' | 'Central'
  budgetAllocations: {
    id: string
    location: string // district name or central level
    budget: number
  }[]
}

export type Document = {
  id: string
  type: 'LongTermObjective' | 'StrategicPlan' | 'CapacityBuildingPlan' | 'FundingSourceMemo'
  file: File | null
  uploadProgress: number
}

type MouState = {
  currentStep: number
  organizationName: string
  mouDuration: number
  durationReason: string
  parties: Party[]
  projects: Project[]
  activities: Activity[]
  documents: Document[]
  lastSaved: Date | null
  isSubmitting: boolean
  setCurrentStep: (step: number) => void
  setOrganizationName: (name: string) => void
  setMouDuration: (duration: number) => void
  setDurationReason: (reason: string) => void
  setOrganizationDetails: (name: string, duration: number, reason: string) => void
  addParty: (party: Omit<Party, 'id'>) => void
  removeParty: (id: string) => void
  updateParty: (id: string, party: Partial<Party>) => void
  clearParties: () => void
  initializeDefaultParties: () => void
  initializePartnerParty: (organizationName: string, signatoryName: string) => void
  addProject: (project: Omit<Project, 'id' | 'totalBudget'>) => void
  removeProject: (id: string) => void
  updateProject: (id: string, project: Partial<Project>) => void
  clearProjects: () => void
  addActivity: (activity: Omit<Activity, 'id'>) => void
  removeActivity: (id: string) => void
  updateActivity: (id: string, activity: Partial<Activity>) => void
  clearActivities: () => void
  updateDocument: (type: Document['type'], file: File) => void
  setSubmitting: (isSubmitting: boolean) => void
}

export const useMouStore = create<MouState>()(
  persist(
    (set, get) => ({
      currentStep: 1,
      organizationName: '',
      mouDuration: 1,
      durationReason: '',
      parties: [],
      projects: [],
      activities: [],
      documents: [
        { id: '1', type: 'LongTermObjective', file: null, uploadProgress: 0 },
        { id: '2', type: 'StrategicPlan', file: null, uploadProgress: 0 },
        { id: '3', type: 'CapacityBuildingPlan', file: null, uploadProgress: 0 },
        { id: '4', type: 'FundingSourceMemo', file: null, uploadProgress: 0 }
      ],
      lastSaved: null,
      isSubmitting: false,

      setCurrentStep: (step) => set({ currentStep: step }),

      setOrganizationName: (name) => set({ organizationName: name, lastSaved: new Date() }),

      setMouDuration: (duration) => set({ mouDuration: duration, lastSaved: new Date() }),

      setDurationReason: (reason) => set({ durationReason: reason, lastSaved: new Date() }),

      setOrganizationDetails: (name, duration, reason) =>
        set({
          organizationName: name,
          mouDuration: duration,
          durationReason: reason,
          lastSaved: new Date()
        }),

      addParty: (party) =>
        set((state) => ({
          parties: [...state.parties, { ...party, id: crypto.randomUUID() }],
          lastSaved: new Date()
        })),

      removeParty: (id) =>
        set((state) => ({
          parties: state.parties.filter((p) => p.id !== id),
          lastSaved: new Date()
        })),

      updateParty: (id, party) =>
        set((state) => ({
          parties: state.parties.map((p) =>
            p.id === id ? { ...p, ...party } : p
          ),
          lastSaved: new Date()
        })),

      clearParties: () => set({ parties: [], lastSaved: new Date() }),

      initializeDefaultParties: () =>
        set((state) => {
          if (state.parties.length === 0) {
            const defaultParties = [
              {
                id: crypto.randomUUID(),
                name: '',
                signatoryName: '',
                position: '',
                responsibilities: ''
              },
              {
                id: crypto.randomUUID(),
                name: '',
                signatoryName: '',
                position: '',
                responsibilities: ''
              }
            ]
            return {
              parties: defaultParties,
              lastSaved: new Date()
            }
          }
          return state
        }),

      initializePartnerParty: (organizationName: string, signatoryName: string) =>
        set((state) => {
          if (state.parties.length === 0) {
            const partnerParty = {
              id: crypto.randomUUID(),
              name: organizationName,
              signatoryName: signatoryName,
              position: '',
              responsibilities: ''
            }
            return {
              parties: [partnerParty],
              lastSaved: new Date()
            }
          }
          return state
        }),

      addProject: (project) =>
        set((state) => ({
          projects: [
            ...state.projects,
            {
              ...project,
              id: crypto.randomUUID(),
              totalBudget: project.fiscalYearBudgets.reduce(
                (sum, fyb) => sum + fyb.budget,
                0
              )
            }
          ],
          lastSaved: new Date()
        })),

      removeProject: (id) =>
        set((state) => ({
          projects: state.projects.filter((p) => p.id !== id),
          activities: state.activities.filter((a) => a.projectId !== id),
          lastSaved: new Date()
        })),

      updateProject: (id, project) =>
        set((state) => ({
          projects: state.projects.map((p) =>
            p.id === id
              ? {
                  ...p,
                  ...project,
                  totalBudget:
                    project.fiscalYearBudgets?.reduce(
                      (sum, fyb) => sum + fyb.budget,
                      0
                    ) ?? p.totalBudget
                }
              : p
          ),
          lastSaved: new Date()
        })),

      clearProjects: () => set({ projects: [], lastSaved: new Date() }),

      addActivity: (activity) =>
        set((state) => ({
          activities: [...state.activities, { ...activity, id: crypto.randomUUID() }],
          lastSaved: new Date()
        })),

      removeActivity: (id) =>
        set((state) => ({
          activities: state.activities.filter((a) => a.id !== id),
          lastSaved: new Date()
        })),

      updateActivity: (id, activity) =>
        set((state) => ({
          activities: state.activities.map((a) =>
            a.id === id ? { ...a, ...activity } : a
          ),
          lastSaved: new Date()
        })),

      clearActivities: () => set({ activities: [], lastSaved: new Date() }),

      updateDocument: (type, file) =>
        set((state) => ({
          documents: state.documents.map((d) =>
            d.type === type ? { ...d, file, uploadProgress: 0 } : d
          ),
          lastSaved: new Date()
        })),

      setSubmitting: (isSubmitting) => set({ isSubmitting })
    }),
    {
      name: 'mou-storage'
    }
  )
)
