import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { 
  CreateInputDto, 
  UpdateInputDto, 
  CreateInputSubclassDto, 
  UpdateInputSubclassDto 
} from './dto/create-input.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class InputsService {
  constructor(private prisma: PrismaService) {}

  // Input methods
  async createInput(createInputDto: CreateInputDto) {
    try {
      // Validate input subclass exists
      const inputSubclass = await this.prisma.inputSubclass.findUnique({
        where: { id: createInputDto.inputSubclassId },
      });

      if (!inputSubclass) {
        throw new NotFoundException(`Input subclass with ID ${createInputDto.inputSubclassId} not found`);
      }

      const input = await this.prisma.input.create({
        data: createInputDto,
        include: {
          inputSubclass: true,
          activities: {
            include: {
              project: true,
            },
          },
        },
      });

      return input;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2003') {
          throw new BadRequestException('Invalid input subclass reference');
        }
      }
      throw error;
    }
  }

  async findAllInputs(page: number = 1, limit: number = 10, inputSubclassId?: number) {
    const skip = (page - 1) * limit;
    
    const where: Prisma.InputWhereInput = {
      deleted: false,
      ...(inputSubclassId && { inputSubclassId }),
    };

    const [inputs, total] = await Promise.all([
      this.prisma.input.findMany({
        where,
        skip,
        take: limit,
        include: {
          inputSubclass: true,
          activities: {
            include: {
              project: true,
            },
          },
        },
        orderBy: {
          name: 'asc',
        },
      }),
      this.prisma.input.count({ where }),
    ]);

    return {
      data: inputs,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOneInput(id: number) {
    const input = await this.prisma.input.findFirst({
      where: {
        id,
        deleted: false,
      },
      include: {
        inputSubclass: true,
        activities: {
          include: {
            project: {
              include: {
                mouApplication: true,
              },
            },
            domainIntervention: true,
          },
        },
      },
    });

    if (!input) {
      throw new NotFoundException(`Input with ID ${id} not found`);
    }

    return input;
  }

  async updateInput(id: number, updateInputDto: UpdateInputDto) {
    // Check if input exists
    const existingInput = await this.prisma.input.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingInput) {
      throw new NotFoundException(`Input with ID ${id} not found`);
    }

    // Validate input subclass if being updated
    if (updateInputDto.inputSubclassId) {
      const inputSubclass = await this.prisma.inputSubclass.findUnique({
        where: { id: updateInputDto.inputSubclassId },
      });

      if (!inputSubclass) {
        throw new NotFoundException(`Input subclass with ID ${updateInputDto.inputSubclassId} not found`);
      }
    }

    try {
      const updatedInput = await this.prisma.input.update({
        where: { id },
        data: updateInputDto,
        include: {
          inputSubclass: true,
          activities: {
            include: {
              project: true,
            },
          },
        },
      });

      return updatedInput;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2003') {
          throw new BadRequestException('Invalid input subclass reference');
        }
      }
      throw error;
    }
  }

  async removeInput(id: number) {
    // Check if input exists
    const existingInput = await this.prisma.input.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingInput) {
      throw new NotFoundException(`Input with ID ${id} not found`);
    }

    // Check if input is being used by activities
    const activitiesUsingInput = await this.prisma.activity.findMany({
      where: {
        inputId: id,
        deleted: false,
      },
    });

    if (activitiesUsingInput.length > 0) {
      throw new ConflictException('Cannot delete input that is being used by activities');
    }

    // Soft delete
    await this.prisma.input.update({
      where: { id },
      data: { deleted: true },
    });

    return { message: 'Input deleted successfully' };
  }

  // Input Subclass methods
  async createInputSubclass(createInputSubclassDto: CreateInputSubclassDto) {
    try {
      const inputSubclass = await this.prisma.inputSubclass.create({
        data: createInputSubclassDto,
        include: {
          input: true,
        },
      });

      return inputSubclass;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Subclass ID must be unique');
        }
      }
      throw error;
    }
  }

  async findAllInputSubclasses() {
    const inputSubclasses = await this.prisma.inputSubclass.findMany({
      where: {
        deleted: false,
      },
      include: {
        input: true,
      },
      orderBy: {
        subclassId: 'asc',
      },
    });

    return inputSubclasses;
  }

  async findOneInputSubclass(id: number) {
    const inputSubclass = await this.prisma.inputSubclass.findFirst({
      where: {
        id,
        deleted: false,
      },
      include: {
        input: {
          include: {
            activities: {
              include: {
                project: true,
              },
            },
          },
        },
      },
    });

    if (!inputSubclass) {
      throw new NotFoundException(`Input subclass with ID ${id} not found`);
    }

    return inputSubclass;
  }

  async updateInputSubclass(id: number, updateInputSubclassDto: UpdateInputSubclassDto) {
    // Check if input subclass exists
    const existingInputSubclass = await this.prisma.inputSubclass.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingInputSubclass) {
      throw new NotFoundException(`Input subclass with ID ${id} not found`);
    }

    try {
      const updatedInputSubclass = await this.prisma.inputSubclass.update({
        where: { id },
        data: updateInputSubclassDto,
        include: {
          input: true,
        },
      });

      return updatedInputSubclass;
    } catch (error) {
      throw error;
    }
  }

  async removeInputSubclass(id: number) {
    // Check if input subclass exists
    const existingInputSubclass = await this.prisma.inputSubclass.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingInputSubclass) {
      throw new NotFoundException(`Input subclass with ID ${id} not found`);
    }

    // Check if input subclass is being used by inputs
    const inputsUsingSubclass = await this.prisma.input.findMany({
      where: {
        inputSubclass: {
          some: { id: id }
        },
        deleted: false,
      },
    });

    if (inputsUsingSubclass.length > 0) {
      throw new ConflictException('Cannot delete input subclass that is being used by inputs');
    }

    // Soft delete
    await this.prisma.inputSubclass.update({
      where: { id },
      data: { deleted: true },
    });

    return { message: 'Input subclass deleted successfully' };
  }

  async getInputsBySubclass(inputSubclassId: number) {
    const inputs = await this.prisma.input.findMany({
      where: {
        inputSubclass: {
          some: { id: inputSubclassId }
        },
        deleted: false,
      },
      include: {
        inputSubclass: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    return inputs;
  }
}
