"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinancingAgentsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let FinancingAgentsService = class FinancingAgentsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createFinancingAgentDto, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: parseInt(userId, 10) },
        });
        if (!user || user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only admins can create financing agents');
        }
        const existingAgent = await this.prisma.financingAgent.findUnique({
            where: { agentName: createFinancingAgentDto.agentName },
        });
        if (existingAgent) {
            throw new common_1.ConflictException('Financing agent with this name already exists');
        }
        return this.prisma.financingAgent.create({
            data: createFinancingAgentDto,
        });
    }
    async findAll() {
        return this.prisma.financingAgent.findMany({
            where: { deleted: false },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findOne(id) {
        const agent = await this.prisma.financingAgent.findUnique({
            where: { id, deleted: false },
        });
        if (!agent) {
            throw new common_1.NotFoundException('Financing agent not found');
        }
        return agent;
    }
    async update(id, updateFinancingAgentDto, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: parseInt(userId, 10) },
        });
        if (!user || user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only admins can update financing agents');
        }
        const existingAgent = await this.prisma.financingAgent.findUnique({
            where: { id, deleted: false },
        });
        if (!existingAgent) {
            throw new common_1.NotFoundException('Financing agent not found');
        }
        if (updateFinancingAgentDto.agentName && updateFinancingAgentDto.agentName !== existingAgent.agentName) {
            const conflictingAgent = await this.prisma.financingAgent.findUnique({
                where: { agentName: updateFinancingAgentDto.agentName },
            });
            if (conflictingAgent) {
                throw new common_1.ConflictException('Financing agent with this name already exists');
            }
        }
        return this.prisma.financingAgent.update({
            where: { id },
            data: updateFinancingAgentDto,
        });
    }
    async remove(id, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: parseInt(userId, 10) },
        });
        if (!user || user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only admins can delete financing agents');
        }
        const existingAgent = await this.prisma.financingAgent.findUnique({
            where: { id, deleted: false },
        });
        if (!existingAgent) {
            throw new common_1.NotFoundException('Financing agent not found');
        }
        return this.prisma.financingAgent.update({
            where: { id },
            data: { deleted: true },
        });
    }
};
exports.FinancingAgentsService = FinancingAgentsService;
exports.FinancingAgentsService = FinancingAgentsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], FinancingAgentsService);
//# sourceMappingURL=financing-agents.service.js.map