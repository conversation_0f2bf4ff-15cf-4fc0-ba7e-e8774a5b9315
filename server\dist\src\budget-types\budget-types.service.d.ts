import { PrismaService } from '../prisma/prisma.service';
import { CreateBudgetTypeDto, UpdateBudgetTypeDto } from './dto';
export declare class BudgetTypesService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    findAll(): Promise<{
        id: number;
        typeName: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        typeName: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    create(createBudgetTypeDto: CreateBudgetTypeDto, currentUserId: string): Promise<{
        id: number;
        typeName: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    update(id: number, updateBudgetTypeDto: UpdateBudgetTypeDto, currentUserId: string): Promise<{
        id: number;
        typeName: string;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    remove(id: number, currentUserId: string): Promise<{
        message: string;
    }>;
}
