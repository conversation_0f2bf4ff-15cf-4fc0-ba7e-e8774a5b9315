import { cn } from '@/lib/utils'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Save, ArrowRight } from 'lucide-react'
import { useStepNavigation } from '@/hooks/use-mou-form'
import { useDraftManager } from '@/hooks/use-draft-manager'
import { useMouStore } from '@/store/mou-store'
import { MouDetailsStep } from './wizard-steps/mou-details-step'
import { ActivitiesStep } from './wizard-steps/activities-step'
import { DocumentsStep } from './wizard-steps/documents-step'
import { PartyDetailsStep } from './wizard-steps/party-details-step'
import { ProjectsStep } from './wizard-steps/projects-step'
import { ReviewStep } from './wizard-steps/review-step'


const steps = [
  { id: 1, title: 'MoU Details', component: MouDetailsStep },
  { id: 2, title: 'Party Details', component: PartyDetailsStep },
  { id: 3, title: 'Projects', component: ProjectsStep },
  { id: 4, title: 'Activities', component: ActivitiesStep },
  { id: 5, title: 'Documents', component: DocumentsStep },
  { id: 6, title: 'Review & Submit', component: ReviewStep }
]

export function MouWizard() {
  const { currentStep, canGoNext, goToNextStep, goToPreviousStep } = useStepNavigation()
  const isSubmitting = useMouStore(state => state.isSubmitting)
  const lastSaved = useMouStore(state => state.lastSaved)
  const { isSaving, lastSaved: draftLastSaved, saveDraft } = useDraftManager()

  // Save and continue function
  const handleSaveAndContinue = async () => {
    const saveSuccess = await saveDraft()
    if (saveSuccess && canGoNext) {
      goToNextStep()
    }
  }
  
  const CurrentStepComponent = steps.find(step => step.id === currentStep)?.component

  return (
    <div className="container mx-auto py-8 max-w-7xl">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-3xl font-bold text-gray-900">MoU Application</h1>
          {lastSaved && (
            <p className="text-sm text-gray-500">
              Last saved: {new Date(lastSaved).toLocaleString()}
            </p>
          )}
        </div>
        
        <div className="relative">
          <div className="overflow-hidden h-2 mb-4 flex rounded bg-gray-200">
            {steps.map(step => (
              <div
                key={step.id}
                className={cn(
                  "transition-all duration-300 h-full",
                  currentStep >= step.id ? "bg-cyan-500" : "bg-transparent",
                )}
                style={{ width: `${100 / steps.length}%` }}
              />
            ))}
          </div>
          <div className="flex justify-between">
            {steps.map(step => (
              <div
                key={step.id}
                className={cn(
                  "flex flex-col items-center relative",
                  currentStep >= step.id ? "text-cyan-500" : "text-gray-400"
                )}
              >
                <div
                  className={cn(
                    "rounded-full transition-colors flex items-center justify-center w-8 h-8 py-3 border-2",
                    currentStep >= step.id
                      ? "border-cyan-500 bg-white"
                      : "border-gray-300 bg-white"
                  )}
                >
                  {step.id}
                </div>
                <div className="absolute -bottom-6 w-32 text-center text-sm">
                  {step.title}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <Card className="p-6 mt-16">
        {CurrentStepComponent && <CurrentStepComponent />}
        
        <div className="flex justify-between items-center mt-8">
          <Button
            variant="outline"
            onClick={goToPreviousStep}
            disabled={currentStep === 1 || isSubmitting}
          >
            Previous
          </Button>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-500 flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${isSaving ? 'bg-blue-500 animate-pulse' : 'bg-green-500'}`}></div>
                {isSaving ? 'Saving draft...' : 'Auto-saving enabled'}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={saveDraft}
                disabled={isSaving}
                className="text-xs"
              >
                <Save className="h-3 w-3 mr-1" />
                Save Draft
              </Button>
            </div>

            <div className="flex items-center gap-2">
              {canGoNext && currentStep < 6 && (
                <Button
                  variant="secondary"
                  onClick={handleSaveAndContinue}
                  disabled={isSaving || isSubmitting}
                  className="bg-cyan-50 border-cyan-200 text-cyan-700 hover:bg-cyan-100 hover:border-cyan-300"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isSaving ? 'Saving...' : 'Save & Continue'}
                  {!isSaving && <ArrowRight className="h-4 w-4 ml-2" />}
                </Button>
              )}

              <Button
                onClick={goToNextStep}
                disabled={!canGoNext || isSubmitting}
              >
                {currentStep === 6 ? 'Submit' : 'Next'}
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}
