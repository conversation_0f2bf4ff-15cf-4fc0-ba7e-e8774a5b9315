import { Injectable, NotFoundException, ConflictException, ForbiddenException, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateAddressDto, UpdateAddressDto, AddressType } from './dto';

@Injectable()
export class AddressesService {
    private readonly logger = new Logger(AddressesService.name);

    constructor(private prisma: PrismaService) {}

    async findByOrganization(organizationId: string) {
        try {
            const addresses = await this.prisma.address.findMany({
                where: {
                    organizationId: parseInt(organizationId, 10),
                    deleted: false
                },
                orderBy: { createdAt: 'desc' }
            });

            return addresses;
        } catch (error) {
            this.logger.error(`Failed to fetch addresses for organization ${organizationId}`, error.stack);
            throw new Error('Failed to fetch addresses');
        }
    }

    async findOne(id: string) {
        try {
            const address = await this.prisma.address.findFirst({
                where: {
                    id: parseInt(id, 10),
                    deleted: false
                }
            });

            if (!address) {
                throw new NotFoundException('Address not found');
            }

            return address;
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            this.logger.error(`Failed to fetch address ${id}`, error.stack);
            throw new Error('Failed to fetch address');
        }
    }

    async create(createAddressDto: CreateAddressDto, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Check if organization exists
            const organization = await this.prisma.organization.findUnique({
                where: { id: parseInt(createAddressDto.organizationId, 10) }
            });

            if (!organization) {
                throw new NotFoundException('Organization not found');
            }

            // Check permissions: Only ADMIN or users from the same organization can create addresses
            if (currentUser.role !== 'ADMIN' && currentUser.organizationId !== parseInt(createAddressDto.organizationId, 10)) {
                throw new ForbiddenException('You can only create addresses for your own organization');
            }

            // Check if organization already has this type of address
            const existingAddress = await this.prisma.address.findFirst({
                where: {
                    organizationId: parseInt(createAddressDto.organizationId, 10),
                    addressType: createAddressDto.addressType,
                    deleted: false
                }
            });

            if (existingAddress) {
                throw new ConflictException(`Organization already has a ${createAddressDto.addressType} address`);
            }

            // Check if organization would exceed 2 addresses limit
            const addressCount = await this.prisma.address.count({
                where: {
                    organizationId: parseInt(createAddressDto.organizationId, 10),
                    deleted: false
                }
            });

            if (addressCount >= 2) {
                throw new BadRequestException('Organization cannot have more than 2 addresses');
            }

            // Validate Rwanda address fields for Rwanda addresses
            if (createAddressDto.addressType === AddressType.RWANDA) {
                if (!createAddressDto.province || !createAddressDto.district) {
                    throw new BadRequestException('Rwanda addresses must include province and district');
                }
            }

            const address = await this.prisma.address.create({
                data: {
                    ...createAddressDto,
                    organizationId: parseInt(createAddressDto.organizationId, 10),
                }
            });

            return address;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || 
                error instanceof ForbiddenException || error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to create address', error.stack);
            throw new Error('Failed to create address');
        }
    }

    async update(id: string, updateAddressDto: UpdateAddressDto, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Get existing address
            const existingAddress = await this.prisma.address.findFirst({
                where: {
                    id: parseInt(id, 10),
                    deleted: false
                }
            });

            if (!existingAddress) {
                throw new NotFoundException('Address not found');
            }

            // Check permissions: Only ADMIN or users from the same organization can update addresses
            if (currentUser.role !== 'ADMIN' && currentUser.organizationId !== existingAddress.organizationId) {
                throw new ForbiddenException('You can only update addresses for your own organization');
            }

            // If changing address type, check for conflicts
            if (updateAddressDto.addressType && updateAddressDto.addressType !== existingAddress.addressType) {
                const conflictingAddress = await this.prisma.address.findFirst({
                    where: {
                        organizationId: existingAddress.organizationId,
                        addressType: updateAddressDto.addressType,
                        deleted: false,
                        id: { not: parseInt(id, 10) }
                    }
                });

                if (conflictingAddress) {
                    throw new ConflictException(`Organization already has a ${updateAddressDto.addressType} address`);
                }
            }

            // Validate Rwanda address fields if updating to Rwanda type
            const finalAddressType = updateAddressDto.addressType || existingAddress.addressType;
            if (finalAddressType === AddressType.RWANDA) {
                const finalProvince = updateAddressDto.province || existingAddress.province;
                const finalDistrict = updateAddressDto.district || existingAddress.district;
                
                if (!finalProvince || !finalDistrict) {
                    throw new BadRequestException('Rwanda addresses must include province and district');
                }
            }

            const address = await this.prisma.address.update({
                where: { id: parseInt(id, 10) },
                data: updateAddressDto
            });

            return address;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || 
                error instanceof ForbiddenException || error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to update address ${id}`, error.stack);
            throw new Error('Failed to update address');
        }
    }

    async remove(id: string, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Get existing address
            const existingAddress = await this.prisma.address.findFirst({
                where: {
                    id: parseInt(id, 10),
                    deleted: false
                }
            });

            if (!existingAddress) {
                throw new NotFoundException('Address not found');
            }

            // Check permissions: Only ADMIN or users from the same organization can delete addresses
            if (currentUser.role !== 'ADMIN' && currentUser.organizationId !== existingAddress.organizationId) {
                throw new ForbiddenException('You can only delete addresses for your own organization');
            }

            // Check if this would leave the organization with no addresses
            const totalAddressCount = await this.prisma.address.count({
                where: {
                    organizationId: existingAddress.organizationId,
                    deleted: false
                }
            });

            if (totalAddressCount <= 1) {
                throw new BadRequestException('Cannot delete the only address. Organizations must have at least one address (either Rwanda or headquarters).');
            }

            // Soft delete the address
            await this.prisma.address.update({
                where: { id: parseInt(id, 10) },
                data: { deleted: true }
            });

            return { message: 'Address deleted successfully' };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ForbiddenException || 
                error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to delete address ${id}`, error.stack);
            throw new Error('Failed to delete address');
        }
    }
}
