import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { OrganizationTypesService } from './organization-types.service';
import { CreateOrganizationTypeDto, UpdateOrganizationTypeDto, OrganizationTypeResponseDto } from './dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { Public } from '../auth/decorator/public.decorator';

@ApiTags('organization-types')
@Controller('organization-types')
export class OrganizationTypesController {
  constructor(private readonly organizationTypesService: OrganizationTypesService) {}

  @Post()
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new organization type (Admin only)' })
  @ApiResponse({ status: 201, description: 'Organization type created successfully', type: OrganizationTypeResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 409, description: 'Organization type with name already exists' })
  async create(@Body() createOrganizationTypeDto: CreateOrganizationTypeDto, @Request() req: any) {
    return this.organizationTypesService.create(createOrganizationTypeDto, req.user.sub);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all organization types (Public access)' })
  @ApiResponse({ status: 200, description: 'List of organization types', type: [OrganizationTypeResponseDto] })
  async findAll() {
    return this.organizationTypesService.findAll();
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get organization type by ID (Public access)' })
  @ApiResponse({ status: 200, description: 'Organization type details', type: OrganizationTypeResponseDto })
  @ApiResponse({ status: 404, description: 'Organization type not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.organizationTypesService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update organization type (Admin only)' })
  @ApiResponse({ status: 200, description: 'Organization type updated successfully', type: OrganizationTypeResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Organization type not found' })
  @ApiResponse({ status: 409, description: 'Organization type with name already exists' })
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateOrganizationTypeDto: UpdateOrganizationTypeDto, @Request() req: any) {
    return this.organizationTypesService.update(id, updateOrganizationTypeDto, req.user.sub);
  }

  @Delete(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete organization type (Admin only)' })
  @ApiResponse({ status: 200, description: 'Organization type deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Organization type not found' })
  @ApiResponse({ status: 409, description: 'Cannot delete organization type that is being used' })
  async remove(@Param('id', ParseIntPipe) id: number, @Request() req: any) {
    return this.organizationTypesService.remove(id, req.user.sub);
  }
}
