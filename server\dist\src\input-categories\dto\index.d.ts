export declare class CreateInputCategoryDto {
    categoryName: string;
    description?: string;
    parentId?: number;
}
export declare class UpdateInputCategoryDto {
    categoryName?: string;
    description?: string;
    parentId?: number;
}
export declare class InputCategoryResponseDto {
    id: number;
    categoryName: string;
    description?: string;
    parentId?: number;
    parent?: InputCategoryResponseDto;
    children?: InputCategoryResponseDto[];
    createdAt: Date;
    updatedAt: Date;
    deleted: boolean;
}
export declare class InputCategoryTreeResponseDto extends InputCategoryResponseDto {
    children: InputCategoryTreeResponseDto[];
}
