"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const email_service_1 = require("../email/email.service");
const client_1 = require("@prisma/client");
let NotificationService = class NotificationService {
    constructor(prisma, emailService) {
        this.prisma = prisma;
        this.emailService = emailService;
    }
    async notifyApplicationSubmitted(applicationId) {
        const application = await this.getApplicationWithDetails(applicationId);
        const coordinators = await this.prisma.user.findMany({
            where: {
                role: client_1.UserRole.PARTNER_COORDINATOR,
                isActive: true,
            },
        });
        for (const coordinator of coordinators) {
            await this.createNotification({
                userId: coordinator.id,
                title: 'New Application Submitted',
                message: `A new MoU application (${application.applicationKey}) has been submitted and requires your review.`,
                type: 'APPLICATION_SUBMITTED',
                applicationId,
            });
            await this.emailService.sendWelcomeEmail({
                email: coordinator.email,
                firstName: coordinator.firstName,
                lastName: coordinator.lastName,
            });
        }
    }
    async notifyTechnicalExpertAssigned(applicationId, expertId) {
        const application = await this.getApplicationWithDetails(applicationId);
        const expert = await this.prisma.user.findUnique({ where: { id: expertId } });
        await this.createNotification({
            userId: expertId,
            title: 'New Technical Review Assignment',
            message: `You have been assigned to review MoU application ${application.applicationKey}.`,
            type: 'TECHNICAL_REVIEW_ASSIGNED',
            applicationId,
        });
        await this.emailService.sendWelcomeEmail({
            email: expert.email,
            firstName: expert.firstName,
            lastName: expert.lastName,
        });
    }
    async notifyReviewCompleted(applicationId, reviewerId, reviewType) {
        const application = await this.getApplicationWithDetails(applicationId);
        const reviewer = await this.prisma.user.findUnique({ where: { id: reviewerId } });
        const coordinators = await this.prisma.user.findMany({
            where: {
                role: client_1.UserRole.PARTNER_COORDINATOR,
                isActive: true,
            },
        });
        for (const coordinator of coordinators) {
            await this.createNotification({
                userId: coordinator.id,
                title: `${reviewType} Review Completed`,
                message: `A ${reviewType.toLowerCase()} review has been completed for application ${application.applicationKey}.`,
                type: 'REVIEW_COMPLETED',
                applicationId,
            });
            await this.emailService.sendWelcomeEmail({
                email: coordinator.email,
                firstName: coordinator.firstName,
                lastName: coordinator.lastName,
            });
        }
    }
    async notifyModificationRequested(applicationId, requesterId) {
        const application = await this.getApplicationWithDetails(applicationId);
        const requester = await this.prisma.user.findUnique({ where: { id: requesterId } });
        const coordinators = await this.prisma.user.findMany({
            where: {
                role: client_1.UserRole.PARTNER_COORDINATOR,
                isActive: true,
            },
        });
        for (const coordinator of coordinators) {
            await this.createNotification({
                userId: coordinator.id,
                title: 'Modification Requested',
                message: `Modifications have been requested for application ${application.applicationKey}.`,
                type: 'MODIFICATION_REQUESTED',
                applicationId,
            });
            await this.emailService.sendWelcomeEmail({
                email: coordinator.email,
                firstName: coordinator.firstName,
                lastName: coordinator.lastName,
            });
        }
    }
    async notifyApprovalRequired(applicationId, approverRole) {
        const application = await this.getApplicationWithDetails(applicationId);
        const approvers = await this.prisma.user.findMany({
            where: {
                role: approverRole,
                isActive: true,
            },
        });
        for (const approver of approvers) {
            await this.createNotification({
                userId: approver.id,
                title: 'Approval Required',
                message: `Your approval is required for MoU application ${application.applicationKey}.`,
                type: 'APPROVAL_REQUIRED',
                applicationId,
            });
            await this.emailService.sendWelcomeEmail({
                email: approver.email,
                firstName: approver.firstName,
                lastName: approver.lastName,
            });
        }
    }
    async notifyApplicationApproved(applicationId) {
        const application = await this.getApplicationWithDetails(applicationId);
        const applicationWithOrg = await this.prisma.mouApplication.findUnique({
            where: { id: applicationId },
            include: {
                user: {
                    include: {
                        organization: true
                    }
                }
            }
        });
        const partner = applicationWithOrg?.user;
        if (partner) {
            await this.createNotification({
                userId: partner.id,
                title: 'Application Approved',
                message: `Your MoU application ${application.applicationKey} has been approved.`,
                type: 'APPLICATION_APPROVED',
                applicationId,
            });
            await this.emailService.sendWelcomeEmail({
                email: partner.email,
                firstName: partner.firstName,
                lastName: partner.lastName,
            });
        }
    }
    async createNotification(data) {
        console.log('Notification:', data);
        return { id: Date.now(), ...data };
    }
    async getApplicationWithDetails(applicationId) {
        return this.prisma.mouApplication.findUnique({
            where: { id: applicationId },
            include: {
                approvalSteps: true,
                projects: true,
            },
        });
    }
    async markNotificationAsRead(notificationId, userId) {
        console.log('Mark notification as read:', { notificationId, userId });
        return { id: notificationId, isRead: true, readAt: new Date() };
    }
    async getUserNotifications(userId, options = {}) {
        console.log('Get user notifications:', { userId, options });
        return [];
    }
    async getUnreadCount(userId) {
        console.log('Get unread count for user:', userId);
        return 0;
    }
};
exports.NotificationService = NotificationService;
exports.NotificationService = NotificationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        email_service_1.EmailService])
], NotificationService);
//# sourceMappingURL=notification.service.js.map