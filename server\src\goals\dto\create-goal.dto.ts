import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class CreateGoalDto {
  @ApiProperty({
    description: 'Goal name',
    example: 'Reduce Maternal Mortality Rate',
  })
  @IsString()
  @IsNotEmpty()
  name: string;
}

export class UpdateGoalDto {
  @ApiProperty({
    description: 'Goal name',
    example: 'Significantly Reduce Maternal Mortality Rate',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  name?: string;
}

export class GoalResponseDto {
  @ApiProperty({ description: 'Goal ID', example: 1 })
  id: number;

  @ApiProperty({ description: 'Goal name', example: 'Reduce Maternal Mortality Rate' })
  name: string;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;

  @ApiProperty({ description: 'Soft delete flag' })
  deleted: boolean;
}
