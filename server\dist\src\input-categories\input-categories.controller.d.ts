import { InputCategoriesService } from './input-categories.service';
import { CreateInputCategoryDto, UpdateInputCategoryDto } from './dto';
export declare class InputCategoriesController {
    private readonly inputCategoriesService;
    constructor(inputCategoriesService: InputCategoriesService);
    create(createInputCategoryDto: CreateInputCategoryDto, req: any): Promise<{
        parent: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        };
        children: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        categoryName: string;
        parentId: number | null;
    }>;
    findAll(): Promise<({
        parent: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        };
        children: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        categoryName: string;
        parentId: number | null;
    })[]>;
    findTree(): Promise<any[]>;
    findOne(id: number): Promise<{
        parent: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        };
        children: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        categoryName: string;
        parentId: number | null;
    }>;
    update(id: number, updateInputCategoryDto: UpdateInputCategoryDto, req: any): Promise<{
        parent: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        };
        children: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        categoryName: string;
        parentId: number | null;
    }>;
    remove(id: number, req: any): Promise<{
        success: boolean;
    }>;
}
