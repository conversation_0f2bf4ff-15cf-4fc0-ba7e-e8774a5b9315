import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsInt, IsNotEmpty, IsPositive, IsDateString } from 'class-validator';

export class CreateActivityDto {
  @ApiProperty({
    description: 'Activity name',
    example: 'Community Health Worker Training',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Activity description',
    example: 'Training program for community health workers in rural areas',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Project ID this activity belongs to',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  projectId: number;

  @ApiProperty({
    description: 'Activity start date',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsDateString()
  startDate: string;

  @ApiProperty({
    description: 'Activity end date',
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsDateString()
  endDate: string;

  @ApiProperty({
    description: 'Name of the implementer',
    example: 'Rwanda Health Partners Initiative',
  })
  @IsString()
  @IsNotEmpty()
  implementer: string;

  @ApiProperty({
    description: 'Implementing unit',
    example: 'Community Health Department',
  })
  @IsString()
  @IsNotEmpty()
  implementerUnit: string;

  @ApiProperty({
    description: 'Fiscal year',
    example: 2024,
  })
  @IsInt()
  @IsPositive()
  fiscalYear: number;

  @ApiProperty({
    description: 'Domain intervention ID',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  domainInterventionId: number;

  @ApiProperty({
    description: 'Input ID',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  inputId: number;
}

export class UpdateActivityDto {
  @ApiProperty({
    description: 'Activity name',
    example: 'Updated Community Health Worker Training',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Activity description',
    example: 'Updated training program description',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Activity start date',
    example: '2024-02-01T00:00:00.000Z',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiProperty({
    description: 'Activity end date',
    example: '2024-11-30T23:59:59.999Z',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @ApiProperty({
    description: 'Name of the implementer',
    example: 'Updated implementer name',
    required: false,
  })
  @IsString()
  @IsOptional()
  implementer?: string;

  @ApiProperty({
    description: 'Implementing unit',
    example: 'Updated implementing unit',
    required: false,
  })
  @IsString()
  @IsOptional()
  implementerUnit?: string;

  @ApiProperty({
    description: 'Fiscal year',
    example: 2025,
    required: false,
  })
  @IsInt()
  @IsPositive()
  @IsOptional()
  fiscalYear?: number;

  @ApiProperty({
    description: 'Domain intervention ID',
    example: 2,
    required: false,
  })
  @IsInt()
  @IsPositive()
  @IsOptional()
  domainInterventionId?: number;

  @ApiProperty({
    description: 'Input ID',
    example: 2,
    required: false,
  })
  @IsInt()
  @IsPositive()
  @IsOptional()
  inputId?: number;
}

export class ActivityResponseDto {
  @ApiProperty({ description: 'Activity ID', example: 1 })
  id: number;

  @ApiProperty({ description: 'Activity name', example: 'Community Health Worker Training' })
  name: string;

  @ApiProperty({ description: 'Activity description', example: 'Training program...', required: false })
  description?: string;

  @ApiProperty({ description: 'Project ID', example: 1 })
  projectId: number;

  @ApiProperty({ description: 'Start date' })
  startDate: Date;

  @ApiProperty({ description: 'End date' })
  endDate: Date;

  @ApiProperty({ description: 'Implementer', example: 'Rwanda Health Partners Initiative' })
  implementer: string;

  @ApiProperty({ description: 'Implementer unit', example: 'Community Health Department' })
  implementerUnit: string;

  @ApiProperty({ description: 'Fiscal year', example: 2024 })
  fiscalYear: number;

  @ApiProperty({ description: 'Domain intervention ID', example: 1 })
  domainInterventionId: number;

  @ApiProperty({ description: 'Input ID', example: 1 })
  inputId: number;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;

  @ApiProperty({ description: 'Soft delete flag' })
  deleted: boolean;
}

export class ActivityWithRelationsDto extends ActivityResponseDto {
  @ApiProperty({ description: 'Project information' })
  project?: {
    id: number;
    name: string;
    description?: string;
    mouApplication: {
      id: number;
      applicationKey: string;
    };
  };

  @ApiProperty({ description: 'Domain intervention information' })
  domainIntervention?: {
    id: number;
    domainName: string;
    description?: string;
    parent?: {
      id: number;
      domainName: string;
    };
  };

  @ApiProperty({ description: 'Input information' })
  input?: {
    id: number;
    name: string;
    inputSubclass: {
      id: number;
      name: string;
      budget: number;
    };
  };
}
