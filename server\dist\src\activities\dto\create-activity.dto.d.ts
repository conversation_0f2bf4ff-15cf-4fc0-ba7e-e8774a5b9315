export declare class CreateActivityDto {
    name: string;
    description?: string;
    projectId: number;
    startDate: string;
    endDate: string;
    implementer: string;
    implementerUnit: string;
    fiscalYear: number;
    domainInterventionId: number;
    inputId: number;
}
export declare class UpdateActivityDto {
    name?: string;
    description?: string;
    startDate?: string;
    endDate?: string;
    implementer?: string;
    implementerUnit?: string;
    fiscalYear?: number;
    domainInterventionId?: number;
    inputId?: number;
}
export declare class ActivityResponseDto {
    id: number;
    name: string;
    description?: string;
    projectId: number;
    startDate: Date;
    endDate: Date;
    implementer: string;
    implementerUnit: string;
    fiscalYear: number;
    domainInterventionId: number;
    inputId: number;
    createdAt: Date;
    updatedAt: Date;
    deleted: boolean;
}
export declare class ActivityWithRelationsDto extends ActivityResponseDto {
    project?: {
        id: number;
        name: string;
        description?: string;
        mouApplication: {
            id: number;
            applicationKey: string;
        };
    };
    domainIntervention?: {
        id: number;
        domainName: string;
        description?: string;
        parent?: {
            id: number;
            domainName: string;
        };
    };
    input?: {
        id: number;
        name: string;
        inputSubclass: {
            id: number;
            name: string;
            budget: number;
        };
    };
}
