"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MouApplicationWithRelationsDto = exports.MouApplicationResponseDto = exports.UpdateMouApplicationDto = exports.CreateMouApplicationDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateMouApplicationDto {
}
exports.CreateMouApplicationDto = CreateMouApplicationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique application key',
        example: 'MOU-APP-2024-001',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateMouApplicationDto.prototype, "applicationKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'MoU ID this application belongs to',
        example: 1,
    }),
    (0, class_validator_1.IsInt)(),
    __metadata("design:type", Number)
], CreateMouApplicationDto.prototype, "mouId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Responsibility description',
        example: 'Primary healthcare service delivery',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateMouApplicationDto.prototype, "responsibility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID of the applicant',
        example: 1,
        required: false,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], CreateMouApplicationDto.prototype, "userId", void 0);
class UpdateMouApplicationDto {
}
exports.UpdateMouApplicationDto = UpdateMouApplicationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Responsibility description',
        example: 'Updated healthcare service delivery',
        required: false,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateMouApplicationDto.prototype, "responsibility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID of the applicant',
        example: 1,
        required: false,
    }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], UpdateMouApplicationDto.prototype, "userId", void 0);
class MouApplicationResponseDto {
}
exports.MouApplicationResponseDto = MouApplicationResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Application ID', example: 1 }),
    __metadata("design:type", Number)
], MouApplicationResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Application key', example: 'MOU-APP-2024-001' }),
    __metadata("design:type", String)
], MouApplicationResponseDto.prototype, "applicationKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'MoU ID', example: 1 }),
    __metadata("design:type", Number)
], MouApplicationResponseDto.prototype, "mouId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Responsibility', example: 'Healthcare delivery', required: false }),
    __metadata("design:type", String)
], MouApplicationResponseDto.prototype, "responsibility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User ID', example: 1, required: false }),
    __metadata("design:type", Number)
], MouApplicationResponseDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation date' }),
    __metadata("design:type", Date)
], MouApplicationResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update date' }),
    __metadata("design:type", Date)
], MouApplicationResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Soft delete flag' }),
    __metadata("design:type", Boolean)
], MouApplicationResponseDto.prototype, "deleted", void 0);
class MouApplicationWithRelationsDto extends MouApplicationResponseDto {
}
exports.MouApplicationWithRelationsDto = MouApplicationWithRelationsDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Associated MoU information' }),
    __metadata("design:type", Object)
], MouApplicationWithRelationsDto.prototype, "mou", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Associated user information' }),
    __metadata("design:type", Object)
], MouApplicationWithRelationsDto.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Associated projects' }),
    __metadata("design:type", Array)
], MouApplicationWithRelationsDto.prototype, "projects", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Approval steps' }),
    __metadata("design:type", Array)
], MouApplicationWithRelationsDto.prototype, "approvalSteps", void 0);
//# sourceMappingURL=create-mou-application.dto.js.map