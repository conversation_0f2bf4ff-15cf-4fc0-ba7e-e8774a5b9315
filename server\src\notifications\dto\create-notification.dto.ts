import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateNotificationDto {
  @ApiProperty({ example: 'user-uuid' })
  @IsNotEmpty()
  @IsString()
  userId: string;

  @ApiProperty({ example: 'You have a new message' })
  @IsNotEmpty()
  @IsString()
  message: string;

  @ApiProperty({ example: false, required: false })
  @IsOptional()
  @IsBoolean()
  read?: boolean;
}
