import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class AdminDashboardService {
  constructor(private prisma: PrismaService) {}

  async getUserStatistics() {
    const [
      totalUsers,
      activeUsers,
      inactiveUsers,
      recentlyActive,
      roleDistribution,
      departmentDistribution
    ] = await Promise.all([
      // Total users count
      this.prisma.user.count(),

      // Active users count
      this.prisma.user.count({
        where: { isActive: true }
      }),

      // Inactive users count
      this.prisma.user.count({
        where: { isActive: false }
      }),

      // Recently active users (last 30 days) - using verifiedAt as proxy for activity
      this.prisma.user.count({
        where: {
          verifiedAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          }
        }
      }),

      // Distribution by role
      this.getRoleDistribution(),

      // Distribution by department
      this.getDepartmentDistribution()
    ]);

    return {
      totalUsers,
      activeUsers,
      inactiveUsers,
      recentlyActive,
      roleDistribution,
      departmentDistribution
    };
  }

  async getActivityMetrics(startDate?: Date, endDate?: Date) {
    const dateFilter = this.getDateFilter(startDate, endDate);

    const [
      loginActivity,
      userActions,
      reviewActivity,
      approvalActivity
    ] = await Promise.all([
      // Login activity
      this.getLoginActivity(dateFilter),

      // User actions
      this.getUserActions(dateFilter),

      // Review activity
      this.getReviewActivity(dateFilter),

      // Approval activity
      this.getApprovalActivity(dateFilter)
    ]);

    return {
      loginActivity,
      userActions,
      reviewActivity,
      approvalActivity
    };
  }

  async getSystemHealth() {
    const [
      activeApplications,
      pendingReviews,
      pendingApprovals,
      recentErrors
    ] = await Promise.all([
      // Active applications count (applications with pending approval steps)
      this.prisma.mouApplication.count({
        where: {
          approvalSteps: {
            some: {
              status: 'PENDING'
            }
          }
        }
      }),

      // Pending reviews count (applications with pending approval steps)
      this.prisma.mouApplication.count({
        where: {
          approvalSteps: {
            some: {
              status: 'PENDING'
            }
          }
        }
      }),

      // Pending approvals count (applications with approved steps)
      this.prisma.mouApplication.count({
        where: {
          approvalSteps: {
            some: {
              status: 'APPROVED'
            }
          }
        }
      }),

      // Recent system errors
      this.prisma.activityLog.findMany({
        where: {
          importance: 'ERROR',
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      })
    ]);

    return {
      activeApplications,
      pendingReviews,
      pendingApprovals,
      recentErrors,
      systemStatus: this.getSystemStatus()
    };
  }

  async getPerformanceMetrics() {
    return {
      averageReviewTime: await this.calculateAverageReviewTime(),
      averageApprovalTime: await this.calculateAverageApprovalTime(),
      applicationThroughput: await this.calculateApplicationThroughput(),
      userProductivity: await this.calculateUserProductivity()
    };
  }

  private async getRoleDistribution() {
    const roles = await this.prisma.user.groupBy({
      by: ['role'],
      _count: {
        role: true
      }
    });

    return {
      roles: roles
    };
  }

  private async getDepartmentDistribution() {
    // Since there's no department field, return organization distribution instead
    return this.prisma.user.groupBy({
      by: ['organizationId'],
      _count: {
        organizationId: true
      }
    });
  }

  private async getLoginActivity(dateFilter: any) {
    return this.prisma.activityLog.groupBy({
      by: ['createdAt'],
      where: {
        action: 'LOGIN',
        ...dateFilter
      },
      _count: {
        action: true
      }
    });
  }

  private async getUserActions(dateFilter: any) {
    return this.prisma.activityLog.groupBy({
      by: ['action'],
      where: dateFilter,
      _count: {
        action: true
      }
    });
  }

  private async getReviewActivity(dateFilter: any) {
    // Use approval steps as proxy for review activity
    return this.prisma.approvalStep.groupBy({
      by: ['status'],
      where: {
        createdAt: dateFilter.createdAt
      },
      _count: {
        status: true
      }
    });
  }

  private async getApprovalActivity(dateFilter: any) {
    // Use approval steps as proxy for approval activity
    return this.prisma.approvalStep.groupBy({
      by: ['status'],
      where: {
        createdAt: dateFilter.createdAt,
        status: 'APPROVED'
      },
      _count: {
        status: true
      }
    });
  }

  private async calculateAverageReviewTime() {
    // Calculate average time between submission and review completion using approval steps
    const approvalSteps = await this.prisma.approvalStep.findMany({
      include: {
        mouApplication: true
      }
    });

    let totalTime = 0;
    let count = 0;

    approvalSteps.forEach(step => {
      if (step.mouApplication.createdAt && step.createdAt) {
        const submissionDate = step.mouApplication.createdAt;
        const reviewDate = step.createdAt;
        totalTime += reviewDate.getTime() - submissionDate.getTime();
        count++;
      }
    });

    return count > 0 ? totalTime / count / (1000 * 60 * 60) : 0; // Return average in hours
  }

  private async calculateAverageApprovalTime() {
    // Similar to review time calculation but for approvals
    const approvals = await this.prisma.approvalStep.findMany({
      where: {
        status: 'APPROVED'
      },
      include: {
        mouApplication: true
      }
    });

    let totalTime = 0;
    let count = 0;

    approvals.forEach(approval => {
      if (approval.mouApplication.createdAt && approval.createdAt) {
        const submissionDate = approval.mouApplication.createdAt;
        const approvalDate = approval.createdAt;
        totalTime += approvalDate.getTime() - submissionDate.getTime();
        count++;
      }
    });

    return count > 0 ? totalTime / count / (1000 * 60 * 60) : 0;
  }

  private async calculateApplicationThroughput() {
    // Calculate applications processed per day over the last 30 days
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    // Since MouApplication doesn't have status, count by approval step status
    const applications = await this.prisma.mouApplication.findMany({
      where: {
        updatedAt: {
          gte: thirtyDaysAgo
        }
      },
      include: {
        approvalSteps: true
      }
    });

    // Group by approval status
    const statusCounts: Record<string, number> = {};
    applications.forEach(app => {
      const latestStep = app.approvalSteps[app.approvalSteps.length - 1];
      const status = latestStep?.status || 'PENDING';
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    return Object.entries(statusCounts).map(([status, count]) => ({
      status,
      _count: { status: count }
    }));
  }

  private async calculateUserProductivity() {
    // Calculate actions per user over the last 30 days
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    // Since ActivityLog doesn't have userId, return empty array
    return [];
  }

  private getDateFilter(startDate?: Date, endDate?: Date) {
    const filter: any = {};

    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.gte = startDate;
      if (endDate) filter.createdAt.lte = endDate;
    }

    return filter;
  }

  private getSystemStatus() {
    // You can implement actual system health checks here
    return {
      status: 'HEALTHY',
      lastCheck: new Date(),
      components: {
        database: 'OPERATIONAL',
        email: 'OPERATIONAL',
        storage: 'OPERATIONAL',
        notifications: 'OPERATIONAL'
      }
    };
  }
}
