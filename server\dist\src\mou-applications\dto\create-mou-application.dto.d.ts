export declare class CreateMouApplicationDto {
    applicationKey: string;
    mouId: number;
    responsibility?: string;
    userId?: number;
}
export declare class UpdateMouApplicationDto {
    responsibility?: string;
    userId?: number;
}
export declare class MouApplicationResponseDto {
    id: number;
    applicationKey: string;
    mouId: number;
    responsibility?: string;
    userId?: number;
    createdAt: Date;
    updatedAt: Date;
    deleted: boolean;
}
export declare class MouApplicationWithRelationsDto extends MouApplicationResponseDto {
    mou?: {
        id: number;
        mouKey: string;
        party: {
            id: number;
            name: string;
            signatory: string;
            position: string;
        };
    };
    user?: {
        id: number;
        firstName: string;
        lastName: string;
        email: string;
        organization?: {
            id: number;
            organizationName: string;
        };
    };
    projects?: Array<{
        id: number;
        name: string;
        description?: string;
        duration: number;
    }>;
    approvalSteps?: Array<{
        id: number;
        status: string;
        comment?: string;
        role: string;
        reviewer: {
            id: number;
            firstName: string;
            lastName: string;
        };
    }>;
}
