import { Injectable, NotFoundException, ConflictException, ForbiddenException, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateDomainInterventionDto, UpdateDomainInterventionDto } from './dto';

@Injectable()
export class DomainInterventionsService {
    private readonly logger = new Logger(DomainInterventionsService.name);

    constructor(private prisma: PrismaService) {}

    async create(createDomainInterventionDto: CreateDomainInterventionDto, userId: string) {
        try {
            // Check if user has admin role
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            if (user.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can create domain interventions');
            }

            // If parentId is provided, verify parent exists
            if (createDomainInterventionDto.parentId) {
                const parent = await this.prisma.domainIntervention.findFirst({
                    where: {
                        id: createDomainInterventionDto.parentId,
                        deleted: false
                    }
                });

                if (!parent) {
                    throw new BadRequestException('Parent domain intervention not found');
                }
            }

            // Check if domain intervention with same name exists at the same level
            const existing = await this.prisma.domainIntervention.findFirst({
                where: {
                    domainName: createDomainInterventionDto.domainName,
                    parentId: createDomainInterventionDto.parentId,
                    deleted: false
                }
            });

            if (existing) {
                throw new ConflictException('Domain intervention with this name already exists at this level');
            }

            // Create the domain intervention
            const domain = await this.prisma.domainIntervention.create({
                data: {
                    domainName: createDomainInterventionDto.domainName,
                    description: createDomainInterventionDto.description,
                    parentId: createDomainInterventionDto.parentId
                },
                include: {
                    parent: true,
                    children: true
                }
            });

            return domain;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException || error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to create domain intervention', error.stack);
            throw new Error('Failed to create domain intervention');
        }
    }

    async findAll() {
        return this.prisma.domainIntervention.findMany({
            where: {
                deleted: false
            },
            include: {
                parent: true,
                children: true
            },
            orderBy: {
                domainName: 'asc'
            }
        });
    }

    async findTree() {
        // First get all domains
        const domains = await this.prisma.domainIntervention.findMany({
            where: {
                deleted: false
            },
            orderBy: {
                domainName: 'asc'
            }
        });

        // Build tree structure
        const buildTree = (parentId: number | null = null): any[] => {
            return domains
                .filter(d => d.parentId === parentId)
                .map(d => ({
                    ...d,
                    children: buildTree(d.id)
                }));
        };

        return buildTree(null);
    }

    async findOne(id: number) {
        const domain = await this.prisma.domainIntervention.findFirst({
            where: {
                id,
                deleted: false
            },
            include: {
                parent: true,
                children: true
            }
        });

        if (!domain) {
            throw new NotFoundException('Domain intervention not found');
        }

        return domain;
    }

    async update(id: number, updateDomainInterventionDto: UpdateDomainInterventionDto, userId: string) {
        try {
            // Check if user has admin role
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            if (user.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can update domain interventions');
            }

            // Check if domain intervention exists
            const existing = await this.prisma.domainIntervention.findFirst({
                where: {
                    id,
                    deleted: false
                },
                include: {
                    children: true
                }
            });

            if (!existing) {
                throw new NotFoundException('Domain intervention not found');
            }

            // If parentId is being changed
            if (updateDomainInterventionDto.parentId !== undefined) {
                // Cannot set parent to self
                if (updateDomainInterventionDto.parentId === id) {
                    throw new BadRequestException('Cannot set domain intervention as its own parent');
                }

                // Cannot set parent to one of its descendants
                const isDescendant = async (childId: number, potentialParentId: number): Promise<boolean> => {
                    const child = await this.prisma.domainIntervention.findUnique({
                        where: { id: childId },
                        include: { children: true }
                    });
                    
                    if (!child) return false;
                    if (child.id === potentialParentId) return true;
                    
                    for (const descendant of child.children) {
                        if (await isDescendant(descendant.id, potentialParentId)) {
                            return true;
                        }
                    }
                    return false;
                };

                if (updateDomainInterventionDto.parentId && 
                    await isDescendant(updateDomainInterventionDto.parentId, id)) {
                    throw new BadRequestException('Cannot set a descendant as parent');
                }

                // Verify new parent exists if specified
                if (updateDomainInterventionDto.parentId) {
                    const parent = await this.prisma.domainIntervention.findFirst({
                        where: {
                            id: updateDomainInterventionDto.parentId,
                            deleted: false
                        }
                    });

                    if (!parent) {
                        throw new BadRequestException('Parent domain intervention not found');
                    }
                }
            }

            // Check for name conflicts at the same level
            if (updateDomainInterventionDto.domainName && 
                updateDomainInterventionDto.domainName !== existing.domainName) {
                const nameConflict = await this.prisma.domainIntervention.findFirst({
                    where: {
                        domainName: updateDomainInterventionDto.domainName,
                        parentId: updateDomainInterventionDto.parentId ?? existing.parentId,
                        deleted: false,
                        id: { not: id }
                    }
                });

                if (nameConflict) {
                    throw new ConflictException('Another domain intervention with this name exists at this level');
                }
            }

            // Update the domain intervention
            const updated = await this.prisma.domainIntervention.update({
                where: { id },
                data: {
                    domainName: updateDomainInterventionDto.domainName,
                    description: updateDomainInterventionDto.description,
                    parentId: updateDomainInterventionDto.parentId
                },
                include: {
                    parent: true,
                    children: true
                }
            });

            return updated;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || 
                error instanceof ForbiddenException || error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to update domain intervention', error.stack);
            throw new Error('Failed to update domain intervention');
        }
    }

    async remove(id: number, userId: string) {
        try {
            // Check if user has admin role
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            if (user.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can delete domain interventions');
            }

            // Check if domain intervention exists
            const existing = await this.prisma.domainIntervention.findFirst({
                where: {
                    id,
                    deleted: false
                },
                include: {
                    children: {
                        where: {
                            deleted: false
                        }
                    },
                    activities: {
                        where: {
                            deleted: false
                        }
                    }
                }
            });

            if (!existing) {
                throw new NotFoundException('Domain intervention not found');
            }

            // Check if domain has children
            if (existing.children.length > 0) {
                throw new ConflictException('Cannot delete domain intervention that has child domains');
            }

            // Check if domain is being used in activities
            if (existing.activities.length > 0) {
                throw new ConflictException('Cannot delete domain intervention that is being used in activities');
            }

            // Soft delete the domain intervention
            await this.prisma.domainIntervention.update({
                where: { id },
                data: { deleted: true }
            });

            return { success: true };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || 
                error instanceof ForbiddenException || error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to delete domain intervention', error.stack);
            throw new Error('Failed to delete domain intervention');
        }
    }
}
