import { Modu<PERSON> } from '@nestjs/common';
import { NotificationService } from './notification.service';
import { NotificationController } from './notification.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { EmailModule } from '../email/email.module';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    PrismaModule,
    EmailModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: { 
          expiresIn: configService.get('JWT_EXPIRATION', '1d')
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [NotificationController],
  providers: [
    NotificationService,
    {
      provide: 'NOTIFICATION_CONFIG',
      useFactory: (configService: ConfigService) => ({
        webSocketEnabled: configService.get('WEBSOCKET_ENABLED', true),
        emailEnabled: configService.get('EMAIL_NOTIFICATIONS_ENABLED', true),
        batchSize: configService.get('NOTIFICATION_BATCH_SIZE', 100),
        retryAttempts: configService.get('NOTIFICATION_RETRY_ATTEMPTS', 3),
        retryDelay: configService.get('NOTIFICATION_RETRY_DELAY', 1000),
      }),
      inject: [ConfigService],
    }
  ],
  exports: [NotificationService],
})
export class NotificationModule {}
