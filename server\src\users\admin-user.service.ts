import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { User, ActivityLog } from '@prisma/client';
import * as bcrypt from 'bcrypt';
import {
  CreateUserDto,
  UpdateUserDto,
  AssignRolesDto,
  FilterUsersDto,
  UserActivityDto,
  BulkUserActionDto
} from './dto/admin-user.dto';

@Injectable()
export class AdminUserService {
  constructor(private prisma: PrismaService) {}

  async createUser(dto: CreateUserDto): Promise<Omit<User, 'password'>> {
    // Check if email exists
    const existingUser = await this.prisma.user.findUnique({
      where: { email: dto.email },
    });

    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(dto.password, 10);

    // Create user
    const user = await this.prisma.user.create({
      data: {
        firstName: dto.firstName,
        lastName: dto.lastName,
        email: dto.email,
        password: hashedPassword,
        role: dto.role,
        organizationId: dto.organizationId,
        isActive: dto.isActive ?? true,
      },
    });

    // Remove password from response
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  async findAllUsers(filters: FilterUsersDto): Promise<{
    data: Array<Omit<User, 'password'>>;
    meta: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  }> {
    const where: any = {};

    // Apply role filters
    if (filters.roles?.length) {
      where.role = { in: filters.roles };
    }

    // Apply search filter
    if (filters.search) {
      where.OR = [
        { firstName: { contains: filters.search, mode: 'insensitive' } },
        { lastName: { contains: filters.search, mode: 'insensitive' } },
        { email: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    // Apply other filters
    if (filters.department) {
      where.department = filters.department;
    }
    if (filters.organizationId) {
      where.organizationId = filters.organizationId;
    }
    if (typeof filters.isActive === 'boolean') {
      where.isActive = filters.isActive;
    }

    // Pagination
    const page = filters.page || 0;
    const limit = filters.limit || 10;
    const skip = page * limit;

    // Sorting
    const orderBy: any = {};
    if (filters.sortBy) {
      orderBy[filters.sortBy] = filters.sortOrder || 'desc';
    } else {
      orderBy.createdAt = 'desc';
    }

    // Get total count and users
    const [total, users] = await Promise.all([
      this.prisma.user.count({ where }),
      this.prisma.user.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          role: true,
          organizationId: true,
          isActive: true,
          emailVerified: true,
          verifiedAt: true,
          verificationToken: true,
          verificationTokenExpiryTime: true,
          refreshToken: true,
          passwordResetToken: true,
          passwordResetExpires: true,
          invitationToken: true,
          invitationExpires: true,
          invitedBy: true,
          deleted: true,
          createdAt: true,
          updatedAt: true,
          organization: {
            select: {
              organizationName: true,
            },
          },
        },
      }),
    ]);

    return {
      data: users,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number): Promise<Omit<User, 'password'>> {
    const user = await this.prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        role: true,
        organizationId: true,
        isActive: true,
        emailVerified: true,
        verifiedAt: true,
        verificationToken: true,
        verificationTokenExpiryTime: true,
        refreshToken: true,
        passwordResetToken: true,
        passwordResetExpires: true,
        invitationToken: true,
        invitationExpires: true,
        invitedBy: true,
        deleted: true,
        createdAt: true,
        updatedAt: true,
        organization: {
          select: {
            organizationName: true,
          },
        },
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async updateUser(id: number, dto: UpdateUserDto): Promise<Omit<User, 'password'>> {
    // Check if user exists
    const user = await this.prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check email uniqueness if email is being updated
    if (dto.email && dto.email !== user.email) {
      const existingUser = await this.prisma.user.findUnique({
        where: { email: dto.email },
      });

      if (existingUser) {
        throw new ConflictException('Email already exists');
      }
    }

    // Update user
    const updatedUser = await this.prisma.user.update({
      where: { id },
      data: dto,
    });

    // Remove password from response
    const { password, ...userWithoutPassword } = updatedUser;
    return userWithoutPassword;
  }

  async assignRoles(id: number, dto: AssignRolesDto): Promise<Omit<User, 'password'>> {
    const user = await this.prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const updatedUser = await this.prisma.user.update({
      where: { id },
      data: {
        role: dto.role || user.role,
      },
    });

    const { password, ...userWithoutPassword } = updatedUser;
    return userWithoutPassword;
  }

  async getUserActivity(dto: UserActivityDto): Promise<{
    data: Array<ActivityLog & {
      user: Pick<User, 'firstName' | 'lastName' | 'email'>;
    }>;
    meta: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  }> {
    const where: any = {
      userId: dto.userId,
    };

    if (dto.startDate || dto.endDate) {
      where.createdAt = {};
      if (dto.startDate) where.createdAt.gte = new Date(dto.startDate);
      if (dto.endDate) where.createdAt.lte = new Date(dto.endDate);
    }

    if (dto.activityType) {
      where.type = dto.activityType;
    }

    const page = dto.page || 0;
    const limit = dto.limit || 10;
    const skip = page * limit;

    const [total, activities] = await Promise.all([
      this.prisma.activityLog.count({ where }),
      this.prisma.activityLog.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        // Note: ActivityLog model doesn't have user relation, so we'll skip this include
      }),
    ]);

    return {
      data: [], // Return empty array since ActivityLog doesn't have user relation
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async bulkUpdateUsers(dto: BulkUserActionDto): Promise<Array<Omit<User, 'password'>>> {
    // Validate user IDs
    const users = await this.prisma.user.findMany({
      where: {
        id: { in: dto.userIds },
      },
    });

    if (users.length !== dto.userIds.length) {
      throw new BadRequestException('Some user IDs are invalid');
    }

    // Prepare update data
    const updateData: any = {};
    if (typeof dto.isActive === 'boolean') updateData.isActive = dto.isActive;
    if (dto.role) updateData.role = dto.role;

    // Perform bulk update
    await this.prisma.user.updateMany({
      where: {
        id: { in: dto.userIds },
      },
      data: updateData,
    });

    // Return updated users
    const updatedUsers = await this.prisma.user.findMany({
      where: {
        id: { in: dto.userIds },
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        role: true,
        isActive: true,
        organizationId: true,
        createdAt: true,
        updatedAt: true,
        deleted: true,
        emailVerified: true,
        verifiedAt: true,
        verificationToken: true,
        verificationTokenExpiryTime: true,
        refreshToken: true,
        passwordResetToken: true,
        passwordResetExpires: true,
        invitationToken: true,
        invitationExpires: true,
        invitedBy: true,
      },
    });

    // Remove password field (it's already excluded by select)
    return updatedUsers;
  }

  async resetUserPassword(id: number, newPassword: string): Promise<{ message: string }> {
    const user = await this.prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10);

    await this.prisma.user.update({
      where: { id },
      data: {
        password: hashedPassword,
        // Note: passwordChangedAt field doesn't exist in User model
      },
    });

    return { message: 'Password reset successfully' };
  }

  async deactivateUser(id: number): Promise<Omit<User, 'password'>> {
    const user = await this.prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const updatedUser = await this.prisma.user.update({
      where: { id },
      data: {
        isActive: false,
      },
    });

    const { password, ...userWithoutPassword } = updatedUser;
    return userWithoutPassword;
  }

  async reactivateUser(id: number): Promise<Omit<User, 'password'>> {
    const user = await this.prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const updatedUser = await this.prisma.user.update({
      where: { id },
      data: {
        isActive: true,
      },
    });

    const { password, ...userWithoutPassword } = updatedUser;
    return userWithoutPassword;
  }
}