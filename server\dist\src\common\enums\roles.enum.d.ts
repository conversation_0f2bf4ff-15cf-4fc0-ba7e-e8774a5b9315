export declare enum BaseRole {
    ADMIN = "ADMIN",
    PARTNER = "PARTNER",
    STAFF = "STAFF"
}
export declare enum ReviewerRole {
    PARTNER_COORDINATOR = "PARTNER_COORDINATOR",
    TECHNICAL_EXPERT = "TECHNICAL_EXPERT",
    LEGAL_OFFICER = "LEGAL_OFFICER",
    AUDITOR = "AUDITOR"
}
export declare enum ApproverRole {
    HEAD_OF_DEPARTMENT = "HEAD_OF_DEPARTMENT",
    LEGAL_OFFICER = "LEGAL_OFFICER",
    PERMANENT_SECRETARY = "PERMANENT_SECRETARY",
    MINISTER = "MINISTER",
    ROLE_MANAGER = "ROLE_MANAGER",
    PERMISSION_MANAGER = "PERMISSION_MANAGER"
}
export declare enum PermissionLevel {
    NONE = "NONE",
    READ = "READ",
    WRITE = "WRITE",
    ADMIN = "ADMIN"
}
export declare enum ResourceType {
    USER = "USER",
    ORGANIZATION = "ORGANIZATION",
    APPLICATION = "APPLICATION",
    DOCUMENT = "DOCUMENT",
    REVIEW = "REVIEW",
    APPROVAL = "APPROVAL",
    REPORT = "REPORT",
    AUDIT_LOG = "AUDIT_LOG"
}
export interface RolePermission {
    resource: ResourceType;
    level: PermissionLevel;
}
export declare const DEFAULT_ROLE_PERMISSIONS: Record<BaseRole | ReviewerRole | ApproverRole, RolePermission[]>;
export declare function hasPermission(userRoles: (BaseRole | ReviewerRole | ApproverRole)[], resource: ResourceType, requiredLevel: PermissionLevel): boolean;
