"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MouApplicationsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let MouApplicationsService = class MouApplicationsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createMouApplicationDto) {
        try {
            const mou = await this.prisma.mou.findUnique({
                where: { id: createMouApplicationDto.mouId },
            });
            if (!mou) {
                throw new common_1.NotFoundException(`MoU with ID ${createMouApplicationDto.mouId} not found`);
            }
            const existingApplication = await this.prisma.mouApplication.findUnique({
                where: { applicationKey: createMouApplicationDto.applicationKey },
            });
            if (existingApplication) {
                throw new common_1.ConflictException(`Application with key ${createMouApplicationDto.applicationKey} already exists`);
            }
            if (createMouApplicationDto.userId) {
                const user = await this.prisma.user.findUnique({
                    where: { id: createMouApplicationDto.userId },
                });
                if (!user) {
                    throw new common_1.NotFoundException(`User with ID ${createMouApplicationDto.userId} not found`);
                }
            }
            const mouApplication = await this.prisma.mouApplication.create({
                data: createMouApplicationDto,
                include: {
                    mou: {
                        include: {
                            party: {
                                include: {
                                    organization: true,
                                },
                            },
                        },
                    },
                    user: {
                        include: {
                            organization: true,
                        },
                    },
                    projects: true,
                    approvalSteps: {
                        include: {
                            reviewer: true,
                        },
                    },
                },
            });
            return mouApplication;
        }
        catch (error) {
            if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
                if (error.code === 'P2002') {
                    throw new common_1.ConflictException('Application key must be unique');
                }
            }
            throw error;
        }
    }
    async findAll(page = 1, limit = 10, userId) {
        const skip = (page - 1) * limit;
        const where = {
            deleted: false,
            ...(userId && { userId }),
        };
        const [applications, total] = await Promise.all([
            this.prisma.mouApplication.findMany({
                where,
                skip,
                take: limit,
                include: {
                    mou: {
                        include: {
                            party: {
                                include: {
                                    organization: true,
                                },
                            },
                        },
                    },
                    user: {
                        include: {
                            organization: true,
                        },
                    },
                    projects: true,
                    approvalSteps: {
                        include: {
                            reviewer: true,
                        },
                    },
                },
                orderBy: {
                    createdAt: 'desc',
                },
            }),
            this.prisma.mouApplication.count({ where }),
        ]);
        return {
            data: applications,
            meta: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id) {
        const mouApplication = await this.prisma.mouApplication.findFirst({
            where: {
                id,
                deleted: false,
            },
            include: {
                mou: {
                    include: {
                        party: {
                            include: {
                                organization: true,
                            },
                        },
                    },
                },
                user: {
                    include: {
                        organization: true,
                    },
                },
                projects: {
                    include: {
                        activities: true,
                        budgetType: true,
                        fundingSource: true,
                        fundingUnit: true,
                    },
                },
                approvalSteps: {
                    include: {
                        reviewer: true,
                        project: true,
                    },
                },
            },
        });
        if (!mouApplication) {
            throw new common_1.NotFoundException(`MoU Application with ID ${id} not found`);
        }
        return mouApplication;
    }
    async findByApplicationKey(applicationKey) {
        const mouApplication = await this.prisma.mouApplication.findFirst({
            where: {
                applicationKey,
                deleted: false,
            },
            include: {
                mou: {
                    include: {
                        party: {
                            include: {
                                organization: true,
                            },
                        },
                    },
                },
                user: {
                    include: {
                        organization: true,
                    },
                },
                projects: {
                    include: {
                        activities: true,
                    },
                },
                approvalSteps: {
                    include: {
                        reviewer: true,
                    },
                },
            },
        });
        if (!mouApplication) {
            throw new common_1.NotFoundException(`MoU Application with key ${applicationKey} not found`);
        }
        return mouApplication;
    }
    async update(id, updateMouApplicationDto) {
        const existingApplication = await this.prisma.mouApplication.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingApplication) {
            throw new common_1.NotFoundException(`MoU Application with ID ${id} not found`);
        }
        if (updateMouApplicationDto.userId) {
            const user = await this.prisma.user.findUnique({
                where: { id: updateMouApplicationDto.userId },
            });
            if (!user) {
                throw new common_1.NotFoundException(`User with ID ${updateMouApplicationDto.userId} not found`);
            }
        }
        const updatedApplication = await this.prisma.mouApplication.update({
            where: { id },
            data: updateMouApplicationDto,
            include: {
                mou: {
                    include: {
                        party: {
                            include: {
                                organization: true,
                            },
                        },
                    },
                },
                user: {
                    include: {
                        organization: true,
                    },
                },
                projects: true,
                approvalSteps: {
                    include: {
                        reviewer: true,
                    },
                },
            },
        });
        return updatedApplication;
    }
    async remove(id) {
        const existingApplication = await this.prisma.mouApplication.findFirst({
            where: {
                id,
                deleted: false,
            },
        });
        if (!existingApplication) {
            throw new common_1.NotFoundException(`MoU Application with ID ${id} not found`);
        }
        await this.prisma.mouApplication.update({
            where: { id },
            data: { deleted: true },
        });
        return { message: 'MoU Application deleted successfully' };
    }
    async getApplicationStats(userId) {
        const where = {
            deleted: false,
            ...(userId && { userId }),
        };
        const [total, pending, approved, rejected] = await Promise.all([
            this.prisma.mouApplication.count({ where }),
            this.prisma.mouApplication.count({
                where: {
                    ...where,
                    approvalSteps: {
                        some: {
                            status: 'PENDING',
                        },
                    },
                },
            }),
            this.prisma.mouApplication.count({
                where: {
                    ...where,
                    approvalSteps: {
                        every: {
                            status: 'APPROVED',
                        },
                    },
                },
            }),
            this.prisma.mouApplication.count({
                where: {
                    ...where,
                    approvalSteps: {
                        some: {
                            status: 'REJECTED',
                        },
                    },
                },
            }),
        ]);
        return {
            total,
            pending,
            approved,
            rejected,
        };
    }
};
exports.MouApplicationsService = MouApplicationsService;
exports.MouApplicationsService = MouApplicationsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], MouApplicationsService);
//# sourceMappingURL=mou-applications.service.js.map