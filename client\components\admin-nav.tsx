"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import {
  BarChart3,
  FileText,
  CheckCircle,
  Clock,
  Settings,
  Building2,
  DollarSign,
  Users,
  UserCircle,
  ChevronRight,
  Shield,
  Database,
  Activity,
  TrendingUp,
  Globe,
  Zap,
  HeartPulse,
  Briefcase,
  Landmark,
  CreditCard,
  Banknote,
  Coins,
  FileInputIcon,
} from "lucide-react"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"

interface AdminNavItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  subtitle: string
  color: string
  badge?: string
  children?: AdminNavItem[]
}

const adminNavItems: AdminNavItem[] = [
  {
    title: "Admin Dashboard",
    href: "/dashboard/admin",
    icon: BarChart3,
    subtitle: "Overview, Analytics & Reports",
    color: "blue",
  },
  {
    title: "MoU Management",
    href: "/dashboard/mou",
    icon: FileText,
    subtitle: "Manage agreements & applications",
    color: "green",
    children: [
      {
        title: "Active MoUs",
        href: "/dashboard/mou/active",
        icon: CheckCircle,
        subtitle: "View active agreements",
        color: "green",
        badge: "45"
      },
      {
        title: "Expired MoUs",
        href: "/dashboard/mou/expired",
        icon: Clock,
        subtitle: "View expired agreements",
        color: "gray",
      },
      {
        title: "Pending Applications",
        href: "/dashboard/applications/pending",
        icon: Clock,
        subtitle: "Review pending requests",
        color: "amber",
        badge: "8"
      },
      {
        title: "In Review",
        href: "/dashboard/applications/review",
        icon: Activity,
        subtitle: "Applications under review",
        color: "blue",
        badge: "4"
      },
      {
        title: "Draft Applications",
        href: "/dashboard/applications/draft",
        icon: FileInputIcon,
        subtitle: "Incomplete applications",
        color: "gray",
      },
      {
        title: "Rejected Applications",
        href: "/dashboard/applications/rejected",
        icon: Shield,
        subtitle: "Declined requests",
        color: "red",
      },
    ]
  },
  {
    title: "System Configuration",
    href: "/dashboard/config",
    icon: Settings,
    subtitle: "Setup & administration",
    color: "purple",
    children: [
      {
        title: "Domain Interventions",
        href: "/dashboard/domain-interventions",
        icon: Globe,
        subtitle: "Manage intervention domains",
        color: "purple",
      },
      {
        title: "Organization Types",
        href: "/dashboard/organization-types",
        icon: Building2,
        subtitle: "Configure organization categories",
        color: "purple",
      },
      {
        title: "Healthcare Providers",
        href: "/dashboard/healthcare-providers",
        icon: HeartPulse,
        subtitle: "Manage healthcare entities",
        color: "purple",
      },
      {
        title: "Input Categories",
        href: "/dashboard/input-categories",
        icon: Database,
        subtitle: "Configure input types",
        color: "purple",
      },
    ]
  },
  {
    title: "Financial Setup",
    href: "/dashboard/financial",
    icon: DollarSign,
    subtitle: "Budget & funding configuration",
    color: "emerald",
    children: [
      {
        title: "Budget Types",
        href: "/dashboard/budget-types",
        icon: Briefcase,
        subtitle: "Configure budget categories",
        color: "emerald",
      },
      {
        title: "Funding Sources",
        href: "/dashboard/funding-source",
        icon: Landmark,
        subtitle: "Manage funding origins",
        color: "emerald",
      },
      {
        title: "Funding Units",
        href: "/dashboard/funding-units",
        icon: TrendingUp,
        subtitle: "Configure funding units",
        color: "emerald",
      },
      {
        title: "Financing Schemes",
        href: "/dashboard/financing-schemes",
        icon: CreditCard,
        subtitle: "Manage financing options",
        color: "emerald",
      },
      {
        title: "Financing Agents",
        href: "/dashboard/financing-agents",
        icon: Banknote,
        subtitle: "Configure financing entities",
        color: "emerald",
      },
      {
        title: "Currencies",
        href: "/dashboard/currency",
        icon: Coins,
        subtitle: "Manage currency options",
        color: "emerald",
      },
    ]
  },
  {
    title: "User Management",
    href: "/dashboard/users",
    icon: Users,
    subtitle: "User accounts & permissions",
    color: "indigo",
  },
  {
    title: "My Profile",
    href: "/dashboard/profile",
    icon: UserCircle,
    subtitle: "Personal settings & info",
    color: "gray",
  },
]

const getColorClasses = (color: string, isActive: boolean) => {
  const colorMap = {
    blue: {
      bg: "bg-blue-100",
      icon: "text-blue-600",
      activeBg: "bg-blue-600",
      activeIcon: "text-white"
    },
    green: {
      bg: "bg-green-100",
      icon: "text-green-600",
      activeBg: "bg-green-600",
      activeIcon: "text-white"
    },
    amber: {
      bg: "bg-amber-100",
      icon: "text-amber-600",
      activeBg: "bg-amber-600",
      activeIcon: "text-white"
    },
    purple: {
      bg: "bg-purple-100",
      icon: "text-purple-600",
      activeBg: "bg-purple-600",
      activeIcon: "text-white"
    },
    emerald: {
      bg: "bg-emerald-100",
      icon: "text-emerald-600",
      activeBg: "bg-emerald-600",
      activeIcon: "text-white"
    },
    indigo: {
      bg: "bg-indigo-100",
      icon: "text-indigo-600",
      activeBg: "bg-indigo-600",
      activeIcon: "text-white"
    },
    gray: {
      bg: "bg-gray-100",
      icon: "text-gray-600",
      activeBg: "bg-gray-600",
      activeIcon: "text-white"
    },
    red: {
      bg: "bg-red-100",
      icon: "text-red-600",
      activeBg: "bg-red-600",
      activeIcon: "text-white"
    }
  }

  return colorMap[color as keyof typeof colorMap] || colorMap.gray
}

export function AdminNav() {
  const pathname = usePathname()
  const [openSections, setOpenSections] = React.useState<string[]>(['mou-management'])

  const isActive = (href: string) => {
    return pathname === href || pathname.startsWith(`${href}/`)
  }

  const toggleSection = (section: string) => {
    setOpenSections(prev => 
      prev.includes(section) 
        ? prev.filter(s => s !== section)
        : [...prev, section]
    )
  }

  const renderNavItem = (item: AdminNavItem, isChild = false) => {
    const Icon = item.icon
    const active = isActive(item.href)
    const colorClasses = getColorClasses(item.color, active)

    if (item.children && !isChild) {
      const sectionKey = item.title.toLowerCase().replace(/\s+/g, '-')
      const isOpen = openSections.includes(sectionKey)

      return (
        <Collapsible
          key={item.href}
          open={isOpen}
          onOpenChange={() => toggleSection(sectionKey)}
          className="w-full"
        >
          <CollapsibleTrigger className="flex w-full items-center justify-between rounded-xl px-4 py-3 text-sm font-medium text-gray-700 hover:bg-white hover:shadow-md transition-all duration-200 bg-white/50">
            <div className="flex items-center gap-3">
              <div className={cn("flex h-12 w-12 items-center justify-center rounded-xl", colorClasses.bg)}>
                <Icon className={cn("h-6 w-6", colorClasses.icon)} />
              </div>
              <div className="flex flex-col items-start">
                <span className="font-bold text-base">{item.title}</span>
                <span className="text-sm text-gray-500 font-medium">{item.subtitle}</span>
              </div>
              {item.badge && (
                <Badge variant="secondary" className={cn("text-xs px-2 py-1 font-semibold ml-auto mr-2", `bg-${item.color}-100 text-${item.color}-700`)}>
                  {item.badge}
                </Badge>
              )}
            </div>
            <ChevronRight className={cn(
              "h-4 w-4 transition-transform text-gray-400",
              isOpen && "rotate-90"
            )} />
          </CollapsibleTrigger>
          <CollapsibleContent>
            <div className="mt-2 space-y-1 pl-16">
              {item.children.map(child => renderNavItem(child, true))}
            </div>
          </CollapsibleContent>
        </Collapsible>
      )
    }

    return (
      <Link
        key={item.href}
        href={item.href}
        className={cn(
          "group flex items-center gap-4 rounded-xl transition-all duration-200 hover:shadow-lg",
          isChild ? "px-3 py-2" : "px-4 py-4",
          active
            ? "bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg"
            : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-100 hover:border-gray-200"
        )}
      >
        <div className={cn(
          "flex items-center justify-center rounded-xl transition-colors",
          isChild ? "h-10 w-10" : "h-12 w-12",
          active ? "bg-white/20" : colorClasses.bg
        )}>
          <Icon className={cn(
            "transition-colors",
            isChild ? "h-5 w-5" : "h-6 w-6",
            active ? "text-white" : colorClasses.icon
          )} />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className={cn(
              "font-bold truncate",
              isChild ? "text-sm" : "text-base",
              active ? "text-white" : "text-gray-900"
            )}>
              {item.title}
            </span>
            {item.badge && (
              <Badge 
                variant="secondary" 
                className={cn(
                  "text-xs px-2 py-1 font-semibold",
                  active 
                    ? "bg-white/20 text-white" 
                    : `bg-${item.color}-100 text-${item.color}-700`
                )}
              >
                {item.badge}
              </Badge>
            )}
          </div>
          <p className={cn(
            "truncate font-medium",
            isChild ? "text-xs" : "text-sm",
            active ? "text-blue-100" : "text-gray-500"
          )}>
            {item.subtitle}
          </p>
        </div>
      </Link>
    )
  }

  return (
    <nav className="flex flex-col gap-4">
      {adminNavItems.map(item => renderNavItem(item))}
    </nav>
  )
}
