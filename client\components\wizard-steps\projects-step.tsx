import { useCallback } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useFieldArray, useForm } from 'react-hook-form'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useMouApplicationStore } from '@/store/mou-application-store'
import {
  mockFundingSources,
  mockFundingUnits,
  mockBudgetTypes,
  mockCurrencies,
  mockFiscalYears,
} from '@/data/mock-data'
import { CalendarIcon, Plus, Trash2 } from 'lucide-react'
import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { cn } from '@/lib/utils'
import { format } from 'date-fns'
import { useToast } from '@/hooks/use-toast'
import { useFieldBlurAutoSave } from '@/hooks/use-mou-form'
import { projectSchema } from '@/lib/validations/mou'

interface FiscalYearBudget {
  year: string
  budget: number
}

interface Goal {
  id: string
  description: string
  isOverallGoal: boolean
}

interface Project {
  id?: string
  name: string
  fundingSourceId: number
  fundingUnitId: number
  budgetTypeId: number
  currencyId: number
  startDate: Date
  endDate: Date
  fiscalYears: FiscalYearBudget[]
  goals: Goal[]
  totalBudget?: number
}

const defaultProject: Omit<Project, 'id' | 'totalBudget'> = {
  name: "",
  fundingSourceId: 0,
  fundingUnitId: 0,
  budgetTypeId: 0,
  currencyId: 0,
  startDate: new Date(),
  endDate: new Date(),
  fiscalYears: [],
  goals: [{ id: crypto.randomUUID(), description: "", isOverallGoal: true }],
}

type FormData = {
  projects: Project[]
}

const formSchema = z.object({
  projects: z.array(projectSchema).min(1, 'At least one project is required')
})

const useCalculateTotalBudget = (fiscalYears: FiscalYearBudget[]) => {
  return fiscalYears.reduce((total, fy) => total + (fy.budget || 0), 0)
}

export function ProjectsStep() {
  const { toast } = useToast()
  const { onBlur } = useFieldBlurAutoSave()
  const { data, addProject, updateProject, removeProject } = useMouApplicationStore()
  
  const {
    register,
    control,
    setValue,
    watch,
    formState: { errors }
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      projects: data.projects || []
    }
  })

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'projects'
  })

  const handleAddProject = useCallback(() => {
    const newProject = {
      ...defaultProject,
      goals: [{ id: crypto.randomUUID(), description: "", isOverallGoal: true }]
    }
    append(newProject)
    addProject(newProject)
  }, [append, addProject])

  const handleRemoveProject = useCallback((index: number, id?: string) => {
    remove(index)
    if (id) {
      removeProject(id)
    }
  }, [remove, removeProject])

  const watchProjects = watch('projects')

  const handleFieldChange = useCallback((index: number, field: string, value: any) => {
    const project = watchProjects[index]
    if (project) {
      updateProject(project.id!, { [field]: value })
    }
  }, [watchProjects, updateProject])

  return (
    <div className="space-y-6">
      {errors.projects?.root && (
        <Alert variant="destructive">
          <AlertDescription>
            {errors.projects.root.message}
          </AlertDescription>
        </Alert>
      )}

      {fields.map((field, projectIndex) => {
        const totalBudget = useCalculateTotalBudget(watchProjects[projectIndex]?.fiscalYears || [])

        return (
          <Card key={field.id} className="p-6 relative">
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-4 top-4"
              onClick={() => handleRemoveProject(projectIndex, field.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>

            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor={`projects.${projectIndex}.name`}>Project Name</Label>
                <Input
                  {...register(`projects.${projectIndex}.name`)}
                  onBlur={(e) => {
                    onBlur()
                    handleFieldChange(projectIndex, 'name', e.target.value)
                  }}
                />
                {errors.projects?.[projectIndex]?.name && (
                  <p className="text-sm text-red-500">
                    {errors.projects[projectIndex]?.name?.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label>Funding Source</Label>
                <Select
                  value={String(watchProjects[projectIndex]?.fundingSourceId)}
                  onValueChange={(value) => {
                    setValue(`projects.${projectIndex}.fundingSourceId`, parseInt(value, 10))
                    handleFieldChange(projectIndex, 'fundingSourceId', parseInt(value, 10))
                    onBlur()
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select funding source" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockFundingSources.map((source: { id: number; name: string }) => (
                      <SelectItem key={source.id} value={String(source.id)}>
                        {source.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.projects?.[projectIndex]?.fundingSourceId && (
                  <p className="text-sm text-red-500">
                    {errors.projects[projectIndex]?.fundingSourceId?.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label>Funding Unit</Label>
                <Select
                  value={String(watchProjects[projectIndex]?.fundingUnitId)}
                  onValueChange={(value) => {
                    setValue(`projects.${projectIndex}.fundingUnitId`, parseInt(value, 10))
                    handleFieldChange(projectIndex, 'fundingUnitId', parseInt(value, 10))
                    onBlur()
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select funding unit" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockFundingUnits.map((unit: { id: number; name: string }) => (
                      <SelectItem key={unit.id} value={String(unit.id)}>
                        {unit.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.projects?.[projectIndex]?.fundingUnitId && (
                  <p className="text-sm text-red-500">
                    {errors.projects[projectIndex]?.fundingUnitId?.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label>Budget Type</Label>
                <Select
                  value={String(watchProjects[projectIndex]?.budgetTypeId)}
                  onValueChange={(value) => {
                    setValue(`projects.${projectIndex}.budgetTypeId`, parseInt(value, 10))
                    handleFieldChange(projectIndex, 'budgetTypeId', parseInt(value, 10))
                    onBlur()
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select budget type" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockBudgetTypes.map((type: { id: number; name: string }) => (
                      <SelectItem key={type.id} value={String(type.id)}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.projects?.[projectIndex]?.budgetTypeId && (
                  <p className="text-sm text-red-500">
                    {errors.projects[projectIndex]?.budgetTypeId?.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label>Currency</Label>
                <Select
                  value={String(watchProjects[projectIndex]?.currencyId)}
                  onValueChange={(value) => {
                    setValue(`projects.${projectIndex}.currencyId`, parseInt(value, 10))
                    handleFieldChange(projectIndex, 'currencyId', parseInt(value, 10))
                    onBlur()
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockCurrencies.map((currency: { id: number; name: string; code: string }) => (
                      <SelectItem key={currency.id} value={String(currency.id)}>
                        {currency.code} - {currency.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.projects?.[projectIndex]?.currencyId && (
                  <p className="text-sm text-red-500">
                    {errors.projects[projectIndex]?.currencyId?.message}
                  </p>
                )}
              </div>

              <div className="col-span-2 grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label>Start Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !watchProjects[projectIndex]?.startDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {watchProjects[projectIndex]?.startDate ? (
                          format(watchProjects[projectIndex].startDate, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={watchProjects[projectIndex]?.startDate}
                        onSelect={(date) => {
                          if (date) {
                            setValue(`projects.${projectIndex}.startDate`, date)
                            handleFieldChange(projectIndex, 'startDate', date)
                            onBlur()
                          }
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label>End Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !watchProjects[projectIndex]?.endDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {watchProjects[projectIndex]?.endDate ? (
                          format(watchProjects[projectIndex].endDate, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={watchProjects[projectIndex]?.endDate}
                        onSelect={(date) => {
                          if (date) {
                            setValue(`projects.${projectIndex}.endDate`, date)
                            handleFieldChange(projectIndex, 'endDate', date)
                            onBlur()
                          }
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              <div className="col-span-2">
                <div className="space-y-2">
                  <Label>Project Goals</Label>
                  {watchProjects[projectIndex]?.goals?.map((goal: Goal, goalIndex: number) => (
                    <div key={goal.id} className="flex items-start space-x-2">
                      <Textarea
                        value={goal.description}
                        onChange={(e) => {
                          const updatedGoals = [...(watchProjects[projectIndex].goals || [])]
                          updatedGoals[goalIndex] = {
                            ...updatedGoals[goalIndex],
                            description: e.target.value,
                          }
                          setValue(`projects.${projectIndex}.goals`, updatedGoals)
                          handleFieldChange(projectIndex, 'goals', updatedGoals)
                        }}
                        onBlur={onBlur}
                        placeholder={`Enter ${goal.isOverallGoal ? 'overall' : 'specific'} goal`}
                        className="flex-1"
                      />
                      {!goal.isOverallGoal && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            const updatedGoals = watchProjects[projectIndex].goals.filter(
                              (_, i) => i !== goalIndex
                            )
                            setValue(`projects.${projectIndex}.goals`, updatedGoals)
                            handleFieldChange(projectIndex, 'goals', updatedGoals)
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => {
                      const newGoal = {
                        id: crypto.randomUUID(),
                        description: '',
                        isOverallGoal: false,
                      }
                      const updatedGoals = [...(watchProjects[projectIndex].goals || []), newGoal]
                      setValue(`projects.${projectIndex}.goals`, updatedGoals)
                      handleFieldChange(projectIndex, 'goals', updatedGoals)
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Specific Goal
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        )
      })}

      <Button
        type="button"
        variant="outline"
        className="w-full"
        onClick={handleAddProject}
      >
        Add Project
      </Button>
    </div>
  )
}
