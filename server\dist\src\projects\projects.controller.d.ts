import { ProjectsService } from './projects.service';
import { CreateProjectDto, UpdateProjectDto } from './dto/create-project.dto';
export declare class ProjectsController {
    private readonly projectsService;
    constructor(projectsService: ProjectsService);
    create(createProjectDto: CreateProjectDto): Promise<{
        organization: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationName: string;
            organizationPhoneNumber: string;
            organizationEmail: string;
            organizationWebsite: string | null;
            homeCountryRepresentative: string;
            rwandaRepresentative: string;
            organizationRgbNumber: string | null;
            organizationTypeId: number;
            centralLevelInstitutionId: number | null;
        };
        budgetType: {
            id: number;
            typeName: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        fundingUnit: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            unitName: string;
        };
        fundingSource: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            sourceName: string;
        };
        mouApplication: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            userId: number | null;
            applicationKey: string;
            mouId: number;
        };
        documents: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            projectId: number | null;
            documentTypeId: number;
        }[];
        activities: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            description: string | null;
            projectId: number;
            startDate: Date;
            endDate: Date;
            implementer: string;
            implementerUnit: string;
            fiscalYear: number;
            domainInterventionId: number;
            inputId: number;
        }[];
        approvalSteps: ({
            reviewer: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                firstName: string;
                lastName: string;
                email: string;
                password: string;
                emailVerified: boolean;
                verifiedAt: Date | null;
                verificationToken: string | null;
                verificationTokenExpiryTime: Date | null;
                refreshToken: string | null;
                passwordResetToken: string | null;
                passwordResetExpires: Date | null;
                invitationToken: string | null;
                invitationExpires: Date | null;
                invitedBy: string | null;
                role: import("@prisma/client").$Enums.UserRole;
                isActive: boolean;
                organizationId: number | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            role: import("@prisma/client").$Enums.UserRole;
            mouApplicationId: number;
            projectId: number | null;
            reviewerId: number;
            status: import("@prisma/client").$Enums.ApprovalStatusType;
            comment: string | null;
        })[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        description: string | null;
        duration: number;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        mouApplicationId: number;
    }>;
    findAll(page?: string, limit?: string, mouApplicationId?: string, organizationId?: string): Promise<{
        data: ({
            organization: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                organizationName: string;
                organizationPhoneNumber: string;
                organizationEmail: string;
                organizationWebsite: string | null;
                homeCountryRepresentative: string;
                rwandaRepresentative: string;
                organizationRgbNumber: string | null;
                organizationTypeId: number;
                centralLevelInstitutionId: number | null;
            };
            budgetType: {
                id: number;
                typeName: string;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
            };
            fundingUnit: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                unitName: string;
            };
            fundingSource: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                sourceName: string;
            };
            mouApplication: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                userId: number | null;
                applicationKey: string;
                mouId: number;
            };
            documents: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                organizationId: number;
                description: string | null;
                projectId: number | null;
                documentTypeId: number;
            }[];
            activities: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
                description: string | null;
                projectId: number;
                startDate: Date;
                endDate: Date;
                implementer: string;
                implementerUnit: string;
                fiscalYear: number;
                domainInterventionId: number;
                inputId: number;
            }[];
            approvalSteps: ({
                reviewer: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    firstName: string;
                    lastName: string;
                    email: string;
                    password: string;
                    emailVerified: boolean;
                    verifiedAt: Date | null;
                    verificationToken: string | null;
                    verificationTokenExpiryTime: Date | null;
                    refreshToken: string | null;
                    passwordResetToken: string | null;
                    passwordResetExpires: Date | null;
                    invitationToken: string | null;
                    invitationExpires: Date | null;
                    invitedBy: string | null;
                    role: import("@prisma/client").$Enums.UserRole;
                    isActive: boolean;
                    organizationId: number | null;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                role: import("@prisma/client").$Enums.UserRole;
                mouApplicationId: number;
                projectId: number | null;
                reviewerId: number;
                status: import("@prisma/client").$Enums.ApprovalStatusType;
                comment: string | null;
            })[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            duration: number;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            mouApplicationId: number;
        })[];
        meta: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    getProjectsByMouApplication(mouApplicationId: number): Promise<({
        organization: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationName: string;
            organizationPhoneNumber: string;
            organizationEmail: string;
            organizationWebsite: string | null;
            homeCountryRepresentative: string;
            rwandaRepresentative: string;
            organizationRgbNumber: string | null;
            organizationTypeId: number;
            centralLevelInstitutionId: number | null;
        };
        budgetType: {
            id: number;
            typeName: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        fundingUnit: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            unitName: string;
        };
        fundingSource: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            sourceName: string;
        };
        documents: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            projectId: number | null;
            documentTypeId: number;
        }[];
        activities: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            description: string | null;
            projectId: number;
            startDate: Date;
            endDate: Date;
            implementer: string;
            implementerUnit: string;
            fiscalYear: number;
            domainInterventionId: number;
            inputId: number;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        description: string | null;
        duration: number;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        mouApplicationId: number;
    })[]>;
    findOne(id: number): Promise<{
        organization: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationName: string;
            organizationPhoneNumber: string;
            organizationEmail: string;
            organizationWebsite: string | null;
            homeCountryRepresentative: string;
            rwandaRepresentative: string;
            organizationRgbNumber: string | null;
            organizationTypeId: number;
            centralLevelInstitutionId: number | null;
        };
        budgetType: {
            id: number;
            typeName: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        fundingUnit: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            unitName: string;
        };
        fundingSource: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            sourceName: string;
        };
        mouApplication: {
            mou: {
                party: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    name: string;
                    organizationId: number;
                    duration: number;
                    partyId: string;
                    responsibilityId: number | null;
                    objectiveId: number | null;
                    goalId: number;
                    signatory: string;
                    position: string;
                    reasonForExtendedDuration: string | null;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                mouKey: string;
                partyId: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            userId: number | null;
            applicationKey: string;
            mouId: number;
        };
        documents: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            projectId: number | null;
            documentTypeId: number;
        }[];
        activities: ({
            domainIntervention: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                description: string | null;
                userId: number | null;
                parentId: number | null;
                domainName: string;
            };
            input: {
                inputSubclass: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    name: string;
                    inputId: number;
                    subclassId: number;
                    budget: number;
                }[];
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            description: string | null;
            projectId: number;
            startDate: Date;
            endDate: Date;
            implementer: string;
            implementerUnit: string;
            fiscalYear: number;
            domainInterventionId: number;
            inputId: number;
        })[];
        approvalSteps: ({
            reviewer: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                firstName: string;
                lastName: string;
                email: string;
                password: string;
                emailVerified: boolean;
                verifiedAt: Date | null;
                verificationToken: string | null;
                verificationTokenExpiryTime: Date | null;
                refreshToken: string | null;
                passwordResetToken: string | null;
                passwordResetExpires: Date | null;
                invitationToken: string | null;
                invitationExpires: Date | null;
                invitedBy: string | null;
                role: import("@prisma/client").$Enums.UserRole;
                isActive: boolean;
                organizationId: number | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            role: import("@prisma/client").$Enums.UserRole;
            mouApplicationId: number;
            projectId: number | null;
            reviewerId: number;
            status: import("@prisma/client").$Enums.ApprovalStatusType;
            comment: string | null;
        })[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        description: string | null;
        duration: number;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        mouApplicationId: number;
    }>;
    update(id: number, updateProjectDto: UpdateProjectDto): Promise<{
        organization: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationName: string;
            organizationPhoneNumber: string;
            organizationEmail: string;
            organizationWebsite: string | null;
            homeCountryRepresentative: string;
            rwandaRepresentative: string;
            organizationRgbNumber: string | null;
            organizationTypeId: number;
            centralLevelInstitutionId: number | null;
        };
        budgetType: {
            id: number;
            typeName: string;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        fundingUnit: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            unitName: string;
        };
        fundingSource: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            sourceName: string;
        };
        mouApplication: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            userId: number | null;
            applicationKey: string;
            mouId: number;
        };
        documents: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            description: string | null;
            projectId: number | null;
            documentTypeId: number;
        }[];
        activities: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            description: string | null;
            projectId: number;
            startDate: Date;
            endDate: Date;
            implementer: string;
            implementerUnit: string;
            fiscalYear: number;
            domainInterventionId: number;
            inputId: number;
        }[];
        approvalSteps: ({
            reviewer: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                firstName: string;
                lastName: string;
                email: string;
                password: string;
                emailVerified: boolean;
                verifiedAt: Date | null;
                verificationToken: string | null;
                verificationTokenExpiryTime: Date | null;
                refreshToken: string | null;
                passwordResetToken: string | null;
                passwordResetExpires: Date | null;
                invitationToken: string | null;
                invitationExpires: Date | null;
                invitedBy: string | null;
                role: import("@prisma/client").$Enums.UserRole;
                isActive: boolean;
                organizationId: number | null;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            role: import("@prisma/client").$Enums.UserRole;
            mouApplicationId: number;
            projectId: number | null;
            reviewerId: number;
            status: import("@prisma/client").$Enums.ApprovalStatusType;
            comment: string | null;
        })[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        description: string | null;
        duration: number;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        mouApplicationId: number;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
}
