import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreatePartyDto, UpdatePartyDto } from './dto/create-party.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class PartiesService {
  constructor(private prisma: PrismaService) {}

  async create(createPartyDto: CreatePartyDto) {
    try {
      // Validate extended duration reason
      if (createPartyDto.duration > 1 && !createPartyDto.reasonForExtendedDuration) {
        throw new BadRequestException('Reason for extended duration is required when duration is more than 1 year');
      }

      // Validate foreign key relationships
      await this.validateRelationships(createPartyDto);

      // Check if party ID is unique
      const existingParty = await this.prisma.party.findUnique({
        where: { partyId: createPartyDto.partyId },
      });

      if (existingParty) {
        throw new ConflictException(`Party with ID ${createPartyDto.partyId} already exists`);
      }

      const party = await this.prisma.party.create({
        data: createPartyDto,
        include: {
          organization: {
            include: {
              organizationType: true,
            },
          },
          responsibility: true,
          objective: true,
          goal: true,
          mou: {
            include: {
              mouApplications: true,
            },
          },
        },
      });

      return party;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Party ID must be unique');
        }
        if (error.code === 'P2003') {
          throw new BadRequestException('Invalid foreign key reference');
        }
      }
      throw error;
    }
  }

  async findAll(page: number = 1, limit: number = 10, organizationId?: number) {
    const skip = (page - 1) * limit;
    
    const where: Prisma.PartyWhereInput = {
      deleted: false,
      ...(organizationId && { organizationId }),
    };

    const [parties, total] = await Promise.all([
      this.prisma.party.findMany({
        where,
        skip,
        take: limit,
        include: {
          organization: {
            include: {
              organizationType: true,
            },
          },
          responsibility: true,
          objective: true,
          goal: true,
          mou: {
            include: {
              mouApplications: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      }),
      this.prisma.party.count({ where }),
    ]);

    return {
      data: parties,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number) {
    const party = await this.prisma.party.findFirst({
      where: {
        id,
        deleted: false,
      },
      include: {
        organization: {
          include: {
            organizationType: true,
            addresses: true,
          },
        },
        responsibility: true,
        objective: true,
        goal: true,
        mou: {
          include: {
            mouApplications: {
              include: {
                projects: true,
                approvalSteps: {
                  include: {
                    reviewer: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!party) {
      throw new NotFoundException(`Party with ID ${id} not found`);
    }

    return party;
  }

  async findByPartyId(partyId: string) {
    const party = await this.prisma.party.findFirst({
      where: {
        partyId,
        deleted: false,
      },
      include: {
        organization: {
          include: {
            organizationType: true,
          },
        },
        responsibility: true,
        objective: true,
        goal: true,
        mou: {
          include: {
            mouApplications: true,
          },
        },
      },
    });

    if (!party) {
      throw new NotFoundException(`Party with ID ${partyId} not found`);
    }

    return party;
  }

  async update(id: number, updatePartyDto: UpdatePartyDto) {
    // Check if party exists
    const existingParty = await this.prisma.party.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingParty) {
      throw new NotFoundException(`Party with ID ${id} not found`);
    }

    // Validate extended duration reason
    const newDuration = updatePartyDto.duration ?? existingParty.duration;
    const newReason = updatePartyDto.reasonForExtendedDuration ?? existingParty.reasonForExtendedDuration;

    if (newDuration > 1 && !newReason) {
      throw new BadRequestException('Reason for extended duration is required when duration is more than 1 year');
    }

    // Validate foreign key relationships if they are being updated
    if (Object.keys(updatePartyDto).some(key => 
      ['organizationId', 'responsibilityId', 'objectiveId', 'goalId'].includes(key)
    )) {
      await this.validateRelationships({
        ...existingParty,
        ...updatePartyDto,
      } as CreatePartyDto);
    }

    try {
      const updatedParty = await this.prisma.party.update({
        where: { id },
        data: updatePartyDto,
        include: {
          organization: {
            include: {
              organizationType: true,
            },
          },
          responsibility: true,
          objective: true,
          goal: true,
          mou: {
            include: {
              mouApplications: true,
            },
          },
        },
      });

      return updatedParty;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2003') {
          throw new BadRequestException('Invalid foreign key reference');
        }
      }
      throw error;
    }
  }

  async remove(id: number) {
    // Check if party exists
    const existingParty = await this.prisma.party.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingParty) {
      throw new NotFoundException(`Party with ID ${id} not found`);
    }

    // Check if party has an associated MoU
    const mouWithParty = await this.prisma.mou.findUnique({
      where: { partyId: id },
    });

    if (mouWithParty) {
      throw new ConflictException('Cannot delete party that has an associated MoU');
    }

    // Soft delete
    await this.prisma.party.update({
      where: { id },
      data: { deleted: true },
    });

    return { message: 'Party deleted successfully' };
  }

  async getPartiesByOrganization(organizationId: number) {
    const parties = await this.prisma.party.findMany({
      where: {
        organizationId,
        deleted: false,
      },
      include: {
        responsibility: true,
        objective: true,
        goal: true,
        mou: {
          include: {
            mouApplications: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return parties;
  }

  private async validateRelationships(dto: CreatePartyDto) {
    const [organization, responsibility, objective, goal] = await Promise.all([
      this.prisma.organization.findUnique({ where: { id: dto.organizationId } }),
      this.prisma.responsibility.findUnique({ where: { id: dto.responsibilityId } }),
      this.prisma.objective.findUnique({ where: { id: dto.objectiveId } }),
      this.prisma.goal.findUnique({ where: { id: dto.goalId } }),
    ]);

    if (!organization) {
      throw new NotFoundException(`Organization with ID ${dto.organizationId} not found`);
    }
    if (!responsibility) {
      throw new NotFoundException(`Responsibility with ID ${dto.responsibilityId} not found`);
    }
    if (!objective) {
      throw new NotFoundException(`Objective with ID ${dto.objectiveId} not found`);
    }
    if (!goal) {
      throw new NotFoundException(`Goal with ID ${dto.goalId} not found`);
    }
  }
}
