"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowValidatorInterceptor = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const client_1 = require("@prisma/client");
let WorkflowValidatorInterceptor = class WorkflowValidatorInterceptor {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        const method = request.method;
        const url = request.url;
        const applicationId = this.extractApplicationId(url);
        const action = this.determineAction(method, url);
        if (applicationId) {
            const application = await this.prisma.mouApplication.findUnique({
                where: { id: applicationId },
                include: {
                    approvalSteps: true,
                    projects: true
                }
            });
            if (!application) {
                throw new common_1.BadRequestException('Application not found');
            }
            await this.validateWorkflowTransition(application, action, user);
        }
        return next.handle();
    }
    extractApplicationId(url) {
        const match = url.match(/\/mou-applications\/(\d+)/);
        return match ? parseInt(match[1]) : null;
    }
    determineAction(method, url) {
        if (url.includes('/review'))
            return 'REVIEW';
        if (url.includes('/assign-expert'))
            return 'ASSIGN_EXPERT';
        if (url.includes('/request-modification'))
            return 'REQUEST_MODIFICATION';
        if (url.includes('/approve'))
            return 'APPROVE';
        if (method === 'PUT' && url.includes('/update-status'))
            return 'UPDATE_STATUS';
        return 'OTHER';
    }
    async validateWorkflowTransition(application, action, user) {
        switch (action) {
            case 'REVIEW':
                await this.validateReviewTransition(application, user);
                break;
            case 'ASSIGN_EXPERT':
                await this.validateExpertAssignment(application, user);
                break;
            case 'REQUEST_MODIFICATION':
                await this.validateModificationRequest(application, user);
                break;
            case 'APPROVE':
                await this.validateApprovalTransition(application, user);
                break;
            case 'UPDATE_STATUS':
                await this.validateStatusUpdate(application, user);
                break;
        }
    }
    async validateReviewTransition(application, user) {
        if (!user.isActive) {
            throw new common_1.ForbiddenException('User is inactive and cannot perform reviews');
        }
        const validTransitions = {
            [client_1.ApplicationStatus.SUBMITTED]: [client_1.UserRole.PARTNER_COORDINATOR],
            [client_1.ApplicationStatus.IN_TECHNICAL_REVIEW]: [client_1.UserRole.TECHNICAL_EXPERT],
            [client_1.ApplicationStatus.IN_LEGAL_REVIEW]: [client_1.UserRole.LEGAL_OFFICER],
        };
        const allowedRoles = validTransitions[application.status];
        if (!allowedRoles?.includes(user.reviewerRole)) {
            throw new common_1.ForbiddenException(`Reviews for applications in ${application.status} status can only be made by ${allowedRoles.join(', ')}`);
        }
        if (user.reviewerRole === client_1.UserRole.TECHNICAL_EXPERT) {
            const isAssigned = application.technicalAssignments.some((assignment) => assignment.assignedById === user.id && assignment.isActive);
            if (!isAssigned) {
                throw new common_1.ForbiddenException('You are not assigned to review this application');
            }
        }
    }
    async validateExpertAssignment(application, user) {
        if (user.reviewerRole !== client_1.UserRole.PARTNER_COORDINATOR) {
            throw new common_1.ForbiddenException('Only Partner Coordinators can assign technical experts');
        }
        if (![client_1.ApplicationStatus.SUBMITTED, client_1.ApplicationStatus.IN_TECHNICAL_REVIEW].includes(application.status)) {
            throw new common_1.BadRequestException('Application is not in a state where technical experts can be assigned');
        }
    }
    async validateModificationRequest(application, user) {
        const validRoles = [
            client_1.UserRole.PARTNER_COORDINATOR,
            client_1.UserRole.TECHNICAL_EXPERT,
            client_1.UserRole.LEGAL_OFFICER
        ];
        if (!validRoles.includes(user.reviewerRole)) {
            throw new common_1.ForbiddenException('You are not authorized to request modifications');
        }
        if (user.reviewerRole === client_1.UserRole.TECHNICAL_EXPERT) {
            const isAssigned = application.technicalAssignments.some((assignment) => assignment.assignedById === user.id && assignment.isActive);
            if (!isAssigned) {
                throw new common_1.ForbiddenException('You are not assigned to this application');
            }
        }
    }
    async validateApprovalTransition(application, user) {
        if (!user.isActive) {
            throw new common_1.ForbiddenException('User is inactive and cannot perform approvals');
        }
        const approvalSequence = [
            client_1.UserRole.PARTNER_COORDINATOR,
            client_1.UserRole.TECHNICAL_EXPERT,
            client_1.UserRole.LEGAL_OFFICER,
            client_1.UserRole.HEAD_OF_DEPARTMENT,
            client_1.UserRole.PermanentSecretary,
            client_1.UserRole.MINISTER
        ];
        if (application.status !== client_1.ApplicationStatus.IN_REVIEW) {
            throw new common_1.BadRequestException('Application is not ready for approval');
        }
        const currentApprovals = application.approvalDecisions;
        const nextApproverRole = this.getNextApproverRole(currentApprovals, approvalSequence);
        if (user.approverRole !== nextApproverRole) {
            throw new common_1.ForbiddenException(`This application needs approval from ${nextApproverRole}. Your role is ${user.approverRole}`);
        }
    }
    async validateStatusUpdate(application, user) {
        if (user.reviewerRole !== client_1.UserRole.PARTNER_COORDINATOR) {
            throw new common_1.ForbiddenException('Only Partner Coordinators can update application status');
        }
    }
    getNextApproverRole(currentApprovals, approvalSequence) {
        const approvedRoles = currentApprovals.map(approval => approval.approverRole);
        for (const role of approvalSequence) {
            if (!approvedRoles.includes(role)) {
                return role;
            }
        }
        return null;
    }
};
exports.WorkflowValidatorInterceptor = WorkflowValidatorInterceptor;
exports.WorkflowValidatorInterceptor = WorkflowValidatorInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], WorkflowValidatorInterceptor);
//# sourceMappingURL=workflow-validator.interceptor.js.map