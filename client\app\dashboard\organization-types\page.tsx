"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Plus, MoreHorizontal, Search, Edit, Trash2 } from "lucide-react"
import { organizationService, type OrganizationType } from "@/lib/services/organization.service"

export default function OrganizationTypesPage() {
  const [organizationTypes, setOrganizationTypes] = useState<OrganizationType[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(true)
  const [formLoading, setFormLoading] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingType, setEditingType] = useState<OrganizationType | null>(null)
  const [typeName, setTypeName] = useState("")
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  useEffect(() => {
    loadOrganizationTypes()
  }, [])

  const loadOrganizationTypes = async () => {
    try {
      setLoading(true)
      const data = await organizationService.getOrganizationTypes()
      setOrganizationTypes(data)
    } catch (error) {
      console.error("Failed to load organization types:", error)
      setError("Failed to load organization types")
    } finally {
      setLoading(false)
    }
  }

  const filteredTypes = organizationTypes.filter((type) =>
    type.typeName.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setFormLoading(true)
    setError("")
    setSuccess("")

    try {
      if (editingType) {
        // Update existing type
        await organizationService.updateOrganizationType(editingType.id, typeName)
        setSuccess("Organization type updated successfully")
      } else {
        // Create new type
        await organizationService.createOrganizationType(typeName)
        setSuccess("Organization type created successfully")
      }

      // Reload the data to get the latest from server
      await loadOrganizationTypes()
      setDialogOpen(false)
      setTypeName("")
      setEditingType(null)
    } catch (err: any) {
      console.error("Failed to save organization type:", err)
      setError(err.response?.data?.message || "Failed to save organization type")
    } finally {
      setFormLoading(false)
    }
  }

  const handleEdit = (type: OrganizationType) => {
    setEditingType(type)
    setTypeName(type.typeName)
    setDialogOpen(true)
  }

  const handleDelete = async (id: number) => {
    if (confirm("Are you sure you want to delete this organization type?")) {
      try {
        await organizationService.deleteOrganizationType(id)
        setSuccess("Organization type deleted successfully")
        // Reload the data to get the latest from server
        await loadOrganizationTypes()
      } catch (err: any) {
        console.error("Failed to delete organization type:", err)
        setError(err.response?.data?.message || "Failed to delete organization type")
      }
    }
  }

  const resetForm = () => {
    setTypeName("")
    setEditingType(null)
    setError("")
    setSuccess("")
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Organization Types</h2>
        <Dialog
          open={dialogOpen}
          onOpenChange={(open) => {
            setDialogOpen(open)
            if (!open) resetForm()
          }}
        >
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Add Organization Type
            </Button>
          </DialogTrigger>
          <DialogContent>
            <form onSubmit={handleSubmit}>
              <DialogHeader>
                <DialogTitle>{editingType ? "Edit Organization Type" : "Add Organization Type"}</DialogTitle>
                <DialogDescription>
                  {editingType ? "Update the organization type details." : "Create a new organization type."}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <div className="space-y-2">
                  <Label htmlFor="typeName">Type Name</Label>
                  <Input
                    id="typeName"
                    value={typeName}
                    onChange={(e) => setTypeName(e.target.value)}
                    placeholder="Enter organization type name"
                    required
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" disabled={formLoading}>
                  {formLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {editingType ? "Updating..." : "Creating..."}
                    </>
                  ) : editingType ? (
                    "Update"
                  ) : (
                    "Create"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {success && (
        <Alert className="bg-green-50 text-green-700 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search organization types..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Type Name</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead>Updated At</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                  <p className="mt-2 text-muted-foreground">Loading organization types...</p>
                </TableCell>
              </TableRow>
            ) : filteredTypes.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-8">
                  <p className="text-muted-foreground">
                    {searchTerm ? "No organization types found matching your search." : "No organization types found."}
                  </p>
                </TableCell>
              </TableRow>
            ) : (
              filteredTypes.map((type) => (
                <TableRow key={type.id}>
                  <TableCell className="font-medium">{type.typeName}</TableCell>
                  <TableCell>{new Date(type.createAt).toLocaleDateString()}</TableCell>
                  <TableCell>{new Date(type.updatedAt).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleEdit(type)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDelete(type.id)} className="text-red-600">
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
