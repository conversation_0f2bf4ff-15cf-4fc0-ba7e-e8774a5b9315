"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InputsModule = void 0;
const common_1 = require("@nestjs/common");
const inputs_service_1 = require("./inputs.service");
const inputs_controller_1 = require("./inputs.controller");
const prisma_module_1 = require("../prisma/prisma.module");
let InputsModule = class InputsModule {
};
exports.InputsModule = InputsModule;
exports.InputsModule = InputsModule = __decorate([
    (0, common_1.Module)({
        imports: [prisma_module_1.PrismaModule],
        controllers: [inputs_controller_1.InputsController],
        providers: [inputs_service_1.InputsService],
        exports: [inputs_service_1.InputsService],
    })
], InputsModule);
//# sourceMappingURL=inputs.module.js.map