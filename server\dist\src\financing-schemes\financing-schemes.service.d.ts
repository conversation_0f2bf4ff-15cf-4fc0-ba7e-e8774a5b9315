import { PrismaService } from '../prisma/prisma.service';
import { CreateFinancingSchemeDto, UpdateFinancingSchemeDto } from './dto';
export declare class FinancingSchemesService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    create(createFinancingSchemeDto: CreateFinancingSchemeDto, userId: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        schemeName: string;
        terms: string | null;
        conditions: string | null;
    }>;
    findAll(): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        schemeName: string;
        terms: string | null;
        conditions: string | null;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        schemeName: string;
        terms: string | null;
        conditions: string | null;
    }>;
    update(id: number, updateFinancingSchemeDto: UpdateFinancingSchemeDto, userId: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        schemeName: string;
        terms: string | null;
        conditions: string | null;
    }>;
    remove(id: number, userId: string): Promise<{
        success: boolean;
    }>;
}
