import { PrismaService } from '../prisma/prisma.service';
import { EmailService } from '../email/email.service';
import { UserRole } from '@prisma/client';
export declare class NotificationService {
    private prisma;
    private emailService;
    constructor(prisma: PrismaService, emailService: EmailService);
    notifyApplicationSubmitted(applicationId: number): Promise<void>;
    notifyTechnicalExpertAssigned(applicationId: number, expertId: number): Promise<void>;
    notifyReviewCompleted(applicationId: number, reviewerId: number, reviewType: string): Promise<void>;
    notifyModificationRequested(applicationId: number, requesterId: number): Promise<void>;
    notifyApprovalRequired(applicationId: number, approverRole: UserRole): Promise<void>;
    notifyApplicationApproved(applicationId: number): Promise<void>;
    createNotification(data: {
        userId: number;
        title: string;
        message: string;
        type: string;
        applicationId: number;
    }): Promise<{
        userId: number;
        title: string;
        message: string;
        type: string;
        applicationId: number;
        id: number;
    }>;
    private getApplicationWithDetails;
    markNotificationAsRead(notificationId: number, userId: number): Promise<{
        id: number;
        isRead: boolean;
        readAt: Date;
    }>;
    getUserNotifications(userId: number, options?: {
        skip?: number;
        take?: number;
        includeRead?: boolean;
    }): Promise<any[]>;
    getUnreadCount(userId: number): Promise<number>;
}
