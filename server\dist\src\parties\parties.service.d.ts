import { PrismaService } from '../prisma/prisma.service';
import { CreatePartyDto, UpdatePartyDto } from './dto/create-party.dto';
export declare class PartiesService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createPartyDto: CreatePartyDto): Promise<{
        organization: {
            organizationType: {
                id: number;
                typeName: string;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationName: string;
            organizationPhoneNumber: string;
            organizationEmail: string;
            organizationWebsite: string | null;
            homeCountryRepresentative: string;
            rwandaRepresentative: string;
            organizationRgbNumber: string | null;
            organizationTypeId: number;
            centralLevelInstitutionId: number | null;
        };
        responsibility: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
        objective: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
        goal: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
        mou: {
            mouApplications: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                userId: number | null;
                applicationKey: string;
                mouId: number;
            }[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            mouKey: string;
            partyId: number;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        duration: number;
        partyId: string;
        responsibilityId: number | null;
        objectiveId: number | null;
        goalId: number;
        signatory: string;
        position: string;
        reasonForExtendedDuration: string | null;
    }>;
    findAll(page?: number, limit?: number, organizationId?: number): Promise<{
        data: ({
            organization: {
                organizationType: {
                    id: number;
                    typeName: string;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                };
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                organizationName: string;
                organizationPhoneNumber: string;
                organizationEmail: string;
                organizationWebsite: string | null;
                homeCountryRepresentative: string;
                rwandaRepresentative: string;
                organizationRgbNumber: string | null;
                organizationTypeId: number;
                centralLevelInstitutionId: number | null;
            };
            responsibility: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
            };
            objective: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
            };
            goal: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                name: string;
            };
            mou: {
                mouApplications: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    userId: number | null;
                    applicationKey: string;
                    mouId: number;
                }[];
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                mouKey: string;
                partyId: number;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: number;
            duration: number;
            partyId: string;
            responsibilityId: number | null;
            objectiveId: number | null;
            goalId: number;
            signatory: string;
            position: string;
            reasonForExtendedDuration: string | null;
        })[];
        meta: {
            total: number;
            page: number;
            limit: number;
            totalPages: number;
        };
    }>;
    findOne(id: number): Promise<{
        organization: {
            organizationType: {
                id: number;
                typeName: string;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
            };
            addresses: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                organizationId: number;
                province: string | null;
                addressType: import("@prisma/client").$Enums.AddressType;
                country: string;
                district: string | null;
                sector: string | null;
                cell: string | null;
                street: string | null;
                poBox: string | null;
                postalCode: string | null;
            }[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationName: string;
            organizationPhoneNumber: string;
            organizationEmail: string;
            organizationWebsite: string | null;
            homeCountryRepresentative: string;
            rwandaRepresentative: string;
            organizationRgbNumber: string | null;
            organizationTypeId: number;
            centralLevelInstitutionId: number | null;
        };
        responsibility: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
        objective: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
        goal: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
        mou: {
            mouApplications: ({
                projects: {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    name: string;
                    organizationId: number;
                    description: string | null;
                    duration: number;
                    budgetTypeId: number;
                    fundingUnitId: number;
                    fundingSourceId: number;
                    mouApplicationId: number;
                }[];
                approvalSteps: ({
                    reviewer: {
                        id: number;
                        createdAt: Date;
                        updatedAt: Date;
                        deleted: boolean;
                        firstName: string;
                        lastName: string;
                        email: string;
                        password: string;
                        emailVerified: boolean;
                        verifiedAt: Date | null;
                        verificationToken: string | null;
                        verificationTokenExpiryTime: Date | null;
                        refreshToken: string | null;
                        passwordResetToken: string | null;
                        passwordResetExpires: Date | null;
                        invitationToken: string | null;
                        invitationExpires: Date | null;
                        invitedBy: string | null;
                        role: import("@prisma/client").$Enums.UserRole;
                        isActive: boolean;
                        organizationId: number | null;
                    };
                } & {
                    id: number;
                    createdAt: Date;
                    updatedAt: Date;
                    deleted: boolean;
                    role: import("@prisma/client").$Enums.UserRole;
                    mouApplicationId: number;
                    projectId: number | null;
                    reviewerId: number;
                    status: import("@prisma/client").$Enums.ApprovalStatusType;
                    comment: string | null;
                })[];
            } & {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                userId: number | null;
                applicationKey: string;
                mouId: number;
            })[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            mouKey: string;
            partyId: number;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        duration: number;
        partyId: string;
        responsibilityId: number | null;
        objectiveId: number | null;
        goalId: number;
        signatory: string;
        position: string;
        reasonForExtendedDuration: string | null;
    }>;
    findByPartyId(partyId: string): Promise<{
        organization: {
            organizationType: {
                id: number;
                typeName: string;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationName: string;
            organizationPhoneNumber: string;
            organizationEmail: string;
            organizationWebsite: string | null;
            homeCountryRepresentative: string;
            rwandaRepresentative: string;
            organizationRgbNumber: string | null;
            organizationTypeId: number;
            centralLevelInstitutionId: number | null;
        };
        responsibility: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
        objective: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
        goal: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
        mou: {
            mouApplications: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                userId: number | null;
                applicationKey: string;
                mouId: number;
            }[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            mouKey: string;
            partyId: number;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        duration: number;
        partyId: string;
        responsibilityId: number | null;
        objectiveId: number | null;
        goalId: number;
        signatory: string;
        position: string;
        reasonForExtendedDuration: string | null;
    }>;
    update(id: number, updatePartyDto: UpdatePartyDto): Promise<{
        organization: {
            organizationType: {
                id: number;
                typeName: string;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
            };
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            organizationName: string;
            organizationPhoneNumber: string;
            organizationEmail: string;
            organizationWebsite: string | null;
            homeCountryRepresentative: string;
            rwandaRepresentative: string;
            organizationRgbNumber: string | null;
            organizationTypeId: number;
            centralLevelInstitutionId: number | null;
        };
        responsibility: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
        objective: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
        goal: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
        mou: {
            mouApplications: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                userId: number | null;
                applicationKey: string;
                mouId: number;
            }[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            mouKey: string;
            partyId: number;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        duration: number;
        partyId: string;
        responsibilityId: number | null;
        objectiveId: number | null;
        goalId: number;
        signatory: string;
        position: string;
        reasonForExtendedDuration: string | null;
    }>;
    remove(id: number): Promise<{
        message: string;
    }>;
    getPartiesByOrganization(organizationId: number): Promise<({
        responsibility: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
        objective: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
        goal: {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
        };
        mou: {
            mouApplications: {
                id: number;
                createdAt: Date;
                updatedAt: Date;
                deleted: boolean;
                userId: number | null;
                applicationKey: string;
                mouId: number;
            }[];
        } & {
            id: number;
            createdAt: Date;
            updatedAt: Date;
            deleted: boolean;
            mouKey: string;
            partyId: number;
        };
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: number;
        duration: number;
        partyId: string;
        responsibilityId: number | null;
        objectiveId: number | null;
        goalId: number;
        signatory: string;
        position: string;
        reasonForExtendedDuration: string | null;
    })[]>;
    private validateRelationships;
}
