import { useEffect, useRef, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useMouApplicationStore } from '@/store/mou-application-store';

interface UseAutoSaveOptions {
  interval?: number; // milliseconds
  enabled?: boolean;
  onSave?: () => void;
  onError?: (error: Error) => void;
}

export function useAutoSave(options: UseAutoSaveOptions = {}) {
  const {
    interval = 30000, // 30 seconds default
    enabled = true,
    onSave,
    onError
  } = options;

  const { toast } = useToast();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastSavedRef = useRef<string>('');
  const { data, setLastSaved } = useMouApplicationStore();

  // Serialize current form data for comparison
  const serializeData = useCallback(() => {
    return JSON.stringify({
      mouDuration: data.mouDuration,
      extendedDurationReason: data.extendedDurationReason,
      parties: data.parties,
      projects: data.projects,
      activities: data.activities,
      documents: data.documents.map(doc => ({
        id: doc.id,
        type: doc.type,
        uploaded: doc.uploaded,
        fileName: doc.file?.name
      }))
    });
  }, [data]);

  // Auto-save function
  const performAutoSave = useCallback(async () => {
    try {
      const currentData = serializeData();
      
      // Only save if data has changed
      if (currentData === lastSavedRef.current) {
        return;
      }

      // Simulate API call (replace with actual API call when backend is ready)
      await new Promise(resolve => setTimeout(resolve, 100));
      
      lastSavedRef.current = currentData;
      setLastSaved(new Date());
      
      onSave?.();
      
      // Show subtle toast notification
      toast({
        title: "Draft saved",
        description: "Your progress has been automatically saved.",
        duration: 2000,
      });
    } catch (error) {
      console.error('Auto-save failed:', error);
      onError?.(error as Error);
      
      toast({
        title: "Auto-save failed",
        description: "Unable to save your progress. Please try again.",
        variant: "destructive",
        duration: 3000,
      });
    }
  }, [serializeData, setLastSaved, onSave, onError, toast]);

  // Manual save function
  const saveNow = useCallback(async () => {
    await performAutoSave();
  }, [performAutoSave]);

  // Start auto-save interval
  useEffect(() => {
    if (!enabled) {
      return;
    }

    intervalRef.current = setInterval(performAutoSave, interval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [enabled, interval, performAutoSave]);

  // Save on component unmount
  useEffect(() => {
    return () => {
      if (enabled) {
        performAutoSave();
      }
    };
  }, [enabled, performAutoSave]);

  return {
    saveNow,
    lastSaved: data.lastSaved,
    isEnabled: enabled
  };
}

// Hook for field blur auto-save
export function useFieldBlurAutoSave() {
  const { saveNow } = useAutoSave({ enabled: false }); // Disable interval, only manual saves

  const onBlur = useCallback(async () => {
    await saveNow();
  }, [saveNow]);

  return { onBlur };
}

// Hook for step navigation with auto-save
export function useStepNavigationWithAutoSave() {
  const { saveNow } = useAutoSave();
  const { data, setCurrentStep } = useMouApplicationStore();

  const goToStep = useCallback(async (step: number) => {
    // Save current progress before navigating
    await saveNow();
    setCurrentStep(step);
  }, [saveNow, setCurrentStep]);

  const goToNextStep = useCallback(async () => {
    if (data.currentStep < 6) {
      await goToStep(data.currentStep + 1);
    }
  }, [data.currentStep, goToStep]);

  const goToPreviousStep = useCallback(async () => {
    if (data.currentStep > 1) {
      await goToStep(data.currentStep - 1);
    }
  }, [data.currentStep, goToStep]);

  return {
    currentStep: data.currentStep,
    goToStep,
    goToNextStep,
    goToPreviousStep,
    canGoNext: data.currentStep < 6,
    canGoPrevious: data.currentStep > 1
  };
}
