"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const projects_service_1 = require("./projects.service");
const create_project_dto_1 = require("./dto/create-project.dto");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
const roles_guard_1 = require("../auth/guard/roles.guard");
const roles_decorator_1 = require("../auth/decorator/roles.decorator");
const dto_1 = require("../auth/dto");
let ProjectsController = class ProjectsController {
    constructor(projectsService) {
        this.projectsService = projectsService;
    }
    async create(createProjectDto) {
        return this.projectsService.create(createProjectDto);
    }
    async findAll(page, limit, mouApplicationId, organizationId) {
        const pageNum = page ? parseInt(page, 10) : 1;
        const limitNum = limit ? parseInt(limit, 10) : 10;
        const mouAppIdNum = mouApplicationId ? parseInt(mouApplicationId, 10) : undefined;
        const orgIdNum = organizationId ? parseInt(organizationId, 10) : undefined;
        return this.projectsService.findAll(pageNum, limitNum, mouAppIdNum, orgIdNum);
    }
    async getProjectsByMouApplication(mouApplicationId) {
        return this.projectsService.getProjectsByMouApplication(mouApplicationId);
    }
    async findOne(id) {
        return this.projectsService.findOne(id);
    }
    async update(id, updateProjectDto) {
        return this.projectsService.update(id, updateProjectDto);
    }
    async remove(id) {
        return this.projectsService.remove(id);
    }
};
exports.ProjectsController = ProjectsController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new project' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Project created successfully',
        type: create_project_dto_1.ProjectWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Related entity not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Project document already in use' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_project_dto_1.CreateProjectDto]),
    __metadata("design:returntype", Promise)
], ProjectsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get all projects with pagination' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10)' }),
    (0, swagger_1.ApiQuery)({ name: 'mouApplicationId', required: false, type: Number, description: 'Filter by MoU application ID' }),
    (0, swagger_1.ApiQuery)({ name: 'organizationId', required: false, type: Number, description: 'Filter by organization ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of projects',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/ProjectWithRelationsDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('mouApplicationId')),
    __param(3, (0, common_1.Query)('organizationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String]),
    __metadata("design:returntype", Promise)
], ProjectsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('by-mou-application/:mouApplicationId'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get projects by MoU application ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of projects for the MoU application',
        type: [create_project_dto_1.ProjectWithRelationsDto],
    }),
    __param(0, (0, common_1.Param)('mouApplicationId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ProjectsController.prototype, "getProjectsByMouApplication", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get a project by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Project details',
        type: create_project_dto_1.ProjectWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Project not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ProjectsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Update a project' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Project updated successfully',
        type: create_project_dto_1.ProjectWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Project not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Project document already in use' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, create_project_dto_1.UpdateProjectDto]),
    __metadata("design:returntype", Promise)
], ProjectsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a project (soft delete)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Project deleted successfully',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Project not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ProjectsController.prototype, "remove", null);
exports.ProjectsController = ProjectsController = __decorate([
    (0, swagger_1.ApiTags)('Projects'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('projects'),
    __metadata("design:paramtypes", [projects_service_1.ProjectsService])
], ProjectsController);
//# sourceMappingURL=projects.controller.js.map