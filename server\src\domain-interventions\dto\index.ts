import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsNumber } from 'class-validator';

export class CreateDomainInterventionDto {
    @ApiProperty({ description: 'The name of the domain intervention' })
    @IsNotEmpty()
    @IsString()
    domainName: string;

    @ApiProperty({ description: 'Description of the domain intervention', required: false })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'ID of the parent domain intervention (for hierarchical structure)', required: false })
    @IsOptional()
    @IsNumber()
    parentId?: number;
}

export class UpdateDomainInterventionDto {
    @ApiProperty({ description: 'The name of the domain intervention', required: false })
    @IsOptional()
    @IsString()
    domainName?: string;

    @ApiProperty({ description: 'Description of the domain intervention', required: false })
    @IsOptional()
    @IsString()
    description?: string;

    @ApiProperty({ description: 'ID of the parent domain intervention', required: false })
    @IsOptional()
    @IsNumber()
    parentId?: number;
}

export class DomainInterventionResponseDto {
    @ApiProperty()
    id: number;

    @ApiProperty({ description: 'The name of the domain intervention' })
    domainName: string;

    @ApiProperty({ description: 'Description of the domain intervention' })
    description?: string;

    @ApiProperty({ description: 'ID of the parent domain intervention' })
    parentId?: number;

    @ApiProperty({ description: 'Parent domain intervention details', required: false })
    parent?: DomainInterventionResponseDto;

    @ApiProperty({ description: 'Child domain interventions', type: [DomainInterventionResponseDto] })
    children?: DomainInterventionResponseDto[];

    @ApiProperty({ description: 'When the domain intervention was created' })
    createdAt: Date;

    @ApiProperty({ description: 'When the domain intervention was last updated' })
    updatedAt: Date;

    @ApiProperty({ description: 'Whether the domain intervention has been soft deleted' })
    deleted: boolean;
}

// Special response type for tree structure
export class DomainInterventionTreeResponseDto extends DomainInterventionResponseDto {
    @ApiProperty({ description: 'Child domain interventions', type: [DomainInterventionTreeResponseDto] })
    children: DomainInterventionTreeResponseDto[];
}
