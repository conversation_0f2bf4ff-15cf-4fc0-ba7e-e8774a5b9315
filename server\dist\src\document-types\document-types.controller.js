"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentTypesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const document_types_service_1 = require("./document-types.service");
const create_document_type_dto_1 = require("./dto/create-document-type.dto");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
const roles_guard_1 = require("../auth/guard/roles.guard");
const roles_decorator_1 = require("../auth/decorator/roles.decorator");
const dto_1 = require("../auth/dto");
let DocumentTypesController = class DocumentTypesController {
    constructor(documentTypesService) {
        this.documentTypesService = documentTypesService;
    }
    async create(createDocumentTypeDto) {
        return this.documentTypesService.create(createDocumentTypeDto);
    }
    async findAll() {
        return this.documentTypesService.findAll();
    }
    async findOne(id) {
        return this.documentTypesService.findOne(id);
    }
    async update(id, updateDocumentTypeDto) {
        return this.documentTypesService.update(id, updateDocumentTypeDto);
    }
    async remove(id) {
        return this.documentTypesService.remove(id);
    }
};
exports.DocumentTypesController = DocumentTypesController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new document type' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Document type created successfully',
        type: create_document_type_dto_1.DocumentTypeResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Document type name already exists' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_document_type_dto_1.CreateDocumentTypeDto]),
    __metadata("design:returntype", Promise)
], DocumentTypesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get all document types' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of document types',
        type: [create_document_type_dto_1.DocumentTypeResponseDto],
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DocumentTypesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get a document type by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Document type details',
        type: create_document_type_dto_1.DocumentTypeResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Document type not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DocumentTypesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Update a document type' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Document type updated successfully',
        type: create_document_type_dto_1.DocumentTypeResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Document type not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Document type name already exists' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, create_document_type_dto_1.UpdateDocumentTypeDto]),
    __metadata("design:returntype", Promise)
], DocumentTypesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a document type (soft delete)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Document type deleted successfully',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Document type not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Document type is being used by documents' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DocumentTypesController.prototype, "remove", null);
exports.DocumentTypesController = DocumentTypesController = __decorate([
    (0, swagger_1.ApiTags)('Document Types'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('document-types'),
    __metadata("design:paramtypes", [document_types_service_1.DocumentTypesService])
], DocumentTypesController);
//# sourceMappingURL=document-types.controller.js.map