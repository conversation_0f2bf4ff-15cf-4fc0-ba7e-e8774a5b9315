"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FundingUnitsModule = void 0;
const common_1 = require("@nestjs/common");
const funding_units_controller_1 = require("./funding-units.controller");
const funding_units_service_1 = require("./funding-units.service");
let FundingUnitsModule = class FundingUnitsModule {
};
exports.FundingUnitsModule = FundingUnitsModule;
exports.FundingUnitsModule = FundingUnitsModule = __decorate([
    (0, common_1.Module)({
        controllers: [funding_units_controller_1.FundingUnitsController],
        providers: [funding_units_service_1.FundingUnitsService],
        exports: [funding_units_service_1.FundingUnitsService],
    })
], FundingUnitsModule);
//# sourceMappingURL=funding-units.module.js.map