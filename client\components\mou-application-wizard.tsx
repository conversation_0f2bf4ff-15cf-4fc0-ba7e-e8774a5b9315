"use client"

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "./ui/card";
import { useMouApplicationStore } from "@/store/mou-application-store";
import { Button } from "./ui/button";
import { Progress } from "./ui/progress";
import { ProgressIndicator } from "./ui/progress-indicator";
import { MouDetailsStep } from "./wizard-steps/mou-details-step";
import { PartyDetailsStep } from "./wizard-steps/party-details-step";
import { ProjectsStep } from "./wizard-steps/projects-step";
import { ActivitiesStep } from "./wizard-steps/activities-step";
import { DocumentsStep } from "./wizard-steps/documents-step";
import { ReviewStep } from "./wizard-steps/review-step";
import { Alert, AlertDescription } from "./ui/alert";
import { Loader2, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAutoSave, useStepNavigationWithAutoSave } from "@/hooks/use-auto-save";
import { validate<PERSON><PERSON> } from "@/lib/validations/mou-application";
import { cn } from "@/lib/utils";
import { MouApplication } from "@/lib/services/mou-application.service";

interface MouApplicationWizardProps {
  onComplete?: (application: MouApplication) => void;
  onCancel?: () => void;
}

const steps = [
  { id: 1, title: "MoU Details", description: "Organization and duration details" },
  { id: 2, title: "Party Details", description: "Signatory parties information" },
  { id: 3, title: "Projects", description: "Project information and budgets" },
  { id: 4, title: "Activities", description: "Project activities and allocations" },
  { id: 5, title: "Documents", description: "Required document uploads" },
  { id: 6, title: "Review & Submit", description: "Final review and submission" },
];

const stepComponents = {
  1: MouDetailsStep,
  2: PartyDetailsStep,
  3: ProjectsStep,
  4: ActivitiesStep,
  5: DocumentsStep,
  6: ReviewStep,
};

export function MouApplicationWizard({ onComplete, onCancel }: MouApplicationWizardProps) {
  const { toast } = useToast();
  const [validationErrors, setValidationErrors] = useState<any>({});
  const { data, getCompletedSteps, setSubmitting, isSubmitting } = useMouApplicationStore();

  // Use auto-save and step navigation hooks
  const { lastSaved } = useAutoSave({
    interval: 30000, // 30 seconds
    enabled: true,
    onSave: () => {
      toast({
        title: "Draft saved",
        description: "Your progress has been automatically saved.",
        duration: 2000,
      });
    },
    onError: (error) => {
      toast({
        title: "Auto-save failed",
        description: "Unable to save your progress. Please try again.",
        variant: "destructive",
        duration: 3000,
      });
    }
  });

  const {
    currentStep,
    goToStep,
    goToNextStep,
    goToPreviousStep,
    canGoNext,
    canGoPrevious
  } = useStepNavigationWithAutoSave();

  const completedSteps = getCompletedSteps();
  const CurrentStepComponent = stepComponents[currentStep as keyof typeof stepComponents];

  // Validate current step before allowing navigation
  const validateCurrentStep = () => {
    const validation = validateStep(currentStep, data);
    setValidationErrors(validation.errors);
    return validation.isValid;
  };

  const handleNext = async () => {
    if (validateCurrentStep()) {
      if (currentStep === 6) {
        // Final submission
        await handleSubmit();
      } else {
        await goToNextStep();
      }
    } else {
      toast({
        title: "Validation Error",
        description: "Please fix the errors before proceeding.",
        variant: "destructive",
      });
    }
  };

  const handlePrevious = async () => {
    await goToPreviousStep();
  };

  const handleStepClick = async (stepId: number) => {
    // Allow navigation to completed steps or the next immediate step
    if (completedSteps.includes(stepId) || stepId <= Math.max(...completedSteps) + 1) {
      await goToStep(stepId);
    }
  };

  const handleSubmit = async () => {
    try {
      setSubmitting(true);

      // Validate all steps
      let allValid = true;
      for (let step = 1; step <= 5; step++) {
        const validation = validateStep(step, data);
        if (!validation.isValid) {
          allValid = false;
          break;
        }
      }

      if (!allValid) {
        toast({
          title: "Validation Error",
          description: "Please complete all required fields before submitting.",
          variant: "destructive",
        });
        return;
      }

      // Simulate API submission
      await new Promise(resolve => setTimeout(resolve, 2000));

      toast({
        title: "Application Submitted",
        description: "Your MoU application has been successfully submitted.",
      });

      // Create mock application object for callback
      const mockApplication: MouApplication = {
        id: data.id,
        mouApplicationId: data.id,
        mouId: "mock-mou-id",
        status: "SUBMITTED" as any,
        currentStep: 6,
        completionPercentage: 100,
        lastAutoSave: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        deleted: false
      };

      onComplete?.(mockApplication);
    } catch (error) {
      toast({
        title: "Submission Failed",
        description: "Failed to submit your application. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Progress Indicator */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h1 className="text-3xl font-bold text-primary">New MoU Application</h1>
              <div className="text-right">
                <div className="text-sm text-muted-foreground">
                  Step {currentStep} of {steps.length}
                </div>
                {lastSaved && (
                  <div className="text-xs text-muted-foreground flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    Last saved: {lastSaved.toLocaleTimeString()}
                  </div>
                )}
              </div>
            </div>

            <ProgressIndicator
              steps={steps}
              currentStep={currentStep}
              completedSteps={completedSteps}
              onStepClick={handleStepClick}
              className="mt-6"
            />
          </div>
        </CardContent>
      </Card>

      {/* Step Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
              {currentStep}
            </span>
            {steps[currentStep - 1].title}
          </CardTitle>
          <p className="text-muted-foreground">{steps[currentStep - 1].description}</p>
        </CardHeader>
        <CardContent className="p-6">
          {/* Validation Errors */}
          {Object.keys(validationErrors).length > 0 && (
            <Alert variant="destructive" className="mb-6">
              <AlertDescription>
                Please fix the following errors:
                <ul className="list-disc list-inside mt-2">
                  {Object.entries(validationErrors).map(([field, error]) => (
                    <li key={field}>{error as string}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* Step Component */}
          <CurrentStepComponent />
        </CardContent>
      </Card>

      {/* Navigation */}
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-between items-center">
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={!canGoPrevious || isSubmitting}
              >
                Previous
              </Button>
              {onCancel && (
                <Button
                  variant="ghost"
                  onClick={onCancel}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
              )}
            </div>

            <Button
              onClick={handleNext}
              disabled={!canGoNext && currentStep !== 6}
              className={cn(
                currentStep === 6 && "bg-green-600 hover:bg-green-700"
              )}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Submitting...
                </>
              ) : currentStep === 6 ? (
                "Submit Application"
              ) : (
                "Next"
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
