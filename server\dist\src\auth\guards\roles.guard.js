"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Auth = exports.PermissionGuard = exports.RolesGuard = exports.RequiresPermission = exports.RequiresResource = exports.Roles = exports.PERMISSION_KEY = exports.RESOURCE_KEY = exports.ROLES_KEY = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const client_1 = require("@prisma/client");
const roles_enum_1 = require("../../common/enums/roles.enum");
function mapUserRoleToPermissionRoles(userRole) {
    switch (userRole) {
        case client_1.UserRole.ADMIN:
            return [roles_enum_1.BaseRole.ADMIN];
        case client_1.UserRole.PARTNER:
            return [roles_enum_1.BaseRole.PARTNER];
        case client_1.UserRole.PARTNER_COORDINATOR:
            return [roles_enum_1.ReviewerRole.PARTNER_COORDINATOR];
        case client_1.UserRole.TECHNICAL_EXPERT:
            return [roles_enum_1.ReviewerRole.TECHNICAL_EXPERT];
        case client_1.UserRole.LEGAL_OFFICER:
            return [roles_enum_1.ReviewerRole.LEGAL_OFFICER, roles_enum_1.ApproverRole.LEGAL_OFFICER];
        case client_1.UserRole.HEAD_OF_DEPARTMENT:
            return [roles_enum_1.ApproverRole.HEAD_OF_DEPARTMENT];
        case client_1.UserRole.PermanentSecretary:
            return [roles_enum_1.ApproverRole.PERMANENT_SECRETARY];
        case client_1.UserRole.MINISTER:
            return [roles_enum_1.ApproverRole.MINISTER];
        default:
            return [roles_enum_1.BaseRole.STAFF];
    }
}
exports.ROLES_KEY = 'roles';
exports.RESOURCE_KEY = 'resource';
exports.PERMISSION_KEY = 'permission';
const Roles = (...roles) => (0, common_1.SetMetadata)(exports.ROLES_KEY, roles);
exports.Roles = Roles;
const RequiresResource = (resource) => (0, common_1.SetMetadata)(exports.RESOURCE_KEY, resource);
exports.RequiresResource = RequiresResource;
const RequiresPermission = (level) => (0, common_1.SetMetadata)(exports.PERMISSION_KEY, level);
exports.RequiresPermission = RequiresPermission;
let RolesGuard = class RolesGuard {
    constructor(reflector) {
        this.reflector = reflector;
    }
    canActivate(context) {
        const requiredRoles = this.reflector.getAllAndOverride(exports.ROLES_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        const requiredResource = this.reflector.getAllAndOverride(exports.RESOURCE_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        const requiredPermission = this.reflector.getAllAndOverride(exports.PERMISSION_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        if (!requiredRoles && !requiredResource) {
            return true;
        }
        const { user } = context.switchToHttp().getRequest();
        if (!user) {
            return false;
        }
        const userPermissionRoles = mapUserRoleToPermissionRoles(user.role);
        if (requiredRoles) {
            const hasRequiredRole = requiredRoles.includes(user.role);
            if (!hasRequiredRole) {
                return false;
            }
        }
        if (requiredResource && requiredPermission) {
            return (0, roles_enum_1.hasPermission)(userPermissionRoles, requiredResource, requiredPermission);
        }
        return true;
    }
};
exports.RolesGuard = RolesGuard;
exports.RolesGuard = RolesGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector])
], RolesGuard);
let PermissionGuard = class PermissionGuard {
    constructor(reflector) {
        this.reflector = reflector;
    }
    canActivate(context) {
        const resource = this.reflector.getAllAndOverride(exports.RESOURCE_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        const requiredPermission = this.reflector.getAllAndOverride(exports.PERMISSION_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        if (!resource || !requiredPermission) {
            return true;
        }
        const { user } = context.switchToHttp().getRequest();
        if (!user) {
            return false;
        }
        const userPermissionRoles = mapUserRoleToPermissionRoles(user.role);
        return (0, roles_enum_1.hasPermission)(userPermissionRoles, resource, requiredPermission);
    }
};
exports.PermissionGuard = PermissionGuard;
exports.PermissionGuard = PermissionGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector])
], PermissionGuard);
const Auth = (roles = [], resource, permission) => {
    return (0, common_1.applyDecorators)((0, common_1.SetMetadata)(exports.ROLES_KEY, roles), ...(resource ? [(0, common_1.SetMetadata)(exports.RESOURCE_KEY, resource)] : []), ...(permission ? [(0, common_1.SetMetadata)(exports.PERMISSION_KEY, permission)] : []));
};
exports.Auth = Auth;
//# sourceMappingURL=roles.guard.js.map