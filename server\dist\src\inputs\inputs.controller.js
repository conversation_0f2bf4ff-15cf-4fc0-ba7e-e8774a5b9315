"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InputsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const inputs_service_1 = require("./inputs.service");
const create_input_dto_1 = require("./dto/create-input.dto");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
const roles_guard_1 = require("../auth/guard/roles.guard");
const roles_decorator_1 = require("../auth/decorator/roles.decorator");
const dto_1 = require("../auth/dto");
let InputsController = class InputsController {
    constructor(inputsService) {
        this.inputsService = inputsService;
    }
    async createInput(createInputDto) {
        return this.inputsService.createInput(createInputDto);
    }
    async findAllInputs(page, limit, inputSubclassId) {
        const pageNum = page ? parseInt(page, 10) : 1;
        const limitNum = limit ? parseInt(limit, 10) : 10;
        const subclassIdNum = inputSubclassId ? parseInt(inputSubclassId, 10) : undefined;
        return this.inputsService.findAllInputs(pageNum, limitNum, subclassIdNum);
    }
    async getInputsBySubclass(inputSubclassId) {
        return this.inputsService.getInputsBySubclass(inputSubclassId);
    }
    async findOneInput(id) {
        return this.inputsService.findOneInput(id);
    }
    async updateInput(id, updateInputDto) {
        return this.inputsService.updateInput(id, updateInputDto);
    }
    async removeInput(id) {
        return this.inputsService.removeInput(id);
    }
    async createInputSubclass(createInputSubclassDto) {
        return this.inputsService.createInputSubclass(createInputSubclassDto);
    }
    async findAllInputSubclasses() {
        return this.inputsService.findAllInputSubclasses();
    }
    async findOneInputSubclass(id) {
        return this.inputsService.findOneInputSubclass(id);
    }
    async updateInputSubclass(id, updateInputSubclassDto) {
        return this.inputsService.updateInputSubclass(id, updateInputSubclassDto);
    }
    async removeInputSubclass(id) {
        return this.inputsService.removeInputSubclass(id);
    }
};
exports.InputsController = InputsController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new input' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Input created successfully',
        type: create_input_dto_1.InputWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Input subclass not found' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_input_dto_1.CreateInputDto]),
    __metadata("design:returntype", Promise)
], InputsController.prototype, "createInput", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get all inputs with pagination' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10)' }),
    (0, swagger_1.ApiQuery)({ name: 'inputSubclassId', required: false, type: Number, description: 'Filter by input subclass ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of inputs',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/InputWithRelationsDto' },
                },
                meta: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        totalPages: { type: 'number' },
                    },
                },
            },
        },
    }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('inputSubclassId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], InputsController.prototype, "findAllInputs", null);
__decorate([
    (0, common_1.Get)('by-subclass/:inputSubclassId'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get inputs by subclass ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of inputs for the subclass',
        type: [create_input_dto_1.InputWithRelationsDto],
    }),
    __param(0, (0, common_1.Param)('inputSubclassId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], InputsController.prototype, "getInputsBySubclass", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get an input by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Input details',
        type: create_input_dto_1.InputWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Input not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], InputsController.prototype, "findOneInput", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Update an input' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Input updated successfully',
        type: create_input_dto_1.InputWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Input not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, create_input_dto_1.UpdateInputDto]),
    __metadata("design:returntype", Promise)
], InputsController.prototype, "updateInput", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Delete an input (soft delete)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Input deleted successfully',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Input not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Input is being used by activities' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], InputsController.prototype, "removeInput", null);
__decorate([
    (0, common_1.Post)('subclasses'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new input subclass' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Input subclass created successfully',
        type: create_input_dto_1.InputSubclassWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Subclass ID already exists' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_input_dto_1.CreateInputSubclassDto]),
    __metadata("design:returntype", Promise)
], InputsController.prototype, "createInputSubclass", null);
__decorate([
    (0, common_1.Get)('subclasses'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get all input subclasses' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of input subclasses',
        type: [create_input_dto_1.InputSubclassWithRelationsDto],
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], InputsController.prototype, "findAllInputSubclasses", null);
__decorate([
    (0, common_1.Get)('subclasses/:id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.PARTNER, dto_1.UserRole.ADMIN, dto_1.UserRole.COORDINATOR, dto_1.UserRole.LEGAL, dto_1.UserRole.TECHNICAL_EXPERT, dto_1.UserRole.HOD, dto_1.UserRole.PS, dto_1.UserRole.MINISTER),
    (0, swagger_1.ApiOperation)({ summary: 'Get an input subclass by ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Input subclass details',
        type: create_input_dto_1.InputSubclassWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Input subclass not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], InputsController.prototype, "findOneInputSubclass", null);
__decorate([
    (0, common_1.Patch)('subclasses/:id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Update an input subclass' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Input subclass updated successfully',
        type: create_input_dto_1.InputSubclassWithRelationsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Input subclass not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, create_input_dto_1.UpdateInputSubclassDto]),
    __metadata("design:returntype", Promise)
], InputsController.prototype, "updateInputSubclass", null);
__decorate([
    (0, common_1.Delete)('subclasses/:id'),
    (0, roles_decorator_1.Roles)(dto_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Delete an input subclass (soft delete)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Input subclass deleted successfully',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Input subclass not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Input subclass is being used by inputs' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], InputsController.prototype, "removeInputSubclass", null);
exports.InputsController = InputsController = __decorate([
    (0, swagger_1.ApiTags)('Inputs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('inputs'),
    __metadata("design:paramtypes", [inputs_service_1.InputsService])
], InputsController);
//# sourceMappingURL=inputs.controller.js.map