"use client"

import { useEffect, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { Loader2, CheckCircle, XCircle, Mail } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import Link from "next/link"
import Image from "next/image"

export default function VerifyEmailPage() {
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)
  const [message, setMessage] = useState("")
  const { verifyAccount } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    const token = searchParams.get("token")
    const urlMessage = searchParams.get("message")
    
    if (urlMessage) {
      setMessage(urlMessage)
    }

    if (token) {
      setIsVerifying(true)
      const verify = async () => {
        try {
          await verifyAccount(token)
          setSuccess(true)
          setMessage("Your email has been successfully verified! You can now log in to access your partner dashboard.")
          // Redirect to login after 5 seconds
          setTimeout(() => {
            router.push("/login?message=Email verified successfully. Please log in to continue.")
          }, 5000)
        } catch (err: any) {
          setError(err.response?.data?.message || "Invalid or expired verification token")
        } finally {
          setIsVerifying(false)
        }
      }
      verify()
    }
  }, [searchParams, verifyAccount, router])

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-cyan-100">
            <Image src={'/logo.png'} alt="MoU Management System" width={48} height={48} />
          </div>
          <CardTitle className="text-2xl font-bold">Email Verification</CardTitle>
          <CardDescription>
            Ministry of Health MoU Management System
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {isVerifying ? (
            <div className="flex flex-col items-center space-y-4">
              <Loader2 className="h-16 w-16 animate-spin text-cyan-600" />
              <p className="text-center text-muted-foreground">Verifying your email address...</p>
            </div>
          ) : success ? (
            <div className="flex flex-col items-center space-y-4">
              <CheckCircle className="h-16 w-16 text-green-500" />
              <Alert className="bg-green-50 border-green-200">
                <AlertDescription className="text-green-800">
                  {message}
                </AlertDescription>
              </Alert>
              <p className="text-sm text-muted-foreground text-center">
                You will be redirected to the login page automatically, or click the button below.
              </p>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center space-y-4">
              <XCircle className="h-16 w-16 text-red-500" />
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            </div>
          ) : (
            <div className="flex flex-col items-center space-y-4">
              <Mail className="h-16 w-16 text-cyan-600" />
              <Alert className="bg-cyan-50 border-cyan-200">
                <AlertDescription className="text-cyan-800">
                  {message || "Please check your email for verification instructions."}
                </AlertDescription>
              </Alert>
              <p className="text-sm text-muted-foreground text-center">
                Click the verification link in your email to activate your account.
              </p>
            </div>
          )}
        </CardContent>
        <div className="px-6 pb-6">
          <div className="flex w-full justify-center space-x-2">
            <Button asChild disabled={isVerifying}>
              <Link href="/login">Go to Login</Link>
            </Button>
            {!success && !isVerifying && (
              <Button variant="outline" asChild>
                <Link href="/signup">Back to Signup</Link>
              </Button>
            )}
          </div>
        </div>
      </Card>
    </div>
  )
}
