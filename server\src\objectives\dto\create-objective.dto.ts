import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class CreateObjectiveDto {
  @ApiProperty({
    description: 'Objective name',
    example: 'Improve Primary Healthcare Access',
  })
  @IsString()
  @IsNotEmpty()
  name: string;
}

export class UpdateObjectiveDto {
  @ApiProperty({
    description: 'Objective name',
    example: 'Enhanced Primary Healthcare Access',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  name?: string;
}

export class ObjectiveResponseDto {
  @ApiProperty({ description: 'Objective ID', example: 1 })
  id: number;

  @ApiProperty({ description: 'Objective name', example: 'Improve Primary Healthcare Access' })
  name: string;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;

  @ApiProperty({ description: 'Soft delete flag' })
  deleted: boolean;
}
