"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Plus, Search, TreePine } from "lucide-react"
import { TreeView, type TreeNode } from "@/components/ui/tree-view"
import { masterDataService, type InputCategory, type InputCategoryHierarchy } from "@/lib/services/master-data.service"

export default function InputCategoriesPage() {
  const [categories, setCategories] = useState<InputCategory[]>([])
  const [treeData, setTreeData] = useState<InputCategoryHierarchy[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(true)
  const [formLoading, setFormLoading] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<InputCategory | null>(null)
  const [parentCategory, setParentCategory] = useState<InputCategory | null>(null)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  // Form state
  const [categoryName, setCategoryName] = useState("")
  const [description, setDescription] = useState("")
  const [selectedParentId, setSelectedParentId] = useState<string>("none")

  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = async () => {
    try {
      setLoading(true)
      const [categoriesData, treeData] = await Promise.all([
        masterDataService.getInputCategories(),
        masterDataService.getInputCategoriesTree(),
      ])
      setCategories(categoriesData)
      setTreeData(treeData)
    } catch (error) {
      console.error("Failed to load input categories:", error)
      setError("Failed to load input categories")
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setCategoryName("")
    setDescription("")
    setSelectedParentId("none")
    setEditingCategory(null)
    setParentCategory(null)
    setError("")
    setSuccess("")
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setFormLoading(true)
    setError("")

    try {
      const categoryData = {
        categoryName,
        description: description || undefined,
        parentId: selectedParentId && selectedParentId !== "none" ? parseInt(selectedParentId) : undefined,
      }

      if (editingCategory) {
        await masterDataService.updateInputCategory(editingCategory.id, categoryData)
        setSuccess("Input category updated successfully")
      } else {
        await masterDataService.createInputCategory(categoryData)
        setSuccess("Input category created successfully")
      }

      await loadCategories()
      setDialogOpen(false)
      resetForm()
    } catch (error: any) {
      setError(error.response?.data?.message || "Failed to save input category")
    } finally {
      setFormLoading(false)
    }
  }

  const handleEdit = (node: TreeNode) => {
    const category = categories.find(c => c.id === node.id)
    if (category) {
      setEditingCategory(category)
      setCategoryName(category.categoryName)
      setDescription(category.description || "")
      setSelectedParentId(category.parentId ? category.parentId.toString() : "none")
      setDialogOpen(true)
    }
  }

  const handleDelete = async (node: TreeNode) => {
    if (confirm("Are you sure you want to delete this input category?")) {
      try {
        await masterDataService.deleteInputCategory(node.id)
        setSuccess("Input category deleted successfully")
        await loadCategories()
      } catch (error: any) {
        setError(error.response?.data?.message || "Failed to delete input category")
      }
    }
  }

  const handleAddChild = (parentNode: TreeNode) => {
    const parent = categories.find(c => c.id === parentNode.id)
    if (parent) {
      setParentCategory(parent)
      setSelectedParentId(parent.id.toString())
      setDialogOpen(true)
    }
  }

  const openCreateDialog = () => {
    resetForm()
    setDialogOpen(true)
  }

  // Convert categories to tree nodes
  const convertToTreeNodes = (categories: InputCategoryHierarchy[]): TreeNode[] => {
    return categories.map(category => ({
      id: category.id,
      name: category.categoryName,
      description: category.description,
      parentId: category.parentId,
      children: category.children ? convertToTreeNodes(category.children) : [],
    }))
  }

  const treeNodes = convertToTreeNodes(treeData)

  // Filter categories for parent selection (exclude current category and its descendants)
  const getSelectableParents = (): InputCategory[] => {
    if (!editingCategory) return categories.filter(c => !c.parentId) // Only root categories for new items
    
    const excludeIds = new Set<number>()
    
    // Add current category
    excludeIds.add(editingCategory.id)
    
    // Add all descendants
    const addDescendants = (parentId: number) => {
      categories.filter(c => c.parentId === parentId).forEach(child => {
        excludeIds.add(child.id)
        addDescendants(child.id)
      })
    }
    addDescendants(editingCategory.id)
    
    return categories.filter(c => !excludeIds.has(c.id))
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Input Categories</h2>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={openCreateDialog}>
              <Plus className="mr-2 h-4 w-4" /> Add Input Category
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>
                {editingCategory ? "Edit Input Category" : parentCategory ? `Add Child to "${parentCategory.categoryName}"` : "Create New Input Category"}
              </DialogTitle>
              <DialogDescription>
                {editingCategory ? "Update input category information." : "Add a new input category to the system."}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4 py-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <div className="space-y-2">
                  <Label htmlFor="categoryName">Category Name *</Label>
                  <Input
                    id="categoryName"
                    value={categoryName}
                    onChange={(e) => setCategoryName(e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={3}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="parentId">Parent Category</Label>
                  <Select value={selectedParentId} onValueChange={setSelectedParentId} disabled={!!parentCategory}>
                    <SelectTrigger>
                      <SelectValue placeholder={parentCategory ? parentCategory.categoryName : "Select parent category (optional)"} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No Parent (Root Category)</SelectItem>
                      {getSelectableParents().map((category) => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.categoryName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" disabled={formLoading}>
                  {formLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {editingCategory ? "Updating..." : "Creating..."}
                    </>
                  ) : editingCategory ? (
                    "Update Category"
                  ) : (
                    "Create Category"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {success && (
        <Alert className="bg-green-50 text-green-700 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {error && !dialogOpen && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search input categories..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TreePine className="h-5 w-5" />
              Category Hierarchy
            </CardTitle>
            <CardDescription>
              Manage input categories in a hierarchical structure. You can create parent-child relationships.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <TreeView
              data={treeNodes}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onAddChild={handleAddChild}
              nameKey="name"
              className="border rounded-lg p-4 bg-gray-50"
            />
          </CardContent>
        </Card>
      )}
    </div>
  )
}
