import { useCallback } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { useFieldBlurAutoSave } from '@/hooks/use-mou-form'
import { useMouStore } from '@/store/mou-store'
import { documentSchema } from '@/lib/validations/mou'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { FileText, Upload, X, Check } from 'lucide-react'

const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
const ACCEPTED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'image/jpeg',
  'image/png',
  'image/gif'
]

const formatDocumentType = (type: string) => {
  return type.replace(/([A-Z])/g, ' $1').trim()
}

const getFileIcon = (file: File | null) => {
  if (!file) return null
  
  if (file.type.startsWith('image/')) {
    return URL.createObjectURL(file)
  }
  
  return null
}

export function DocumentsStep() {
  const { onBlur } = useFieldBlurAutoSave()
  const documents = useMouStore(state => state.documents)
  const updateDocument = useMouStore(
    state => state.updateDocument as (type: typeof documents[number]['type'], file: File | null) => void
  )

  const {
    formState: { errors },
    trigger
  } = useForm({
    resolver: zodResolver(z.object({
      documents: z.array(documentSchema)
    })),
    defaultValues: {
      documents
    }
  })

  const handleFileChange = useCallback(async (
    type: typeof documents[number]['type'],
    file: File | null
  ) => {
    if (!file) return

    try {
      // Validate file size and type
      if (file.size > MAX_FILE_SIZE) {
        throw new Error('File size must be less than 10MB')
      }
      if (!ACCEPTED_FILE_TYPES.includes(file.type)) {
        throw new Error('Invalid file type')
      }

      // Simulate file upload progress
      updateDocument(type, file)
      let progress = 0
      const interval = setInterval(() => {
        progress += 10
        if (progress <= 100) {
          useMouStore.setState({
            documents: documents.map(d =>
              d.type === type ? { ...d, uploadProgress: progress } : d
            )
          })
        } else {
          clearInterval(interval)
        }
      }, 100)

      onBlur()
      await trigger('documents')
    } catch (error) {
      if (error instanceof Error) {
        // Reset the file and show error
        updateDocument(type, null)
      }
    }
  }, [documents, updateDocument, onBlur, trigger])

  return (
    <div className="space-y-6">
      {errors.documents?.root && (
        <Alert variant="destructive">
          <AlertDescription>
            {errors.documents.root.message}
          </AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6">
        {documents.map((doc) => (
          <Card key={doc.id} className="p-6">
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <h3 className="font-semibold">{formatDocumentType(doc.type)}</h3>
                <p className="text-sm text-muted-foreground">
                  Accepts PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, GIF (max 10MB)
                </p>
              </div>
              {doc.file && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleFileChange(doc.type, null)}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            {doc.file ? (
              <div className="mt-4">
                <div className="flex items-center gap-4">
                  {getFileIcon(doc.file) ? (
                    <img
                      src={getFileIcon(doc.file) ?? undefined}
                      alt={doc.file.name}
                      className="h-16 w-16 object-cover rounded"
                    />
                  ) : (
                    <FileText className="h-16 w-16 text-muted-foreground" />
                  )}
                  <div className="flex-1 space-y-1">
                    <p className="text-sm font-medium">{doc.file.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {(doc.file.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                  <Check className="h-5 w-5 text-green-500" />
                </div>
                {doc.uploadProgress < 100 && (
                  <Progress value={doc.uploadProgress} className="mt-4" />
                )}
              </div>
            ) : (
              <div className="mt-4">
                <div className="relative">
                  <input
                    type="file"
                    className="hidden"
                    id={`file-${doc.type}`}
                    accept={ACCEPTED_FILE_TYPES.join(',')}
                    onChange={(e) => {
                      const file = e.target.files?.[0] || null
                      handleFileChange(doc.type, file)
                    }}
                  />
                  <label
                    htmlFor={`file-${doc.type}`}
                    className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer hover:bg-muted/50"
                  >
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                      <p className="text-sm text-muted-foreground">
                        Click to upload or drag and drop
                      </p>
                    </div>
                  </label>
                </div>
              </div>
            )}

            {errors.documents?.[documents.indexOf(doc)] && (
              <Alert variant="destructive" className="mt-4">
                <AlertDescription>
                  {errors.documents[documents.indexOf(doc)]?.message}
                </AlertDescription>
              </Alert>
            )}
          </Card>
        ))}
      </div>
    </div>
  )
}
