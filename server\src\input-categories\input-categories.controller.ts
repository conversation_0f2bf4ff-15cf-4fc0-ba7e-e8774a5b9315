import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { InputCategoriesService } from './input-categories.service';
import { CreateInputCategoryDto, UpdateInputCategoryDto, InputCategoryResponseDto } from './dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { Public } from '../auth/decorator/public.decorator';

@ApiTags('input-categories')
@Controller('input-categories')
export class InputCategoriesController {
  constructor(private readonly inputCategoriesService: InputCategoriesService) {}

  @Post()
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new input category (Admin only)' })
  @ApiResponse({ status: 201, description: 'Input category created successfully', type: InputCategoryResponseDto })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid parent category' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 409, description: 'Input category with name already exists' })
  async create(@Body() createInputCategoryDto: CreateInputCategoryDto, @Request() req: any) {
    return this.inputCategoriesService.create(createInputCategoryDto, req.user.sub);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all input categories with hierarchy' })
  @ApiResponse({ status: 200, description: 'Returns list of input categories', type: [InputCategoryResponseDto] })
  async findAll() {
    return this.inputCategoriesService.findAll();
  }

  @Get('tree')
  @Public()
  @ApiOperation({ summary: 'Get input categories as tree structure (root categories with children)' })
  @ApiResponse({ status: 200, description: 'Returns tree structure of input categories', type: [InputCategoryResponseDto] })
  async findTree() {
    return this.inputCategoriesService.findTree();
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get input category by ID with hierarchy' })
  @ApiResponse({ status: 200, description: 'Returns input category details', type: InputCategoryResponseDto })
  @ApiResponse({ status: 404, description: 'Input category not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.inputCategoriesService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update input category (Admin only)' })
  @ApiResponse({ status: 200, description: 'Input category updated successfully', type: InputCategoryResponseDto })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid parent category or circular reference' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Input category not found' })
  @ApiResponse({ status: 409, description: 'Input category with name already exists' })
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateInputCategoryDto: UpdateInputCategoryDto, @Request() req: any) {
    return this.inputCategoriesService.update(id, updateInputCategoryDto, req.user.sub);
  }

  @Delete(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete input category (Admin only)' })
  @ApiResponse({ status: 200, description: 'Input category deleted successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - Category has children' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Input category not found' })
  async remove(@Param('id', ParseIntPipe) id: number, @Request() req: any) {
    return this.inputCategoriesService.remove(id, req.user.sub);
  }
}
