"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AuthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const jwt_1 = require("@nestjs/jwt");
const prisma_service_1 = require("../prisma/prisma.service");
const email_service_1 = require("../email/email.service");
const bcrypt = require("bcryptjs");
const uuid_1 = require("uuid");
let AuthService = AuthService_1 = class AuthService {
    constructor(jwtService, prisma, emailService, configService) {
        this.jwtService = jwtService;
        this.prisma = prisma;
        this.emailService = emailService;
        this.configService = configService;
        this.logger = new common_1.Logger(AuthService_1.name);
        this.SALT_ROUNDS = 10;
        this.EMAIL_VERIFICATION_EXPIRY = 24 * 60 * 60 * 1000;
        this.PASSWORD_RESET_EXPIRY = 60 * 60 * 1000;
        this.REFRESH_TOKEN_EXPIRY = 7 * 24 * 60 * 60 * 1000;
        this.INVITATION_EXPIRY = 7 * 24 * 60 * 60 * 1000;
    }
    async generateAccessToken(user) {
        const payload = {
            sub: user.id,
            email: user.email,
            role: user.role
        };
        return this.jwtService.sign(payload, {
            secret: this.configService.get('JWT_SECRET'),
            expiresIn: '15m'
        });
    }
    async generateRefreshToken(user) {
        const refreshToken = (0, uuid_1.v4)();
        try {
            await this.prisma.user.update({
                where: { id: user.id },
                data: {
                    refreshToken
                }
            });
            return refreshToken;
        }
        catch (error) {
            this.logger.error(`Failed to generate refresh token for user ${user.id}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to generate refresh token');
        }
    }
    async getUser(id) {
        try {
            const user = await this.prisma.user.findUnique({ where: { id: parseInt(id, 10) } });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            return user;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Failed to get user with ID ${id}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to get user');
        }
    }
    async validateUser(email, password) {
        try {
            const user = await this.prisma.user.findUnique({ where: { email } });
            if (!user) {
                return null;
            }
            const isPasswordValid = await bcrypt.compare(password, user.password);
            if (!isPasswordValid) {
                return null;
            }
            return user;
        }
        catch (error) {
            this.logger.error(`Failed to validate user with email ${email}`, error.stack);
            return null;
        }
    }
    async register(registerDto) {
        const { firstName, lastName, email, password, organization } = registerDto;
        try {
            const exists = await this.prisma.user.findUnique({ where: { email } });
            if (exists) {
                throw new common_1.ConflictException('User with this email already exists');
            }
            this.validateOrganizationData(organization);
            const existingOrg = await this.prisma.organization.findFirst({
                where: {
                    OR: [
                        { organizationRgbNumber: organization.organizationRgbNumber },
                        { organizationEmail: organization.organizationEmail }
                    ],
                    deleted: false
                }
            });
            if (existingOrg) {
                throw new common_1.ConflictException('Organization with this registration number or email already exists');
            }
            const orgType = await this.prisma.organizationType.findUnique({
                where: { id: organization.organizationTypeId }
            });
            if (!orgType) {
                throw new common_1.NotFoundException('Organization type not found');
            }
            const verificationToken = (0, uuid_1.v4)();
            const verificationTokenExpiryTime = new Date(Date.now() + this.EMAIL_VERIFICATION_EXPIRY);
            const hashedPassword = await bcrypt.hash(password, this.SALT_ROUNDS);
            const result = await this.prisma.$transaction(async (prisma) => {
                const newOrg = await prisma.organization.create({
                    data: {
                        organizationName: organization.organizationName,
                        organizationPhoneNumber: organization.organizationPhoneNumber,
                        organizationEmail: organization.organizationEmail,
                        organizationWebsite: organization.organizationWebsite,
                        homeCountryRepresentative: organization.homeCountryRepresentative,
                        rwandaRepresentative: organization.rwandaRepresentative,
                        organizationRgbNumber: organization.organizationRgbNumber,
                        organizationTypeId: organization.organizationTypeId
                    }
                });
                for (const addressData of organization.addresses) {
                    await prisma.address.create({
                        data: {
                            ...addressData,
                            organizationId: newOrg.id
                        }
                    });
                }
                const newUser = await prisma.user.create({
                    data: {
                        firstName,
                        lastName,
                        email,
                        password: hashedPassword,
                        role: 'PARTNER',
                        organizationId: newOrg.id,
                        verificationToken,
                        verificationTokenExpiryTime,
                        emailVerified: false
                    },
                });
                await this.emailService.sendEmailVerificationEmail({
                    email: newUser.email,
                    firstName: newUser.firstName,
                    lastName: newUser.lastName,
                    verificationToken: verificationToken
                });
                return { user: newUser, organization: newOrg };
            });
            const accessToken = await this.generateAccessToken(result.user);
            const refreshToken = await this.generateRefreshToken(result.user);
            return {
                user: {
                    id: result.user.id,
                    email: result.user.email,
                    firstName: result.user.firstName,
                    lastName: result.user.lastName,
                    role: result.user.role,
                    emailVerified: result.user.emailVerified,
                    organizationId: result.user.organizationId
                },
                organization: {
                    id: result.organization.id,
                    organizationName: result.organization.organizationName,
                    organizationEmail: result.organization.organizationEmail
                },
                accessToken,
                refreshToken
            };
        }
        catch (error) {
            if (error instanceof common_1.ConflictException || error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to register partner with organization for email ${email}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to register partner with organization');
        }
    }
    async createUserByAdmin(createUserDto, adminUserId) {
        const { firstName, lastName, email, role, organizationId } = createUserDto;
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(adminUserId, 10) }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can create users');
            }
            const exists = await this.prisma.user.findUnique({ where: { email } });
            if (exists) {
                throw new common_1.ConflictException('User with this email already exists');
            }
            if (role === 'PARTNER') {
                throw new common_1.ForbiddenException('ADMIN users cannot create PARTNER users. PARTNER users must self-register with organization data.');
            }
            if (role === 'ADMIN' && organizationId) {
                throw new common_1.BadRequestException('ADMIN users cannot be associated with an organization');
            }
            if (organizationId) {
                const organization = await this.prisma.organization.findUnique({
                    where: { id: organizationId }
                });
                if (!organization) {
                    throw new common_1.NotFoundException('Organization not found');
                }
            }
            const tempPassword = (0, uuid_1.v4)().substring(0, 12);
            const hashedPassword = await bcrypt.hash(tempPassword, this.SALT_ROUNDS);
            const verificationToken = (0, uuid_1.v4)();
            const verificationTokenExpiryTime = new Date(Date.now() + 24 * 60 * 60 * 1000);
            const user = await this.prisma.user.create({
                data: {
                    firstName,
                    lastName,
                    email,
                    password: hashedPassword,
                    role: role,
                    organizationId: organizationId || null,
                    verificationToken,
                    verificationTokenExpiryTime,
                    emailVerified: false
                },
                include: {
                    organization: {
                        select: {
                            id: true,
                            organizationName: true
                        }
                    }
                }
            });
            await this.emailService.sendEmailVerificationEmail({
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                verificationToken: verificationToken,
                tempPassword: tempPassword
            });
            return {
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                role: user.role,
                emailVerified: user.emailVerified,
                organizationId: user.organizationId,
                organization: user.organization,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
                tempPassword
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException ||
                error instanceof common_1.ForbiddenException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to create user for email ${email}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to create user');
        }
    }
    validateOrganizationData(organization) {
        if (!organization.addresses || organization.addresses.length === 0) {
            throw new common_1.BadRequestException('Organization must have at least one address (either Rwanda or headquarters)');
        }
        if (organization.addresses.length > 2) {
            throw new common_1.BadRequestException('Organization cannot have more than 2 addresses');
        }
        const addressTypes = organization.addresses.map((addr) => addr.addressType);
        const uniqueTypes = new Set(addressTypes);
        if (addressTypes.length !== uniqueTypes.size) {
            throw new common_1.BadRequestException('Cannot have duplicate address types');
        }
        const validAddressTypes = ['RWANDA', 'HEADQUARTERS'];
        for (const address of organization.addresses) {
            if (!validAddressTypes.includes(address.addressType)) {
                throw new common_1.BadRequestException('Invalid address type. Must be either RWANDA or HEADQUARTERS');
            }
        }
        const rwandaAddress = organization.addresses.find((addr) => addr.addressType === 'RWANDA');
        if (rwandaAddress && (!rwandaAddress.province || !rwandaAddress.district)) {
            throw new common_1.BadRequestException('Rwanda address must include province and district');
        }
        const hasRwandaAddress = organization.addresses.some((addr) => addr.addressType === 'RWANDA');
        const hasHeadquartersAddress = organization.addresses.some((addr) => addr.addressType === 'HEADQUARTERS');
        if (!hasRwandaAddress && !hasHeadquartersAddress) {
            throw new common_1.BadRequestException('Organization must have at least one address (either Rwanda or headquarters)');
        }
    }
    async login(loginDto) {
        const { email, password } = loginDto;
        try {
            const user = await this.prisma.user.findUnique({ where: { email } });
            if (!user) {
                throw new common_1.UnauthorizedException('Invalid credentials');
            }
            const isValid = await bcrypt.compare(password, user.password);
            if (!isValid) {
                throw new common_1.UnauthorizedException('Invalid credentials');
            }
            const accessToken = await this.generateAccessToken(user);
            const refreshToken = await this.generateRefreshToken(user);
            return {
                user: {
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    role: user.role,
                    emailVerified: user.emailVerified
                },
                accessToken,
                refreshToken
            };
        }
        catch (error) {
            if (error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            this.logger.error(`Failed to login user with email ${email}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to login');
        }
    }
    async refreshToken(refreshTokenDto) {
        const { refreshToken } = refreshTokenDto;
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    refreshToken
                }
            });
            if (!user) {
                throw new common_1.UnauthorizedException('Invalid refresh token');
            }
            const accessToken = await this.generateAccessToken(user);
            const newRefreshToken = await this.generateRefreshToken(user);
            return {
                accessToken,
                refreshToken: newRefreshToken
            };
        }
        catch (error) {
            if (error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            this.logger.error('Failed to refresh token', error.stack);
            throw new common_1.InternalServerErrorException('Failed to refresh token');
        }
    }
    async verifyEmail(verifyEmailDto) {
        const { token } = verifyEmailDto;
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    verificationToken: token,
                    verificationTokenExpiryTime: { gt: new Date() }
                }
            });
            if (!user) {
                throw new common_1.BadRequestException('Invalid or expired verification token');
            }
            const updatedUser = await this.prisma.user.update({
                where: { id: user.id },
                data: {
                    emailVerified: true,
                    verificationToken: null,
                    verificationTokenExpiryTime: null,
                    verifiedAt: new Date()
                }
            });
            await this.emailService.sendWelcomeEmail({
                email: updatedUser.email,
                firstName: updatedUser.firstName,
                lastName: updatedUser.lastName
            });
            return { message: 'Email verified successfully. Welcome email sent!' };
        }
        catch (error) {
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to verify email with token ${token}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to verify email');
        }
    }
    async forgotPassword(forgotPasswordDto) {
        const { email } = forgotPasswordDto;
        try {
            const user = await this.prisma.user.findUnique({ where: { email } });
            if (!user) {
                return { message: 'If your email is registered, you will receive a password reset link' };
            }
            const passwordResetToken = (0, uuid_1.v4)();
            const passwordResetExpires = new Date(Date.now() + this.PASSWORD_RESET_EXPIRY);
            await this.prisma.user.update({
                where: { id: user.id },
                data: {
                    passwordResetToken,
                    passwordResetExpires
                }
            });
            await this.emailService.sendPasswordResetEmail({
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                resetToken: passwordResetToken
            });
            return { message: 'If your email is registered, you will receive a password reset link' };
        }
        catch (error) {
            this.logger.error(`Failed to process forgot password for email ${email}`, error.stack);
            return { message: 'If your email is registered, you will receive a password reset link' };
        }
    }
    async resetPassword(resetPasswordDto) {
        const { token, password } = resetPasswordDto;
        try {
            const user = await this.prisma.user.findFirst({
                where: {
                    passwordResetToken: token,
                    passwordResetExpires: { gt: new Date() }
                }
            });
            if (!user) {
                throw new common_1.BadRequestException('Invalid or expired reset token');
            }
            const hashedPassword = await bcrypt.hash(password, this.SALT_ROUNDS);
            await this.prisma.user.update({
                where: { id: user.id },
                data: {
                    password: hashedPassword,
                    passwordResetToken: null,
                    passwordResetExpires: null
                }
            });
            return { message: 'Password reset successfully' };
        }
        catch (error) {
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to reset password with token ${token}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to reset password');
        }
    }
    async inviteUser(inviteUserDto, inviterId) {
        const { email, role, organizationId } = inviteUserDto;
        try {
            const inviter = await this.prisma.user.findUnique({
                where: { id: parseInt(inviterId, 10) },
                include: { organization: true }
            });
            if (!inviter) {
                throw new common_1.NotFoundException('Inviter not found');
            }
            const inviterRole = String(inviter.role);
            if (inviterRole !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can invite users');
            }
            if (organizationId) {
                const organization = await this.prisma.organization.findUnique({ where: { id: parseInt(organizationId, 10) } });
                if (!organization) {
                    throw new common_1.NotFoundException(`Organization with ID ${organizationId} not found`);
                }
            }
            const existingUser = await this.prisma.user.findUnique({ where: { email } });
            if (existingUser) {
                throw new common_1.ConflictException('User with this email already exists');
            }
            const invitationToken = (0, uuid_1.v4)();
            const invitationExpires = new Date(Date.now() + this.INVITATION_EXPIRY);
            await this.prisma.$transaction(async (prisma) => {
                const placeholderUser = await prisma.user.create({
                    data: {
                        email,
                        firstName: 'Invited',
                        lastName: 'User',
                        password: await bcrypt.hash((0, uuid_1.v4)(), this.SALT_ROUNDS),
                        role: role,
                        organizationId: organizationId ? parseInt(organizationId, 10) : null,
                        invitationToken,
                        invitationExpires,
                        invitedBy: inviterId,
                        emailVerified: false
                    }
                });
                await this.emailService.sendInvitationEmail({
                    email,
                    invitationToken,
                    inviterName: `${inviter.firstName} ${inviter.lastName}`,
                    companyName: inviter.organization?.organizationName || 'Ministry of Health',
                    companyDescription: `Join the Ministry of Health MoU Management System as a ${role.toLowerCase()}`,
                    role: role.toLowerCase()
                });
                return placeholderUser;
            });
            return { message: `Invitation sent to ${email}` };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.ForbiddenException ||
                error instanceof common_1.ConflictException) {
                throw error;
            }
            this.logger.error(`Failed to invite user with email ${email}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to send invitation');
        }
    }
    async acceptInvitation(acceptInvitationDto) {
        const { token, firstName, lastName, password } = acceptInvitationDto;
        try {
            const invitation = await this.prisma.user.findFirst({
                where: {
                    invitationToken: token,
                    invitationExpires: { gt: new Date() }
                },
                include: { organization: true }
            });
            if (!invitation) {
                throw new common_1.BadRequestException('Invalid or expired invitation');
            }
            const hashedPassword = await bcrypt.hash(password, this.SALT_ROUNDS);
            const user = await this.prisma.user.update({
                where: { id: invitation.id },
                data: {
                    firstName,
                    lastName,
                    password: hashedPassword,
                    invitationToken: null,
                    invitationExpires: null,
                    emailVerified: true,
                    verifiedAt: new Date()
                }
            });
            await this.emailService.sendWelcomeEmail({
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName
            });
            const accessToken = await this.generateAccessToken(user);
            const refreshToken = await this.generateRefreshToken(user);
            return {
                user: {
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    role: user.role,
                    emailVerified: user.emailVerified,
                    organization: invitation.organization ? {
                        id: invitation.organization.id,
                        name: invitation.organization.organizationName
                    } : null
                },
                accessToken,
                refreshToken
            };
        }
        catch (error) {
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to accept invitation with token ${token}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to accept invitation');
        }
    }
    async resendVerificationEmail(userId) {
        try {
            const user = await this.prisma.user.findUnique({ where: { id: parseInt(userId, 10) } });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (user.emailVerified) {
                throw new common_1.BadRequestException('Email is already verified');
            }
            const verificationToken = (0, uuid_1.v4)();
            const verificationTokenExpiryTime = new Date(Date.now() + this.EMAIL_VERIFICATION_EXPIRY);
            await this.prisma.user.update({
                where: { id: user.id },
                data: {
                    verificationToken,
                    verificationTokenExpiryTime
                }
            });
            await this.emailService.sendEmailVerificationEmail({
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                verificationToken: verificationToken
            });
            return { message: 'Verification email sent' };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to resend verification email for user ${userId}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to resend verification email');
        }
    }
    async logout(userId) {
        try {
            await this.prisma.user.update({
                where: { id: parseInt(userId, 10) },
                data: {
                    refreshToken: null
                }
            });
            return { message: 'Logged out successfully' };
        }
        catch (error) {
            this.logger.error(`Failed to logout user ${userId}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to logout');
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = AuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        prisma_service_1.PrismaService,
        email_service_1.EmailService,
        config_1.ConfigService])
], AuthService);
//# sourceMappingURL=auth.service.js.map