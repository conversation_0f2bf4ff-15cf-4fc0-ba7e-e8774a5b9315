import { z } from 'zod'

const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
const ACCEPTED_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'image/jpeg',
  'image/png',
  'image/gif'
]

export const mouDetailsSchema = z.object({
  organizationName: z.string().min(1, 'Organization name is required'),
  mouDuration: z.number().min(1, 'Duration must be at least 1 year'),
  durationReason: z.string().optional().nullable()
}).refine(data => {
  if (data.mouDuration > 1 && !data.durationReason) {
    return false
  }
  return true
}, {
  message: "Reason is required when duration is more than 1 year",
  path: ["durationReason"]
})

// Schema for individual responsibility
export const responsibilitySchema = z.object({
  id: z.number().optional(), // For existing responsibilities
  name: z.string().min(1, 'Responsibility name is required')
})

// Schema for individual objective
export const objectiveSchema = z.object({
  id: z.number().optional(), // For existing objectives
  name: z.string().min(1, 'Objective name is required')
})

// Schema for goal
export const goalSchema = z.object({
  id: z.number().optional(), // For existing goals
  name: z.string().min(1, 'Goal name is required')
})

// Updated party schema with multiple responsibilities and objectives
export const partySchema = z.object({
  name: z.string().min(1, 'Party name is required'),
  signatoryName: z.string().min(1, 'Signatory name is required'),
  position: z.string().min(1, 'Position is required'),
  responsibilities: z.array(responsibilitySchema).min(1, 'At least one responsibility is required'),
  goal: goalSchema,
  objectives: z.array(objectiveSchema).min(1, 'At least one objective is required')
})

export const fiscalYearBudgetSchema = z.object({
  fiscalYear: z.string().min(1, 'Fiscal year is required'),
  budget: z.number().min(0, 'Budget must be non-negative')
})

export const projectGoalSchema = z.object({
  description: z.string().min(1, 'Goal description is required'),
  isOverallGoal: z.boolean()
})

export const projectSchema = z.object({
  name: z.string().min(1, 'Project name is required'),
  fundingSource: z.string().min(1, 'Funding source is required'),
  fundingUnit: z.string().min(1, 'Funding unit is required'),
  budgetType: z.string().min(1, 'Budget type is required'),
  currency: z.string().min(1, 'Currency is required'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().min(1, 'End date is required'),
  fiscalYearBudgets: z.array(fiscalYearBudgetSchema)
    .min(1, 'At least one fiscal year budget is required'),
  goals: z.array(projectGoalSchema)
    .min(1, 'At least one goal is required')
    .refine(
      goals => goals.filter(g => g.isOverallGoal).length === 1,
      'Exactly one overall goal is required'
    )
}).refine(
  data => new Date(data.startDate) < new Date(data.endDate),
  {
    message: "End date must be after start date",
    path: ["endDate"]
  }
)

export const budgetAllocationSchema = z.object({
  location: z.string().min(1, 'Location is required'),
  budget: z.number().min(0, 'Budget must be non-negative')
})

export const activitySchema = z.object({
  projectId: z.string().min(1, 'Project ID is required'),
  name: z.string().min(1, 'Activity name is required'),
  implementor: z.string().min(1, 'Implementor is required'),
  implementingUnit: z.string().min(1, 'Implementing unit is required'),
  fiscalYear: z.string().min(1, 'Fiscal year is required'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().min(1, 'End date is required'),
  domain: z.string().min(1, 'Domain is required'),
  subDomain: z.string().min(1, 'Sub-domain is required'),
  subDomainFunction: z.string().min(1, 'Function is required'),
  subFunction: z.string().min(1, 'Sub-function is required'),
  inputCategory: z.string().min(1, 'Input category is required'),
  activityInput: z.string().min(1, 'Activity input is required'),
  geographicLevel: z.enum(['Provinces', 'Central']),
  budgetAllocations: z.array(budgetAllocationSchema)
    .min(1, 'At least one budget allocation is required')
}).refine(
  data => new Date(data.startDate) < new Date(data.endDate),
  {
    message: "End date must be after start date",
    path: ["endDate"]
  }
)

export const documentSchema = z.object({
  file: z.instanceof(File).refine(
    file => file.size <= MAX_FILE_SIZE,
    'File size must be less than 10MB'
  ).refine(
    file => ACCEPTED_FILE_TYPES.includes(file.type),
    'Invalid file type'
  )
})

export const mouFormSchema = z.object({
  mouDetails: mouDetailsSchema,
  parties: z.array(partySchema).min(1, 'At least one party is required'),
  projects: z.array(projectSchema).min(1, 'At least one project is required'),
  activities: z.array(activitySchema),
  documents: z.object({
    LongTermObjective: documentSchema,
    StrategicPlan: documentSchema,
    CapacityBuildingPlan: documentSchema,
    FundingSourceMemo: documentSchema
  })
})
