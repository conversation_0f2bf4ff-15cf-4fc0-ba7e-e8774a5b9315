import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsInt, IsNotEmpty, IsPositive, Min, Max } from 'class-validator';

export class CreatePartyDto {
  @ApiProperty({
    description: 'Unique party identifier',
    example: 'PARTY-001-2024',
  })
  @IsString()
  @IsNotEmpty()
  partyId: string;

  @ApiProperty({
    description: 'Party name',
    example: 'Ministry of Health',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Responsibility ID',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  responsibilityId: number;

  @ApiProperty({
    description: 'Organization ID',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  organizationId: number;

  @ApiProperty({
    description: 'Objective ID',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  objectiveId: number;

  @ApiProperty({
    description: 'Goal ID',
    example: 1,
  })
  @IsInt()
  @IsPositive()
  goalId: number;

  @ApiProperty({
    description: 'Signatory name',
    example: 'Dr. <PERSON>',
  })
  @IsString()
  @IsNotEmpty()
  signatory: string;

  @ApiProperty({
    description: 'Signatory position',
    example: 'Director General',
  })
  @IsString()
  @IsNotEmpty()
  position: string;

  @ApiProperty({
    description: 'Duration in years',
    example: 1,
    default: 1,
  })
  @IsInt()
  @Min(1)
  @Max(10)
  duration: number = 1;

  @ApiProperty({
    description: 'Reason for extended duration (required if duration > 1)',
    example: 'Long-term strategic partnership requires extended commitment',
    required: false,
  })
  @IsString()
  @IsOptional()
  reasonForExtendedDuration?: string;
}

export class UpdatePartyDto {
  @ApiProperty({
    description: 'Party name',
    example: 'Updated Ministry of Health',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Responsibility ID',
    example: 2,
    required: false,
  })
  @IsInt()
  @IsPositive()
  @IsOptional()
  responsibilityId?: number;

  @ApiProperty({
    description: 'Objective ID',
    example: 2,
    required: false,
  })
  @IsInt()
  @IsPositive()
  @IsOptional()
  objectiveId?: number;

  @ApiProperty({
    description: 'Goal ID',
    example: 2,
    required: false,
  })
  @IsInt()
  @IsPositive()
  @IsOptional()
  goalId?: number;

  @ApiProperty({
    description: 'Signatory name',
    example: 'Dr. Jane Smith',
    required: false,
  })
  @IsString()
  @IsOptional()
  signatory?: string;

  @ApiProperty({
    description: 'Signatory position',
    example: 'Deputy Director General',
    required: false,
  })
  @IsString()
  @IsOptional()
  position?: string;

  @ApiProperty({
    description: 'Duration in years',
    example: 2,
    required: false,
  })
  @IsInt()
  @Min(1)
  @Max(10)
  @IsOptional()
  duration?: number;

  @ApiProperty({
    description: 'Reason for extended duration',
    example: 'Updated justification for extended partnership',
    required: false,
  })
  @IsString()
  @IsOptional()
  reasonForExtendedDuration?: string;
}

export class PartyResponseDto {
  @ApiProperty({ description: 'Party ID', example: 1 })
  id: number;

  @ApiProperty({ description: 'Party identifier', example: 'PARTY-001-2024' })
  partyId: string;

  @ApiProperty({ description: 'Party name', example: 'Ministry of Health' })
  name: string;

  @ApiProperty({ description: 'Responsibility ID', example: 1 })
  responsibilityId: number;

  @ApiProperty({ description: 'Organization ID', example: 1 })
  organizationId: number;

  @ApiProperty({ description: 'Objective ID', example: 1 })
  objectiveId: number;

  @ApiProperty({ description: 'Goal ID', example: 1 })
  goalId: number;

  @ApiProperty({ description: 'Signatory name', example: 'Dr. John Doe' })
  signatory: string;

  @ApiProperty({ description: 'Signatory position', example: 'Director General' })
  position: string;

  @ApiProperty({ description: 'Duration in years', example: 1 })
  duration: number;

  @ApiProperty({ description: 'Reason for extended duration', required: false })
  reasonForExtendedDuration?: string;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;

  @ApiProperty({ description: 'Soft delete flag' })
  deleted: boolean;
}

export class PartyWithRelationsDto extends PartyResponseDto {
  @ApiProperty({ description: 'Organization information' })
  organization?: {
    id: number;
    organizationName: string;
    organizationEmail: string;
    organizationType: {
      id: number;
      typeName: string;
    };
  };

  @ApiProperty({ description: 'Responsibility information' })
  responsibility?: {
    id: number;
    name: string;
  };

  @ApiProperty({ description: 'Objective information' })
  objective?: {
    id: number;
    name: string;
  };

  @ApiProperty({ description: 'Goal information' })
  goal?: {
    id: number;
    name: string;
  };

  @ApiProperty({ description: 'Associated MoU information' })
  mou?: {
    id: number;
    mouKey: string;
    mouApplications: Array<{
      id: number;
      applicationKey: string;
    }>;
  };
}
