import { ApiProperty } from '@nestjs/swagger';

export class DomainInterventionResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the domain intervention',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Name of the domain intervention',
    example: 'Primary Health Care',
  })
  domainName: string;

  @ApiProperty({
    description: 'Description of the domain intervention',
    example: 'Comprehensive primary health care services and interventions',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Parent domain ID for hierarchical structure',
    example: 1,
    required: false,
  })
  parentId?: number;

  @ApiProperty({
    description: 'Parent domain details',
    required: false,
  })
  parent?: DomainInterventionResponseDto;

  @ApiProperty({
    description: 'Child domains (sub-domains)',
    type: [DomainInterventionResponseDto],
    required: false,
  })
  children?: DomainInterventionResponseDto[];

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;
}
