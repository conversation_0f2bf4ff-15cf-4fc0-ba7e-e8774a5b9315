import { CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserRole } from '@prisma/client';
import { ResourceType, PermissionLevel } from '../../common/enums/roles.enum';
export declare const ROLES_KEY = "roles";
export declare const RESOURCE_KEY = "resource";
export declare const PERMISSION_KEY = "permission";
export declare const Roles: (...roles: UserRole[]) => import("@nestjs/common").CustomDecorator<string>;
export declare const RequiresResource: (resource: ResourceType) => import("@nestjs/common").CustomDecorator<string>;
export declare const RequiresPermission: (level: PermissionLevel) => import("@nestjs/common").CustomDecorator<string>;
export declare class RolesGuard implements CanActivate {
    private reflector;
    constructor(reflector: Reflector);
    canActivate(context: ExecutionContext): boolean;
}
export declare class PermissionGuard implements CanActivate {
    private reflector;
    constructor(reflector: Reflector);
    canActivate(context: ExecutionContext): boolean;
}
export declare const Auth: (roles?: UserRole[], resource?: ResourceType, permission?: PermissionLevel) => <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
