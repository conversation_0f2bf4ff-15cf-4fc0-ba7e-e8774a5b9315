import { PrismaService } from '../prisma/prisma.service';
import { CreateFinancingAgentDto, UpdateFinancingAgentDto } from './dto';
export declare class FinancingAgentsService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createFinancingAgentDto: CreateFinancingAgentDto, userId: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        agentName: string;
        contactInfo: string | null;
    }>;
    findAll(): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        agentName: string;
        contactInfo: string | null;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        agentName: string;
        contactInfo: string | null;
    }>;
    update(id: number, updateFinancingAgentDto: UpdateFinancingAgentDto, userId: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        agentName: string;
        contactInfo: string | null;
    }>;
    remove(id: number, userId: string): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        deleted: boolean;
        description: string | null;
        agentName: string;
        contactInfo: string | null;
    }>;
}
