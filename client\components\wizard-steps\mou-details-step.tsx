"use client"

import { useEffect } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Building, Calendar, Info } from 'lucide-react'
import { useFieldBlurAutoSave } from '@/hooks/use-auto-save'
import { useMouApplicationStore } from '@/store/mou-application-store'
import { mouDetailsSchema } from '@/lib/validations/mou-application'
import { demoOrganization } from '@/data/mock-data'

type FormData = z.infer<typeof mouDetailsSchema>

export function MouDetailsStep() {
  const { onBlur } = useFieldBlurAutoSave()
  const { data, updateMouDetails } = useMouApplicationStore()

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<FormData>({
    resolver: zodResolver(mouDetailsSchema),
    defaultValues: {
      organizationName: data.organizationName || demoOrganization.name,
      mouDuration: data.mouDuration,
      extendedDurationReason: data.extendedDurationReason || ''
    }
  })

  const watchDuration = watch('mouDuration')

  useEffect(() => {
    // Update store when form values change
    const subscription = watch((value, { name }) => {
      if (name) {
        updateMouDetails(
          value.mouDuration || 1,
          value.extendedDurationReason || ''
        )
      }
    })
    return () => subscription.unsubscribe()
  }, [watch, updateMouDetails])

  return (
    <div className="space-y-6">
      {/* Organization Information Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="h-5 w-5 text-primary" />
            Organization Information
          </CardTitle>
          <CardDescription>
            Your organization details are pre-filled from your account information.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="organizationName">Organization Name</Label>
            <Input
              id="organizationName"
              {...register('organizationName')}
              onBlur={onBlur}
              className="bg-muted"
              readOnly
            />
            <p className="text-sm text-muted-foreground">
              This information is taken from your registered organization profile.
            </p>
            {errors.organizationName && (
              <p className="text-sm text-destructive">{errors.organizationName.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* MoU Duration Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-primary" />
            MoU Duration
          </CardTitle>
          <CardDescription>
            Specify the duration for this Memorandum of Understanding.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="mouDuration">Duration (Years)</Label>
            <Select
              value={String(watchDuration)}
              onValueChange={(value) => setValue('mouDuration', parseInt(value, 10))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select duration" />
              </SelectTrigger>
              <SelectContent>
                {[1, 2, 3, 4, 5].map((year) => (
                  <SelectItem key={year} value={String(year)}>
                    {year} {year === 1 ? 'Year' : 'Years'}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.mouDuration && (
              <p className="text-sm text-destructive">{errors.mouDuration.message}</p>
            )}
          </div>

          {watchDuration > 1 && (
            <div className="space-y-2">
              <Label htmlFor="extendedDurationReason">
                Reason for Extended Duration
                <span className="text-destructive ml-1">*</span>
              </Label>
              <Textarea
                id="extendedDurationReason"
                {...register('extendedDurationReason')}
                onBlur={onBlur}
                placeholder="Please explain why a duration longer than 1 year is needed. Include specific objectives, milestones, and expected outcomes that justify the extended timeframe..."
                className="min-h-[120px]"
              />
              {errors.extendedDurationReason && (
                <p className="text-sm text-destructive">{errors.extendedDurationReason.message}</p>
              )}
            </div>
          )}

          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <strong>Standard Duration:</strong> The typical MoU duration is 1 year.
              Extended durations require additional justification and may be subject to
              enhanced review and approval processes.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  )
}
