import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsInt } from 'class-validator';

export class CreateInputCategoryDto {
  @ApiProperty({
    description: 'Name of the input category',
    example: 'Medical Equipment',
  })
  @IsString()
  @IsNotEmpty()
  categoryName: string;

  @ApiProperty({
    description: 'Description of the input category',
    example: 'Category for all medical equipment and devices',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Parent category ID for nested structure',
    example: 1,
    required: false,
  })
  @IsInt()
  @IsOptional()
  parentId?: number;
}
