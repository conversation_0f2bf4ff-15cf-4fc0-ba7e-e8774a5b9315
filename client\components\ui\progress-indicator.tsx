"use client"

import * as React from "react"
import { Check, Circle } from "lucide-react"
import { cn } from "@/lib/utils"

interface Step {
  id: number
  title: string
  description?: string
}

interface ProgressIndicatorProps {
  steps: Step[]
  currentStep: number
  completedSteps?: number[]
  className?: string
  orientation?: "horizontal" | "vertical"
  showLabels?: boolean
  onStepClick?: (stepId: number) => void
}

export function ProgressIndicator({
  steps,
  currentStep,
  completedSteps = [],
  className,
  orientation = "horizontal",
  showLabels = true,
  onStepClick,
}: ProgressIndicatorProps) {
  const isStepCompleted = (stepId: number) => completedSteps.includes(stepId)
  const isStepCurrent = (stepId: number) => stepId === currentStep
  const isStepAccessible = (stepId: number) => stepId <= currentStep || isStepCompleted(stepId)

  if (orientation === "vertical") {
    return (
      <div className={cn("space-y-4", className)}>
        {steps.map((step, index) => {
          const isCompleted = isStepCompleted(step.id)
          const isCurrent = isStepCurrent(step.id)
          const isAccessible = isStepAccessible(step.id)
          const isLast = index === steps.length - 1

          return (
            <div key={step.id} className="relative">
              <div className="flex items-start">
                <div className="flex flex-col items-center">
                  <button
                    onClick={() => isAccessible && onStepClick?.(step.id)}
                    disabled={!isAccessible || !onStepClick}
                    className={cn(
                      "flex h-8 w-8 items-center justify-center rounded-full border-2 transition-colors",
                      isCompleted && "bg-primary border-primary text-primary-foreground",
                      isCurrent && !isCompleted && "border-primary bg-background text-primary",
                      !isCurrent && !isCompleted && !isAccessible && "border-muted bg-muted text-muted-foreground",
                      !isCurrent && !isCompleted && isAccessible && "border-muted-foreground bg-background text-muted-foreground hover:border-primary",
                      isAccessible && onStepClick && "cursor-pointer",
                      !isAccessible && "cursor-not-allowed"
                    )}
                  >
                    {isCompleted ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <span className="text-sm font-medium">{step.id}</span>
                    )}
                  </button>
                  {!isLast && (
                    <div
                      className={cn(
                        "mt-2 h-8 w-0.5 transition-colors",
                        isCompleted ? "bg-primary" : "bg-muted"
                      )}
                    />
                  )}
                </div>
                {showLabels && (
                  <div className="ml-4 min-w-0 flex-1">
                    <p
                      className={cn(
                        "text-sm font-medium",
                        isCurrent && "text-primary",
                        isCompleted && "text-foreground",
                        !isCurrent && !isCompleted && "text-muted-foreground"
                      )}
                    >
                      {step.title}
                    </p>
                    {step.description && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {step.description}
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  return (
    <div className={cn("flex items-center justify-between", className)}>
      {steps.map((step, index) => {
        const isCompleted = isStepCompleted(step.id)
        const isCurrent = isStepCurrent(step.id)
        const isAccessible = isStepAccessible(step.id)
        const isLast = index === steps.length - 1

        return (
          <React.Fragment key={step.id}>
            <div className="flex flex-col items-center space-y-2">
              <button
                onClick={() => isAccessible && onStepClick?.(step.id)}
                disabled={!isAccessible || !onStepClick}
                className={cn(
                  "flex h-8 w-8 items-center justify-center rounded-full border-2 transition-colors",
                  isCompleted && "bg-primary border-primary text-primary-foreground",
                  isCurrent && !isCompleted && "border-primary bg-background text-primary",
                  !isCurrent && !isCompleted && !isAccessible && "border-muted bg-muted text-muted-foreground",
                  !isCurrent && !isCompleted && isAccessible && "border-muted-foreground bg-background text-muted-foreground hover:border-primary",
                  isAccessible && onStepClick && "cursor-pointer",
                  !isAccessible && "cursor-not-allowed"
                )}
              >
                {isCompleted ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <span className="text-sm font-medium">{step.id}</span>
                )}
              </button>
              {showLabels && (
                <div className="text-center">
                  <p
                    className={cn(
                      "text-xs font-medium",
                      isCurrent && "text-primary",
                      isCompleted && "text-foreground",
                      !isCurrent && !isCompleted && "text-muted-foreground"
                    )}
                  >
                    {step.title}
                  </p>
                </div>
              )}
            </div>
            {!isLast && (
              <div
                className={cn(
                  "flex-1 h-0.5 mx-2 transition-colors",
                  isCompleted ? "bg-primary" : "bg-muted"
                )}
              />
            )}
          </React.Fragment>
        )
      })}
    </div>
  )
}

// Simple progress bar with step count
interface SimpleProgressProps {
  currentStep: number
  totalSteps: number
  className?: string
}

export function SimpleProgress({
  currentStep,
  totalSteps,
  className,
}: SimpleProgressProps) {
  const percentage = (currentStep / totalSteps) * 100

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex justify-between text-sm">
        <span className="font-medium">
          Step {currentStep} of {totalSteps}
        </span>
        <span className="text-muted-foreground">{Math.round(percentage)}%</span>
      </div>
      <div className="w-full bg-muted rounded-full h-2">
        <div
          className="bg-primary h-2 rounded-full transition-all duration-300 ease-in-out"
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  )
}
