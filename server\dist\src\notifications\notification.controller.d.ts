import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { NotificationService } from './notification.service';
export declare class NotificationController implements OnGatewayConnection, OnGatewayDisconnect {
    private readonly notificationService;
    server: Server;
    private userSockets;
    constructor(notificationService: NotificationService);
    handleConnection(client: Socket): Promise<void>;
    handleDisconnect(client: Socket): void;
    getUserNotifications(req: any, skip?: number, take?: number, includeRead?: boolean): Promise<any[]>;
    getUnreadCount(req: any): Promise<{
        count: number;
    }>;
    markAsRead(id: number, req: any): Promise<{
        success: boolean;
    }>;
    markAllRead(req: any): Promise<{
        success: boolean;
    }>;
    handleSubscribe(client: Socket): void;
    handleUnsubscribe(client: Socket): void;
    sendNotificationToUser(userId: number, notification: any): Promise<void>;
    sendNotificationToRole(role: string, notification: any): Promise<void>;
    private getUserIdFromToken;
    private getUserIdFromSocket;
    private getUsersByRole;
}
