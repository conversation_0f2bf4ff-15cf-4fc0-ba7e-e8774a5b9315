export declare class CreateHealthCareProviderDto {
    providerName: string;
    description?: string;
    location?: string;
    contactEmail?: string;
    contactPhone?: string;
    website?: string;
}
export declare class UpdateHealthCareProviderDto {
    providerName?: string;
    description?: string;
    location?: string;
    contactEmail?: string;
    contactPhone?: string;
    website?: string;
}
export declare class HealthCareProviderResponseDto {
    id: number;
    providerName: string;
    description?: string;
    location?: string;
    contactEmail?: string;
    contactPhone?: string;
    website?: string;
    createdAt: Date;
    updatedAt: Date;
    deleted: boolean;
}
