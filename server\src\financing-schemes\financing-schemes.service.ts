import { Injectable, NotFoundException, ConflictException, ForbiddenException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateFinancingSchemeDto, UpdateFinancingSchemeDto } from './dto';

@Injectable()
export class FinancingSchemesService {
    private readonly logger = new Logger(FinancingSchemesService.name);

    constructor(private prisma: PrismaService) {}

    async create(createFinancingSchemeDto: CreateFinancingSchemeDto, userId: string) {
        try {
            // Check if user has admin role
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            if (user.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can create financing schemes');
            }

            // Check if financing scheme already exists
            const existing = await this.prisma.financingScheme.findFirst({
                where: {
                    schemeName: createFinancingSchemeDto.schemeName,
                    deleted: false
                }
            });

            if (existing) {
                throw new ConflictException('Financing scheme with this name already exists');
            }

            // Create the financing scheme
            const financingScheme = await this.prisma.financingScheme.create({
                data: {
                    schemeName: createFinancingSchemeDto.schemeName,
                    description: createFinancingSchemeDto.description,
                    terms: createFinancingSchemeDto.terms,
                    conditions: createFinancingSchemeDto.conditions
                }
            });

            return financingScheme;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to create financing scheme', error.stack);
            throw new Error('Failed to create financing scheme');
        }
    }

    async findAll() {
        return this.prisma.financingScheme.findMany({
            where: {
                deleted: false
            },
            orderBy: {
                schemeName: 'asc'
            }
        });
    }

    async findOne(id: number) {
        const financingScheme = await this.prisma.financingScheme.findFirst({
            where: {
                id,
                deleted: false
            }
        });

        if (!financingScheme) {
            throw new NotFoundException('Financing scheme not found');
        }

        return financingScheme;
    }

    async update(id: number, updateFinancingSchemeDto: UpdateFinancingSchemeDto, userId: string) {
        try {
            // Check if user has admin role
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            if (user.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can update financing schemes');
            }

            // Check if financing scheme exists
            const existing = await this.prisma.financingScheme.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });

            if (!existing) {
                throw new NotFoundException('Financing scheme not found');
            }

            // Check if name is being changed and if so, if it conflicts
            if (updateFinancingSchemeDto.schemeName && updateFinancingSchemeDto.schemeName !== existing.schemeName) {
                const nameConflict = await this.prisma.financingScheme.findFirst({
                    where: {
                        schemeName: updateFinancingSchemeDto.schemeName,
                        deleted: false,
                        id: { not: id }
                    }
                });

                if (nameConflict) {
                    throw new ConflictException('Another financing scheme with this name already exists');
                }
            }

            // Update the financing scheme
            const updated = await this.prisma.financingScheme.update({
                where: { id },
                data: {
                    schemeName: updateFinancingSchemeDto.schemeName,
                    description: updateFinancingSchemeDto.description,
                    terms: updateFinancingSchemeDto.terms,
                    conditions: updateFinancingSchemeDto.conditions
                }
            });

            return updated;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to update financing scheme', error.stack);
            throw new Error('Failed to update financing scheme');
        }
    }

    async remove(id: number, userId: string) {
        try {
            // Check if user has admin role
            const user = await this.prisma.user.findFirst({
                where: {
                    id: parseInt(userId),
                    deleted: false
                }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            if (user.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can delete financing schemes');
            }

            // Check if financing scheme exists
            const existing = await this.prisma.financingScheme.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });

            if (!existing) {
                throw new NotFoundException('Financing scheme not found');
            }

            // For now, just soft delete the financing scheme as there are no direct dependencies
            // If dependencies are added later, add checks here
            await this.prisma.financingScheme.update({
                where: { id },
                data: { deleted: true }
            });

            return { success: true };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to delete financing scheme', error.stack);
            throw new Error('Failed to delete financing scheme');
        }
    }
}
