import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateActivityDto, UpdateActivityDto } from './dto/create-activity.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class ActivitiesService {
  constructor(private prisma: PrismaService) {}

  async create(createActivityDto: CreateActivityDto) {
    try {
      // Validate foreign key relationships
      await this.validateRelationships(createActivityDto);

      // Validate date range
      const startDate = new Date(createActivityDto.startDate);
      const endDate = new Date(createActivityDto.endDate);

      if (startDate >= endDate) {
        throw new BadRequestException('Start date must be before end date');
      }

      const activity = await this.prisma.activity.create({
        data: {
          ...createActivityDto,
          startDate,
          endDate,
        },
        include: {
          project: {
            include: {
              mouApplication: true,
            },
          },
          domainIntervention: {
            include: {
              parent: true,
            },
          },
          input: {
            include: {
              inputSubclass: true,
            },
          },
        },
      });

      return activity;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2003') {
          throw new BadRequestException('Invalid foreign key reference');
        }
      }
      throw error;
    }
  }

  async findAll(page: number = 1, limit: number = 10, projectId?: number, fiscalYear?: number) {
    const skip = (page - 1) * limit;
    
    const where: Prisma.ActivityWhereInput = {
      deleted: false,
      ...(projectId && { projectId }),
      ...(fiscalYear && { fiscalYear }),
    };

    const [activities, total] = await Promise.all([
      this.prisma.activity.findMany({
        where,
        skip,
        take: limit,
        include: {
          project: {
            include: {
              mouApplication: true,
            },
          },
          domainIntervention: {
            include: {
              parent: true,
            },
          },
          input: {
            include: {
              inputSubclass: true,
            },
          },
        },
        orderBy: {
          startDate: 'desc',
        },
      }),
      this.prisma.activity.count({ where }),
    ]);

    return {
      data: activities,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number) {
    const activity = await this.prisma.activity.findFirst({
      where: {
        id,
        deleted: false,
      },
      include: {
        project: {
          include: {
            mouApplication: {
              include: {
                mou: {
                  include: {
                    party: {
                      include: {
                        organization: true,
                      },
                    },
                  },
                },
              },
            },
            budgetType: true,
            fundingSource: true,
            fundingUnit: true,
          },
        },
        domainIntervention: {
          include: {
            parent: true,
            children: true,
          },
        },
        input: {
          include: {
            inputSubclass: true,
          },
        },
      },
    });

    if (!activity) {
      throw new NotFoundException(`Activity with ID ${id} not found`);
    }

    return activity;
  }

  async update(id: number, updateActivityDto: UpdateActivityDto) {
    // Check if activity exists
    const existingActivity = await this.prisma.activity.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingActivity) {
      throw new NotFoundException(`Activity with ID ${id} not found`);
    }

    // Validate foreign key relationships if they are being updated
    if (Object.keys(updateActivityDto).some(key => 
      ['projectId', 'domainInterventionId', 'inputId'].includes(key)
    )) {
      await this.validateRelationships({
        ...existingActivity,
        ...updateActivityDto,
      } as CreateActivityDto);
    }

    // Validate date range if dates are being updated
    const startDate = updateActivityDto.startDate ? new Date(updateActivityDto.startDate) : existingActivity.startDate;
    const endDate = updateActivityDto.endDate ? new Date(updateActivityDto.endDate) : existingActivity.endDate;

    if (startDate >= endDate) {
      throw new BadRequestException('Start date must be before end date');
    }

    try {
      const updatedActivity = await this.prisma.activity.update({
        where: { id },
        data: {
          ...updateActivityDto,
          ...(updateActivityDto.startDate && { startDate }),
          ...(updateActivityDto.endDate && { endDate }),
        },
        include: {
          project: {
            include: {
              mouApplication: true,
            },
          },
          domainIntervention: {
            include: {
              parent: true,
            },
          },
          input: {
            include: {
              inputSubclass: true,
            },
          },
        },
      });

      return updatedActivity;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2003') {
          throw new BadRequestException('Invalid foreign key reference');
        }
      }
      throw error;
    }
  }

  async remove(id: number) {
    // Check if activity exists
    const existingActivity = await this.prisma.activity.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingActivity) {
      throw new NotFoundException(`Activity with ID ${id} not found`);
    }

    // Soft delete
    await this.prisma.activity.update({
      where: { id },
      data: { deleted: true },
    });

    return { message: 'Activity deleted successfully' };
  }

  async getActivitiesByProject(projectId: number) {
    const activities = await this.prisma.activity.findMany({
      where: {
        projectId,
        deleted: false,
      },
      include: {
        domainIntervention: {
          include: {
            parent: true,
          },
        },
        input: {
          include: {
            inputSubclass: true,
          },
        },
      },
      orderBy: {
        startDate: 'asc',
      },
    });

    return activities;
  }

  async getActivitiesByFiscalYear(fiscalYear: number) {
    const activities = await this.prisma.activity.findMany({
      where: {
        fiscalYear,
        deleted: false,
      },
      include: {
        project: {
          include: {
            mouApplication: true,
            organization: true,
          },
        },
        domainIntervention: true,
        input: {
          include: {
            inputSubclass: true,
          },
        },
      },
      orderBy: {
        startDate: 'asc',
      },
    });

    return activities;
  }

  private async validateRelationships(dto: CreateActivityDto) {
    const [project, domainIntervention, input] = await Promise.all([
      this.prisma.project.findUnique({ where: { id: dto.projectId } }),
      this.prisma.domainIntervention.findUnique({ where: { id: dto.domainInterventionId } }),
      this.prisma.input.findUnique({ where: { id: dto.inputId } }),
    ]);

    if (!project) {
      throw new NotFoundException(`Project with ID ${dto.projectId} not found`);
    }
    if (!domainIntervention) {
      throw new NotFoundException(`Domain intervention with ID ${dto.domainInterventionId} not found`);
    }
    if (!input) {
      throw new NotFoundException(`Input with ID ${dto.inputId} not found`);
    }
  }
}
