import { Injectable, NotFoundException, ConflictException, ForbiddenException, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateUserDto, UpdateUserDto } from './dto';
import { UserRole } from '@prisma/client';
import * as bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import { EmailService } from '../email/email.service';

@Injectable()
export class UsersService {
    private readonly logger = new Logger(UsersService.name);
    private readonly SALT_ROUNDS = 10;

    constructor(private prisma: PrismaService, private emailService: EmailService) {}

    async findAll(currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can view all users
            if (currentUser.role !== UserRole.ADMIN) {
                throw new ForbiddenException('Only administrators can view all users');
            }

            const users = await this.prisma.user.findMany({
                where: { deleted: false },
                include: {
                    organization: {
                        select: {
                            id: true,
                            organizationName: true
                        }
                    }
                },
                orderBy: { createdAt: 'desc' }
            });

            return users.map(user => ({
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                role: user.role,
                emailVerified: user.emailVerified,
                organizationId: user.organizationId,
                organization: user.organization,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            }));
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to fetch users', error.stack);
            throw new Error('Failed to fetch users');
        }
    }

    async findOne(id: string, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Users can view their own profile, admins can view any profile
            if (currentUser.role !== UserRole.ADMIN && currentUserId !== id) {
                throw new ForbiddenException('You can only view your own profile');
            }

            const user = await this.prisma.user.findUnique({
                where: { id: parseInt(id, 10), deleted: false },
                include: {
                    organization: {
                        select: {
                            id: true,
                            organizationName: true
                        }
                    }
                }
            });

            if (!user) {
                throw new NotFoundException('User not found');
            }

            return {
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                role: user.role,
                emailVerified: user.emailVerified,
                organizationId: user.organizationId,
                organization: user.organization,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to fetch user with ID ${id}`, error.stack);
            throw new Error('Failed to fetch user');
        }
    }

    async create(createUserDto: CreateUserDto, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can create users
            if (currentUser.role !== UserRole.ADMIN) {
                throw new ForbiddenException('Only administrators can create users');
            }

            // Check if user already exists
            const existingUser = await this.prisma.user.findUnique({
                where: { email: createUserDto.email }
            });

            if (existingUser) {
                throw new ConflictException('User with this email already exists');
            }

            // Business rule validation
            if (createUserDto.role === UserRole.PARTNER) {
                throw new ForbiddenException('ADMIN users cannot create PARTNER users. PARTNER users must self-register with organization data.');
            }


            // Generate temporary password
            const tempPassword = uuidv4().substring(0, 12);
            const hashedPassword = await bcrypt.hash(tempPassword, this.SALT_ROUNDS);

            // Generate verification token
            const verificationToken = uuidv4();
            const verificationTokenExpiryTime = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

            const user = await this.prisma.user.create({
                data: {
                    firstName: createUserDto.firstName,
                    lastName: createUserDto.lastName,
                    email: createUserDto.email,
                    password: hashedPassword,
                    role: createUserDto.role,
                    verificationToken,
                    verificationTokenExpiryTime,
                    emailVerified: false
                },
            });

            // Send verification email with temporary password
            await this.emailService.sendEmailVerificationEmail({
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                verificationToken: verificationToken,
                tempPassword: tempPassword
            });

            return {
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                role: user.role,
                emailVerified: user.emailVerified,
                organizationId: user.organizationId,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
                tempPassword // Return temp password for admin to share with user
            };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException ||
                error instanceof ForbiddenException || error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to create user', error.stack);
            throw new Error('Failed to create user');
        }
    }

    async update(id: string, updateUserDto: UpdateUserDto, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Users can update their own profile (limited fields), admins can update any profile
            if (currentUser.role !== UserRole.ADMIN && currentUserId !== id) {
                throw new ForbiddenException('You can only update your own profile');
            }

            // Check if user exists
            const existingUser = await this.prisma.user.findUnique({
                where: { id: parseInt(id, 10), deleted: false }
            });

            if (!existingUser) {
                throw new NotFoundException('User not found');
            }

            // If email is being updated, check for conflicts
            if (updateUserDto.email && updateUserDto.email !== existingUser.email) {
                const emailExists = await this.prisma.user.findUnique({
                    where: { email: updateUserDto.email }
                });

                if (emailExists) {
                    throw new ConflictException('Email already in use');
                }
            }

            // Business rule validation for role and organization changes
            if (updateUserDto.role === UserRole.ADMIN && updateUserDto.organizationId) {
                throw new BadRequestException('ADMIN users cannot be associated with an organization');
            }

            // Check if organization exists (if provided)
            if (updateUserDto.organizationId) {
                const organization = await this.prisma.organization.findUnique({
                    where: { id: parseInt(updateUserDto.organizationId, 10) }
                });

                if (!organization) {
                    throw new NotFoundException('Organization not found');
                }
            }

            // Non-admin users can only update limited fields
            const updateData: any = {};
            if (currentUser.role === UserRole.ADMIN) {
                // Admin can update all fields
                if (updateUserDto.firstName) updateData.firstName = updateUserDto.firstName;
                if (updateUserDto.lastName) updateData.lastName = updateUserDto.lastName;
                if (updateUserDto.email) updateData.email = updateUserDto.email;
                if (updateUserDto.role) updateData.role = updateUserDto.role;
                if (updateUserDto.organizationId !== undefined) updateData.organizationId = updateUserDto.organizationId;
            } else {
                // Regular users can only update name
                if (updateUserDto.firstName) updateData.firstName = updateUserDto.firstName;
                if (updateUserDto.lastName) updateData.lastName = updateUserDto.lastName;
            }

            const user = await this.prisma.user.update({
                where: { id: parseInt(id, 10) },
                data: updateData,
                include: {
                    organization: {
                        select: {
                            id: true,
                            organizationName: true
                        }
                    }
                }
            });

            return {
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                role: user.role,
                emailVerified: user.emailVerified,
                organizationId: user.organizationId,
                organization: user.organization,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException ||
                error instanceof ForbiddenException || error instanceof BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to update user with ID ${id}`, error.stack);
            throw new Error('Failed to update user');
        }
    }

    async remove(id: string, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: parseInt(currentUserId, 10) }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can delete users
            if (currentUser.role !== UserRole.ADMIN) {
                throw new ForbiddenException('Only administrators can delete users');
            }

            // Check if user exists
            const existingUser = await this.prisma.user.findUnique({
                where: { id: parseInt(id, 10), deleted: false }
            });

            if (!existingUser) {
                throw new NotFoundException('User not found');
            }

            // Prevent admin from deleting themselves
            if (id === currentUserId) {
                throw new ForbiddenException('You cannot delete your own account');
            }

            // Soft delete the user
            await this.prisma.user.update({
                where: { id: parseInt(id, 10) },
                data: { deleted: true }
            });

            return { message: 'User deleted successfully' };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to delete user with ID ${id}`, error.stack);
            throw new Error('Failed to delete user');
        }
    }
}
