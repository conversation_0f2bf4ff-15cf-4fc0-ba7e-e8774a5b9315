import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateResponsibilityDto, UpdateResponsibilityDto } from './dto/create-responsibility.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class ResponsibilitiesService {
  constructor(private prisma: PrismaService) {}

  async create(createResponsibilityDto: CreateResponsibilityDto) {
    try {
      const responsibility = await this.prisma.responsibility.create({
        data: createResponsibilityDto,
      });

      return responsibility;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Responsibility name must be unique');
        }
      }
      throw error;
    }
  }

  async findAll() {
    const responsibilities = await this.prisma.responsibility.findMany({
      where: {
        deleted: false,
      },
      orderBy: {
        name: 'asc',
      },
    });

    return responsibilities;
  }

  async findOne(id: number) {
    const responsibility = await this.prisma.responsibility.findFirst({
      where: {
        id,
        deleted: false,
      },
      include: {
        parties: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!responsibility) {
      throw new NotFoundException(`Responsibility with ID ${id} not found`);
    }

    return responsibility;
  }

  async update(id: number, updateResponsibilityDto: UpdateResponsibilityDto) {
    // Check if responsibility exists
    const existingResponsibility = await this.prisma.responsibility.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingResponsibility) {
      throw new NotFoundException(`Responsibility with ID ${id} not found`);
    }

    try {
      const updatedResponsibility = await this.prisma.responsibility.update({
        where: { id },
        data: updateResponsibilityDto,
      });

      return updatedResponsibility;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Responsibility name must be unique');
        }
      }
      throw error;
    }
  }

  async remove(id: number) {
    // Check if responsibility exists
    const existingResponsibility = await this.prisma.responsibility.findFirst({
      where: {
        id,
        deleted: false,
      },
    });

    if (!existingResponsibility) {
      throw new NotFoundException(`Responsibility with ID ${id} not found`);
    }

    // Check if responsibility is being used by parties
    const partiesUsingResponsibility = await this.prisma.party.findMany({
      where: {
        responsibilityId: id,
        deleted: false,
      },
    });

    if (partiesUsingResponsibility.length > 0) {
      throw new ConflictException('Cannot delete responsibility that is being used by parties');
    }

    // Soft delete
    await this.prisma.responsibility.update({
      where: { id },
      data: { deleted: true },
    });

    return { message: 'Responsibility deleted successfully' };
  }
}
