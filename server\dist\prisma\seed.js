"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const bcrypt = require("bcryptjs");
const prisma = new client_1.PrismaClient();
async function main() {
    const organizationTypes = [
        { typeName: "Local NGOs" },
        { typeName: "International NGOs" },
    ];
    for (const type of organizationTypes) {
        await prisma.organizationType.upsert({
            where: { typeName: type.typeName },
            update: {},
            create: {
                typeName: type.typeName,
            },
        });
    }
    console.log('✅ Seeded OrganizationType data.');
    const hashedPassword = await bcrypt.hash(process.env.ADMIN_PASSWORD, 10);
    const adminUser = {
        firstName: process.env.ADMIN_FIRST_NAME || 'Admin',
        lastName: process.env.ADMIN_LAST_NAME || 'User',
        email: process.env.ADMIN_EMAIL,
        password: hashedPassword,
    };
    await prisma.user.upsert({
        where: { email: adminUser.email },
        update: {},
        create: {
            firstName: adminUser.firstName,
            lastName: adminUser.lastName,
            email: adminUser.email,
            password: adminUser.password,
            role: client_1.UserRole.ADMIN,
            emailVerified: true,
        },
    });
    console.log('✅ Seeded Admin user data.');
}
main()
    .catch((e) => {
    console.error('❌ Error seeding data:', e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=seed.js.map