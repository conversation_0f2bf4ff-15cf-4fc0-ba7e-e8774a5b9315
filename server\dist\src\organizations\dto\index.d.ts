import { CreateAddressDto, AddressResponseDto } from '../../addresses/dto';
export declare class CreateOrganizationDto {
    organizationName: string;
    organizationRegistrationNumber: string;
    organizationPhoneNumber: string;
    organizationEmail: string;
    organizationWebsite?: string;
    homeCountryRepresentative: string;
    rwandaRepresentative: string;
    organizationRgbNumber: string;
    organizationTypeId: number;
    addresses: CreateAddressDto[];
}
export declare class UpdateOrganizationDto {
    organizationName?: string;
    organizationRegistrationNumber?: string;
    organizationPhoneNumber?: string;
    organizationEmail?: string;
    organizationWebsite?: string;
    homeCountryRepresentative?: string;
    rwandaRepresentative?: string;
    organizationRgbNumber?: string;
    organizationTypeId?: number;
}
export declare class OrganizationResponseDto {
    id: string;
    organizationName: string;
    organizationRegistrationNumber: string;
    organizationPhoneNumber: string;
    organizationEmail: string;
    organizationWebsite?: string;
    homeCountryRepresentative: string;
    rwandaRepresentative: string;
    organizationRgbNumber: string;
    organizationTypeId: number;
    addresses: AddressResponseDto[];
    createAt: Date;
    updatedAt: Date;
    deleted: boolean;
}
export declare class CreateOrganizationWithUserDto {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    organization: CreateOrganizationDto;
}
