import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { InputsService } from './inputs.service';
import { 
  CreateInputDto, 
  UpdateInputDto, 
  InputResponseDto, 
  InputWithRelationsDto,
  CreateInputSubclassDto,
  UpdateInputSubclassDto,
  InputSubclassResponseDto,
  InputSubclassWithRelationsDto
} from './dto/create-input.dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { RolesGuard } from '../auth/guard/roles.guard';
import { Roles } from '../auth/decorator/roles.decorator';
import { UserRole } from '../auth/dto';

@ApiTags('Inputs')
@ApiBearerAuth()
@UseGuards(JwtGuard, RolesGuard)
@Controller('inputs')
export class InputsController {
  constructor(private readonly inputsService: InputsService) {}

  // Input endpoints
  @Post()
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new input' })
  @ApiResponse({
    status: 201,
    description: 'Input created successfully',
    type: InputWithRelationsDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Input subclass not found' })
  async createInput(@Body() createInputDto: CreateInputDto) {
    return this.inputsService.createInput(createInputDto);
  }

  @Get()
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get all inputs with pagination' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10)' })
  @ApiQuery({ name: 'inputSubclassId', required: false, type: Number, description: 'Filter by input subclass ID' })
  @ApiResponse({
    status: 200,
    description: 'List of inputs',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/InputWithRelationsDto' },
        },
        meta: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            page: { type: 'number' },
            limit: { type: 'number' },
            totalPages: { type: 'number' },
          },
        },
      },
    },
  })
  async findAllInputs(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('inputSubclassId') inputSubclassId?: string,
  ) {
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 10;
    const subclassIdNum = inputSubclassId ? parseInt(inputSubclassId, 10) : undefined;

    return this.inputsService.findAllInputs(pageNum, limitNum, subclassIdNum);
  }

  @Get('by-subclass/:inputSubclassId')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get inputs by subclass ID' })
  @ApiResponse({
    status: 200,
    description: 'List of inputs for the subclass',
    type: [InputWithRelationsDto],
  })
  async getInputsBySubclass(@Param('inputSubclassId', ParseIntPipe) inputSubclassId: number) {
    return this.inputsService.getInputsBySubclass(inputSubclassId);
  }

  @Get(':id')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get an input by ID' })
  @ApiResponse({
    status: 200,
    description: 'Input details',
    type: InputWithRelationsDto,
  })
  @ApiResponse({ status: 404, description: 'Input not found' })
  async findOneInput(@Param('id', ParseIntPipe) id: number) {
    return this.inputsService.findOneInput(id);
  }

  @Patch(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Update an input' })
  @ApiResponse({
    status: 200,
    description: 'Input updated successfully',
    type: InputWithRelationsDto,
  })
  @ApiResponse({ status: 404, description: 'Input not found' })
  async updateInput(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateInputDto: UpdateInputDto,
  ) {
    return this.inputsService.updateInput(id, updateInputDto);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete an input (soft delete)' })
  @ApiResponse({
    status: 200,
    description: 'Input deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Input not found' })
  @ApiResponse({ status: 409, description: 'Input is being used by activities' })
  async removeInput(@Param('id', ParseIntPipe) id: number) {
    return this.inputsService.removeInput(id);
  }

  // Input Subclass endpoints
  @Post('subclasses')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new input subclass' })
  @ApiResponse({
    status: 201,
    description: 'Input subclass created successfully',
    type: InputSubclassWithRelationsDto,
  })
  @ApiResponse({ status: 409, description: 'Subclass ID already exists' })
  async createInputSubclass(@Body() createInputSubclassDto: CreateInputSubclassDto) {
    return this.inputsService.createInputSubclass(createInputSubclassDto);
  }

  @Get('subclasses')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get all input subclasses' })
  @ApiResponse({
    status: 200,
    description: 'List of input subclasses',
    type: [InputSubclassWithRelationsDto],
  })
  async findAllInputSubclasses() {
    return this.inputsService.findAllInputSubclasses();
  }

  @Get('subclasses/:id')
  @Roles(UserRole.PARTNER, UserRole.ADMIN, UserRole.COORDINATOR, UserRole.LEGAL, UserRole.TECHNICAL_EXPERT, UserRole.HOD, UserRole.PS, UserRole.MINISTER)
  @ApiOperation({ summary: 'Get an input subclass by ID' })
  @ApiResponse({
    status: 200,
    description: 'Input subclass details',
    type: InputSubclassWithRelationsDto,
  })
  @ApiResponse({ status: 404, description: 'Input subclass not found' })
  async findOneInputSubclass(@Param('id', ParseIntPipe) id: number) {
    return this.inputsService.findOneInputSubclass(id);
  }

  @Patch('subclasses/:id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Update an input subclass' })
  @ApiResponse({
    status: 200,
    description: 'Input subclass updated successfully',
    type: InputSubclassWithRelationsDto,
  })
  @ApiResponse({ status: 404, description: 'Input subclass not found' })
  async updateInputSubclass(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateInputSubclassDto: UpdateInputSubclassDto,
  ) {
    return this.inputsService.updateInputSubclass(id, updateInputSubclassDto);
  }

  @Delete('subclasses/:id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete an input subclass (soft delete)' })
  @ApiResponse({
    status: 200,
    description: 'Input subclass deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Input subclass not found' })
  @ApiResponse({ status: 409, description: 'Input subclass is being used by inputs' })
  async removeInputSubclass(@Param('id', ParseIntPipe) id: number) {
    return this.inputsService.removeInputSubclass(id);
  }
}
