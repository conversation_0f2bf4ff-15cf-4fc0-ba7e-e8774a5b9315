import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  BadRequestException,
  ForbiddenException
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { PrismaService } from '../../prisma/prisma.service';
import { ApplicationStatus, UserRole } from '@prisma/client';

@Injectable()
export class WorkflowValidatorInterceptor implements NestInterceptor {
  constructor(private prisma: PrismaService) {}

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const method = request.method;
    const url = request.url;

    // Extract application ID and action type
    const applicationId = this.extractApplicationId(url);
    const action = this.determineAction(method, url);

    if (applicationId) {
      // Get current application state
      const application = await this.prisma.mouApplication.findUnique({
        where: { id: applicationId },
        include: {
          approvalSteps: true,
          projects: true
        }
      });

      if (!application) {
        throw new BadRequestException('Application not found');
      }

      // Validate workflow state transitions
      await this.validateWorkflowTransition(application, action, user);
    }

    return next.handle();
  }

  private extractApplicationId(url: string): number | null {
    const match = url.match(/\/mou-applications\/(\d+)/);
    return match ? parseInt(match[1]) : null;
  }

  private determineAction(method: string, url: string): string {
    if (url.includes('/review')) return 'REVIEW';
    if (url.includes('/assign-expert')) return 'ASSIGN_EXPERT';
    if (url.includes('/request-modification')) return 'REQUEST_MODIFICATION';
    if (url.includes('/approve')) return 'APPROVE';
    if (method === 'PUT' && url.includes('/update-status')) return 'UPDATE_STATUS';
    return 'OTHER';
  }

  private async validateWorkflowTransition(application: any, action: string, user: any) {
    switch (action) {
      case 'REVIEW':
        await this.validateReviewTransition(application, user);
        break;
      case 'ASSIGN_EXPERT':
        await this.validateExpertAssignment(application, user);
        break;
      case 'REQUEST_MODIFICATION':
        await this.validateModificationRequest(application, user);
        break;
      case 'APPROVE':
        await this.validateApprovalTransition(application, user);
        break;
      case 'UPDATE_STATUS':
        await this.validateStatusUpdate(application, user);
        break;
    }
  }

  private async validateReviewTransition(application: any, user: any) {
    if (!user.isActive) {
      throw new ForbiddenException('User is inactive and cannot perform reviews');
    }
    // Validate based on current status and user role
    const validTransitions = {
      [ApplicationStatus.SUBMITTED]: [UserRole.PARTNER_COORDINATOR],
      [ApplicationStatus.IN_TECHNICAL_REVIEW]: [UserRole.TECHNICAL_EXPERT],
      [ApplicationStatus.IN_LEGAL_REVIEW]: [UserRole.LEGAL_OFFICER],
    };

    const allowedRoles = validTransitions[application.status];
    if (!allowedRoles?.includes(user.reviewerRole)) {
      throw new ForbiddenException(
        `Reviews for applications in ${application.status} status can only be made by ${allowedRoles.join(', ')}`
      );
    }

    // Additional validations for technical experts
    if (user.reviewerRole === UserRole.TECHNICAL_EXPERT) {
      const isAssigned = application.technicalAssignments.some(
        (assignment: any) => assignment.assignedById === user.id && assignment.isActive
      );
      if (!isAssigned) {
        throw new ForbiddenException('You are not assigned to review this application');
      }
    }
  }

  private async validateExpertAssignment(application: any, user: any) {
    if (user.reviewerRole !== UserRole.PARTNER_COORDINATOR) {
      throw new ForbiddenException('Only Partner Coordinators can assign technical experts');
    }

    if (![ApplicationStatus.SUBMITTED, ApplicationStatus.IN_TECHNICAL_REVIEW].includes(application.status)) {
      throw new BadRequestException('Application is not in a state where technical experts can be assigned');
    }
  }

  private async validateModificationRequest(application: any, user: any) {
    const validRoles = [
      UserRole.PARTNER_COORDINATOR,
      UserRole.TECHNICAL_EXPERT,
      UserRole.LEGAL_OFFICER
    ];

    if (!validRoles.includes(user.reviewerRole)) {
      throw new ForbiddenException('You are not authorized to request modifications');
    }

    // Technical experts can only request modifications for assigned applications
    if (user.reviewerRole === UserRole.TECHNICAL_EXPERT) {
      const isAssigned = application.technicalAssignments.some(
        (assignment: any) => assignment.assignedById === user.id && assignment.isActive
      );
      if (!isAssigned) {
        throw new ForbiddenException('You are not assigned to this application');
      }
    }
  }

  private async validateApprovalTransition(application: any, user: any) {
    if (!user.isActive) {
      throw new ForbiddenException('User is inactive and cannot perform approvals');
    }
    // Define valid approval sequence
    const approvalSequence = [
      UserRole.PARTNER_COORDINATOR,
      UserRole.TECHNICAL_EXPERT,
      UserRole.LEGAL_OFFICER,
      UserRole.HEAD_OF_DEPARTMENT,
      UserRole.PermanentSecretary,
      UserRole.MINISTER
    ];

    // Check if application is ready for approval
    if (application.status !== ApplicationStatus.IN_REVIEW) {
      throw new BadRequestException('Application is not ready for approval');
    }

    // Get the current approval stage
    const currentApprovals = application.approvalDecisions;
    const nextApproverRole = this.getNextApproverRole(currentApprovals, approvalSequence);

    if (user.approverRole !== nextApproverRole) {
      throw new ForbiddenException(
        `This application needs approval from ${nextApproverRole}. Your role is ${user.approverRole}`
      );
    }
  }

  private async validateStatusUpdate(application: any, user: any) {
    if (user.reviewerRole !== UserRole.PARTNER_COORDINATOR) {
      throw new ForbiddenException('Only Partner Coordinators can update application status');
    }

    // Add any specific status update validation rules here
  }

  private getNextApproverRole(currentApprovals: any[], approvalSequence: UserRole[]): UserRole {
    const approvedRoles = currentApprovals.map(approval => approval.approverRole);

    for (const role of approvalSequence) {
      if (!approvedRoles.includes(role)) {
        return role;
      }
    }

    return null; // All approvals completed
  }
}