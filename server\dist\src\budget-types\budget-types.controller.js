"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BudgetTypesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const budget_types_service_1 = require("./budget-types.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
const public_decorator_1 = require("../auth/decorator/public.decorator");
let BudgetTypesController = class BudgetTypesController {
    constructor(budgetTypesService) {
        this.budgetTypesService = budgetTypesService;
    }
    async create(createBudgetTypeDto, req) {
        return this.budgetTypesService.create(createBudgetTypeDto, req.user.sub);
    }
    async findAll() {
        return this.budgetTypesService.findAll();
    }
    async findOne(id) {
        return this.budgetTypesService.findOne(id);
    }
    async update(id, updateBudgetTypeDto, req) {
        return this.budgetTypesService.update(id, updateBudgetTypeDto, req.user.sub);
    }
    async remove(id, req) {
        return this.budgetTypesService.remove(id, req.user.sub);
    }
};
exports.BudgetTypesController = BudgetTypesController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new budget type (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Budget type created successfully', type: dto_1.BudgetTypeResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Budget type with name already exists' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateBudgetTypeDto, Object]),
    __metadata("design:returntype", Promise)
], BudgetTypesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all budget types (Public access)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of budget types', type: [dto_1.BudgetTypeResponseDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], BudgetTypesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get budget type by ID (Public access)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Budget type details', type: dto_1.BudgetTypeResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Budget type not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], BudgetTypesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update budget type (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Budget type updated successfully', type: dto_1.BudgetTypeResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Budget type not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Budget type with name already exists' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateBudgetTypeDto, Object]),
    __metadata("design:returntype", Promise)
], BudgetTypesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Delete budget type (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Budget type deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Budget type not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Cannot delete budget type that is being used' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], BudgetTypesController.prototype, "remove", null);
exports.BudgetTypesController = BudgetTypesController = __decorate([
    (0, swagger_1.ApiTags)('budget-types'),
    (0, common_1.Controller)('budget-types'),
    __metadata("design:paramtypes", [budget_types_service_1.BudgetTypesService])
], BudgetTypesController);
//# sourceMappingURL=budget-types.controller.js.map